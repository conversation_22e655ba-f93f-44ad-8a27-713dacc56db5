{"accuracy": 0.96, "correct_count": 384, "total_count": 400}
{"id": "simple_13", "model_name": "Qwen_Qwen2.5-14B-Instruct", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'interval'. Expected outer type array with inner type <class 'float'>. Parameter value: [1, 3]."], "error_type": "type_error:nested", "prompt": {"id": "simple_13", "question": [[{"role": "user", "content": "Calculate the area under the curve y=x^2 from x=1 to x=3."}]], "function": [{"name": "calculate_area_under_curve", "description": "Calculate the area under a mathematical function within a given interval.", "parameters": {"type": "dict", "properties": {"function": {"type": "string", "description": "The mathematical function as a string."}, "interval": {"type": "array", "items": {"type": "float"}, "description": "An array that defines the interval to calculate the area under the curve from the start to the end point."}, "method": {"type": "string", "description": "The numerical method to approximate the area under the curve. The default value is 'trapezoidal'."}}, "required": ["function", "interval"]}}]}, "model_result_raw": "[calculate_area_under_curve(function=\"x**2\", interval=[1, 3])]", "model_result_decoded": [{"calculate_area_under_curve": {"function": "x**2", "interval": [1, 3]}}], "possible_answer": [{"calculate_area_under_curve": {"function": ["x**2", "lambda x: x**2", "y=x**2"], "interval": [[1.0, 3.0]], "method": ["", "trapezoidal"]}}]}
{"id": "simple_80", "model_name": "Qwen_Qwen2.5-14B-Instruct", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'location': 'Manhattan, City'. Expected one of ['Manhattan, New York City', 'Manhattan', 'Manhattan, New York', 'Manhattan, NY', 'Manhattan, NYC']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_80", "question": [[{"role": "user", "content": "I want to find 5 restaurants nearby my location, Manhattan, offering Thai food and a vegan menu."}]], "function": [{"name": "find_restaurants", "description": "Locate nearby restaurants based on location and food preferences.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The specific location or area. The location should be in the format of District, City."}, "food_type": {"type": "string", "description": "The type of food preferred."}, "number": {"type": "integer", "description": "Number of results to return."}, "dietary_requirements": {"type": "array", "items": {"type": "string"}, "description": "Special dietary requirements, e.g. vegan, gluten-free. Default is empty list."}}, "required": ["location", "food_type", "number"]}}]}, "model_result_raw": "[find_restaurants(location=\"Manhattan, City\", food_type=\"Thai\", number=5, dietary_requirements=['vegan'])]", "model_result_decoded": [{"find_restaurants": {"location": "Manhattan, City", "food_type": "Thai", "number": 5, "dietary_requirements": ["vegan"]}}], "possible_answer": [{"find_restaurants": {"location": ["Manhattan, New York City", "Manhattan", "Manhattan, New York", "Manhattan, NY", "Manhattan, NYC"], "food_type": ["Thai"], "number": [5], "dietary_requirements": [["vegan"], ["Vegan"]]}}]}
{"id": "simple_87", "model_name": "Qwen_Qwen2.5-14B-Instruct", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'list'. Expected outer type array with inner type <class 'float'>. Parameter value: [5, 3, 4, 1, 2]."], "error_type": "type_error:nested", "prompt": {"id": "simple_87", "question": [[{"role": "user", "content": "Sort the list [5, 3, 4, 1, 2] in ascending order."}]], "function": [{"name": "array_sort", "description": "Sorts a given list in ascending or descending order.", "parameters": {"type": "dict", "properties": {"list": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to be sorted."}, "order": {"type": "string", "enum": ["ascending", "descending"], "description": "Order of sorting."}}, "required": ["list", "order"]}}]}, "model_result_raw": "[array_sort(list=[5, 3, 4, 1, 2], order=\"ascending\")]", "model_result_decoded": [{"array_sort": {"list": [5, 3, 4, 1, 2], "order": "ascending"}}], "possible_answer": [{"array_sort": {"list": [[5.0, 3.0, 4.0, 1.0, 2.0]], "order": ["ascending"]}}]}
{"id": "simple_98", "model_name": "Qwen_Qwen2.5-14B-Instruct", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'hours': 18. Expected one of [6]."], "error_type": "value_error:others", "prompt": {"id": "simple_98", "question": [[{"role": "user", "content": "What will be the angle between the hour and minute hands of a clock at 6:30 PM?"}]], "function": [{"name": "calculate_clock_angle", "description": "Calculate the angle between the hour and minute hands of a clock at a given time.", "parameters": {"type": "dict", "properties": {"hours": {"type": "integer", "description": "The hour on the clock face."}, "minutes": {"type": "integer", "description": "The minutes on the clock face."}, "round_to": {"type": "integer", "description": "The number of decimal places to round the result to, default is 2."}}, "required": ["hours", "minutes"]}}]}, "model_result_raw": "[calculate_clock_angle(hours=18, minutes=30)]", "model_result_decoded": [{"calculate_clock_angle": {"hours": 18, "minutes": 30}}], "possible_answer": [{"calculate_clock_angle": {"hours": [6], "minutes": [30], "round_to": ["", 2]}}]}
{"id": "simple_116", "model_name": "Qwen_Qwen2.5-14B-Instruct", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'round': 3. Expected one of [2, '']."], "error_type": "value_error:others", "prompt": {"id": "simple_116", "question": [[{"role": "user", "content": "What's the probability of drawing a king from a well shuffled standard deck of 52 cards?"}]], "function": [{"name": "probabilities.calculate_single", "description": "Calculate the probability of an event.", "parameters": {"type": "dict", "properties": {"total_outcomes": {"type": "integer", "description": "The total number of outcomes."}, "event_outcomes": {"type": "integer", "description": "The number of outcomes where the event occurs."}, "round": {"type": "integer", "description": "Round the answer to a specified number of decimal places. Defaults to 2."}}, "required": ["total_outcomes", "event_outcomes"]}}]}, "model_result_raw": "[probabilities.calculate_single(total_outcomes=52, event_outcomes=4, round=3)]", "model_result_decoded": [{"probabilities.calculate_single": {"total_outcomes": 52, "event_outcomes": 4, "round": 3}}], "possible_answer": [{"probabilities.calculate_single": {"total_outcomes": [52], "event_outcomes": [4], "round": [2, ""]}}]}
{"id": "simple_202", "model_name": "Qwen_Qwen2.5-14B-Instruct", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'energy_type': 'solar'. Expected one of ['renewable']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_202", "question": [[{"role": "user", "content": "How many greenhouse gas emissions would I save if I switched to renewable energy sources for 3 months in California?"}]], "function": [{"name": "calculate_emission_savings", "description": "Calculate potential greenhouse gas emissions saved by switching to renewable energy sources.", "parameters": {"type": "dict", "properties": {"energy_type": {"type": "string", "description": "Type of the renewable energy source."}, "usage_duration": {"type": "integer", "description": "Usage duration in months."}, "region": {"type": "string", "description": "The region where you use energy. Default is 'Texas'."}}, "required": ["energy_type", "usage_duration"]}}]}, "model_result_raw": "[calculate_emission_savings(energy_type=\"solar\", usage_duration=3, region=\"California\")]", "model_result_decoded": [{"calculate_emission_savings": {"energy_type": "solar", "usage_duration": 3, "region": "California"}}], "possible_answer": [{"calculate_emission_savings": {"energy_type": ["renewable"], "usage_duration": [3], "region": ["California", "CA"]}}]}
{"id": "simple_225", "model_name": "Qwen_Qwen2.5-14B-Instruct", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'option_one': 'digital books'. Expected one of ['digital reading', 'digital']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_225", "question": [[{"role": "user", "content": "What is the percentage of population preferring digital reading over physical books?"}]], "function": [{"name": "psych_research.get_preference", "description": "Gathers research data on public preference between two options, based on societal category.", "parameters": {"type": "dict", "properties": {"category": {"type": "string", "description": "The societal category the preference data is about. E.g. reading, transportation, food"}, "option_one": {"type": "string", "description": "The first option people could prefer."}, "option_two": {"type": "string", "description": "The second option people could prefer."}, "demographic": {"type": "string", "description": "Specific demographic of society to narrow down the research.", "default": "all"}}, "required": ["category", "option_one", "option_two"]}}]}, "model_result_raw": "[psych_research.get_preference(category=\"reading\", option_one=\"digital books\", option_two=\"physical books\")]", "model_result_decoded": [{"psych_research.get_preference": {"category": "reading", "option_one": "digital books", "option_two": "physical books"}}], "possible_answer": [{"psych_research.get_preference": {"category": ["reading"], "option_one": ["digital reading", "digital"], "option_two": ["physical book", "physical", "physical books"], "demographic": ["", "all"]}}]}
{"id": "simple_234", "model_name": "Qwen_Qwen2.5-14B-Instruct", "test_category": "simple", "valid": false, "error": ["Wrong number of functions."], "error_type": "simple_function_checker:wrong_count", "prompt": {"id": "simple_234", "question": [[{"role": "user", "content": "Find important Wars in European history during the 19th century."}]], "function": [{"name": "history_eu.fetch_events", "description": "Fetches significant historical events within a specific time period in European history.", "parameters": {"type": "dict", "properties": {"century": {"type": "integer", "description": "The century you are interested in."}, "region": {"type": "string", "description": "The region of Europe you are interested in.", "enum": ["Northern", "Southern", "Eastern", "Western"]}, "category": {"type": "string", "description": "Category of the historical events. Default is 'Culture'.", "enum": ["Wars", "Culture", "Politics", "Scientific", "Others"]}}, "required": ["century", "region"]}}]}, "model_result_raw": "[history_eu.fetch_events(century=19, category=\"Wars\", region=\"Western\"), history_eu.fetch_events(century=19, category=\"Wars\", region=\"Northern\"), history_eu.fetch_events(century=19, category=\"Wars\", region=\"Southern\"), history_eu.fetch_events(century=19, category=\"Wars\", region=\"Eastern\")]", "model_result_decoded": [{"history_eu.fetch_events": {"century": 19, "category": "Wars", "region": "Western"}}, {"history_eu.fetch_events": {"century": 19, "category": "Wars", "region": "Northern"}}, {"history_eu.fetch_events": {"century": 19, "category": "Wars", "region": "Southern"}}, {"history_eu.fetch_events": {"century": 19, "category": "Wars", "region": "Eastern"}}], "possible_answer": [{"history_eu.fetch_events": {"century": [19], "region": ["Northern", "Southern", "Eastern", "Western"], "category": ["Wars"]}}]}
{"id": "simple_244", "model_name": "Qwen_Qwen2.5-14B-Instruct", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'work_title': 'Philosophi\u00e6 Naturalis Principia Mathematica'. Expected one of ['Law of Universal Gravitation', 'Universal Law of Gravitation', 'The law of universal gravitation']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_244", "question": [[{"role": "user", "content": "What year was the law of universal gravitation published by Isaac Newton?"}]], "function": [{"name": "publication_year.find", "description": "Fetches the year a particular scientific work was published.", "parameters": {"type": "dict", "properties": {"author": {"type": "string", "description": "Name of the author of the work."}, "work_title": {"type": "string", "description": "Title of the scientific work."}, "location": {"type": "string", "description": "Place of the publication, if known. Default to 'all'."}}, "required": ["author", "work_title"]}}]}, "model_result_raw": "[publication_year.find(author=\"Isaac Newton\", work_title=\"Philosophi\u00e6 Naturalis Principia Mathematica\")]", "model_result_decoded": [{"publication_year.find": {"author": "Isaac Newton", "work_title": "Philosophi\u00e6 Naturalis Principia Mathematica"}}], "possible_answer": [{"publication_year.find": {"author": ["Isaac Newton"], "work_title": ["Law of Universal Gravitation", "Universal Law of Gravitation", "The law of universal gravitation"], "location": ["", "all"]}}]}
{"id": "simple_266", "model_name": "Qwen_Qwen2.5-14B-Instruct", "test_category": "simple", "valid": false, "error": ["Invalid syntax. Failed to decode AST. unterminated string literal (detected at line 1) (<unknown>, line 1)"], "error_type": "ast_decoder:decoder_failed", "prompt": {"id": "simple_266", "question": [[{"role": "user", "content": "What is the value of the sculpture 'The Thinker' by Rodin?"}]], "function": [{"name": "get_sculpture_value", "description": "Retrieve the current market value of a particular sculpture by a specific artist.", "parameters": {"type": "dict", "properties": {"sculpture": {"type": "string", "description": "The name of the sculpture."}, "artist": {"type": "string", "description": "The name of the artist who created the sculpture."}}, "required": ["sculpture", "artist"]}}]}, "model_result_raw": "[get_sculpture_value(sculpture=\"The Thinker\", artist=\"Rodin)]", "possible_answer": [{"get_sculpture_value": {"sculpture": ["The Thinker"], "artist": ["Rodin"]}}]}
{"id": "simple_267", "model_name": "Qwen_Qwen2.5-14B-Instruct", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'location': 'New York'. Expected one of ['New York City, NY']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_267", "question": [[{"role": "user", "content": "Find the top rated modern sculpture exhibition happening in New York in the upcoming month."}]], "function": [{"name": "find_exhibition", "description": "Locate the most popular exhibitions based on criteria like location, time, art form, and user ratings.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state where the exhibition is held, e.g., New York City, NY."}, "art_form": {"type": "string", "description": "The form of art the exhibition is displaying e.g., sculpture."}, "month": {"type": "string", "description": "The month of exhibition. Default value will return upcoming events if not specified."}, "user_ratings": {"type": "string", "enum": ["low", "average", "high"], "description": "Select exhibitions with user rating threshold. Default is 'low'"}}, "required": ["location", "art_form"]}}]}, "model_result_raw": "[find_exhibition(location=\"New York\", art_form=\"sculpture\", month=\"upcoming\", user_ratings=\"high\")]", "model_result_decoded": [{"find_exhibition": {"location": "New York", "art_form": "sculpture", "month": "upcoming", "user_ratings": "high"}}], "possible_answer": [{"find_exhibition": {"location": ["New York City, NY"], "art_form": ["sculpture", "modern sculpture"], "month": [""], "user_ratings": ["high"]}}]}
{"id": "simple_355", "model_name": "Qwen_Qwen2.5-14B-Instruct", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'recipe': 'Beef Lasagna Recipe'. Expected one of ['Beef Lasagna']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_355", "question": [[{"role": "user", "content": "How many calories in the Beef Lasagna Recipe from Foodnetwork.com?"}]], "function": [{"name": "recipe_info.get_calories", "description": "Retrieve the amount of calories from a specific recipe in a food website.", "parameters": {"type": "dict", "properties": {"website": {"type": "string", "description": "The food website that has the recipe."}, "recipe": {"type": "string", "description": "Name of the recipe."}, "optional_meal_time": {"type": "string", "description": "Specific meal time of the day for the recipe (optional, could be 'Breakfast', 'Lunch', 'Dinner'). Default is all if not specified."}}, "required": ["website", "recipe"]}}]}, "model_result_raw": "[recipe_info.get_calories(website=\"Foodnetwork.com\", recipe=\"Beef Lasagna Recipe\")]", "model_result_decoded": [{"recipe_info.get_calories": {"website": "Foodnetwork.com", "recipe": "Beef Lasagna Recipe"}}], "possible_answer": [{"recipe_info.get_calories": {"website": ["Foodnetwork.com"], "recipe": ["Beef Lasagna"], "optional_meal_time": [""]}}]}
{"id": "simple_370", "model_name": "Qwen_Qwen2.5-14B-Instruct", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'quantity': [3, 5]. Expected one of [[3, 1]]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_370", "question": [[{"role": "user", "content": "Order three bottles of olive oil and a five pound bag of rice from Safeway in Palo Alto."}]], "function": [{"name": "safeway.order", "description": "Order specified items from a Safeway location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location of the Safeway store, e.g. Palo Alto, CA."}, "items": {"type": "array", "items": {"type": "string"}, "description": "List of items to order."}, "quantity": {"type": "array", "items": {"type": "integer"}, "description": "Quantity of each item in the order list."}}, "required": ["location", "items", "quantity"]}}]}, "model_result_raw": "[safeway.order(location=\"Palo Alto, CA\", items=['olive oil', 'rice'], quantity=[3, 5])]", "model_result_decoded": [{"safeway.order": {"location": "Palo Alto, CA", "items": ["olive oil", "rice"], "quantity": [3, 5]}}], "possible_answer": [{"safeway.order": {"location": ["Palo Alto", "Palo Alto, CA"], "items": [["olive oil", "rice"], ["olive oil", "bag of rice"]], "quantity": [[3, 1]]}}]}
{"id": "simple_373", "model_name": "Qwen_Qwen2.5-14B-Instruct", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'pack_size'. Expected outer type array with inner type <class 'int'>. Parameter value: [None, None, 12]."], "error_type": "type_error:nested", "prompt": {"id": "simple_373", "question": [[{"role": "user", "content": "I want to buy apples, rice, and 12 pack of bottled water from a Walmart near San Jose. Show me the product information and stock availability."}]], "function": [{"name": "walmart.purchase", "description": "Retrieve information of items from Walmart including stock availability.", "parameters": {"type": "dict", "properties": {"loc": {"type": "string", "description": "Location of the nearest Walmart."}, "product_list": {"type": "array", "items": {"type": "string"}, "description": "Items to be purchased listed in an array."}, "pack_size": {"type": "array", "items": {"type": "integer"}, "description": "Size of the product pack if applicable. The size of the array should be equal to product_list. Default is not use it if not specified."}}, "required": ["loc", "product_list"]}}]}, "model_result_raw": "[walmart.purchase(loc=\"San Jose\", product_list=['apples', 'rice', 'bottled water'], pack_size=[None, None, 12])]", "model_result_decoded": [{"walmart.purchase": {"loc": "San Jose", "product_list": ["apples", "rice", "bottled water"], "pack_size": [null, null, 12]}}], "possible_answer": [{"walmart.purchase": {"loc": ["San Jose", "San Jose, CA"], "product_list": [["apples", "rice", "bottled water"], ["apples", "rice", "water"]], "pack_size": [[1, 1, 12]]}}]}
{"id": "simple_375", "model_name": "Qwen_Qwen2.5-14B-Instruct", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'items': ['pumpkin', 'eggs']. Expected one of [['pumpkins', 'eggs'], ['pumpkin', 'dozen eggs']]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_375", "question": [[{"role": "user", "content": "Check the total price for three pumpkins and two dozen eggs at Walmart."}]], "function": [{"name": "walmart.check_price", "description": "Calculate total price for given items and their quantities at Walmart.", "parameters": {"type": "dict", "properties": {"items": {"type": "array", "items": {"type": "string"}, "description": "List of items to be priced."}, "quantities": {"type": "array", "items": {"type": "integer"}, "description": "Quantity of each item corresponding to the items list."}, "store_location": {"type": "string", "description": "The store location for specific pricing (optional). Default to all if not specified."}}, "required": ["items", "quantities"]}}]}, "model_result_raw": "[walmart.check_price(items=['pumpkin', 'eggs'], quantities=[3, 24])]", "model_result_decoded": [{"walmart.check_price": {"items": ["pumpkin", "eggs"], "quantities": [3, 24]}}], "possible_answer": [{"walmart.check_price": {"items": [["pumpkins", "eggs"], ["pumpkin", "dozen eggs"]], "quantities": [[3, 24], [3, 2]], "store_location": [""]}}]}
{"id": "simple_376", "model_name": "Qwen_Qwen2.5-14B-Instruct", "test_category": "simple", "valid": false, "error": ["Optional parameter 'display_format' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_376", "question": [[{"role": "user", "content": "What time is it currently in London, UK in 24 hour format?"}]], "function": [{"name": "time_zone_converter", "description": "Retrieve the current time of a specific city.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city you want to know the current time for."}, "country": {"type": "string", "description": "The country where the city is located."}, "display_format": {"type": "string", "description": "The time display format: '12h' or '24h'. Default is '24h'."}}, "required": ["city", "country"]}}]}, "model_result_raw": "[time_zone_converter(city=\"London\", country=\"UK\")]", "model_result_decoded": [{"time_zone_converter": {"city": "London", "country": "UK"}}], "possible_answer": [{"time_zone_converter": {"city": ["London"], "country": ["UK", "United Kingdom"], "display_format": ["24h", "24 hour"]}}]}