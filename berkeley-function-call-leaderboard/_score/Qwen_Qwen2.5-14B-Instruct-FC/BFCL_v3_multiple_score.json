{"accuracy": 0.955, "correct_count": 191, "total_count": 200}
{"id": "multiple_7", "model_name": "Qwen_Qwen2.5-14B-Instruct-FC", "test_category": "multiple", "valid": false, "error": ["Wrong number of functions."], "error_type": "multiple_function_checker:wrong_count", "prompt": {"id": "multiple_7", "question": [[{"role": "user", "content": "How to assess the population growth in deer and their impact on woodland in Washington state over the past decade?"}]], "function": [{"name": "wildlife_population.assess_growth", "description": "Assesses the population growth of a specific species in a specified location over a period.", "parameters": {"type": "dict", "properties": {"species": {"type": "string", "description": "The species for which the growth is to be calculated."}, "location": {"type": "string", "description": "The area where the species is present."}, "duration": {"type": "integer", "description": "The time period for which the population growth should be calculated in years."}}, "required": ["species", "location", "duration"]}}, {"name": "ecological_impact.analyze", "description": "Analyzes the impact of a species on a particular ecosystem.", "parameters": {"type": "dict", "properties": {"species": {"type": "string", "description": "The species whose impact is to be calculated."}, "ecosystem": {"type": "string", "description": "The ecosystem being affected."}, "location": {"type": "string", "description": "The area where the impact is analyzed."}, "timeframe": {"type": "integer", "description": "The time period for which the impact analysis should be carried out in years.", "default": 5}}, "required": ["species", "ecosystem", "location"]}}]}, "model_result_raw": [{"wildlife_population.assess_growth": {"species": "deer", "location": "Washington state", "duration": 10}}, {"ecological_impact.analyze": {"species": "deer", "ecosystem": "woodland", "location": "Washington state", "timeframe": 10}}], "model_result_decoded": [{"wildlife_population.assess_growth": {"species": "deer", "location": "Washington state", "duration": 10}}, {"ecological_impact.analyze": {"species": "deer", "ecosystem": "woodland", "location": "Washington state", "timeframe": 10}}], "possible_answer": [{"wildlife_population.assess_growth": {"species": ["deer", "Deer"], "location": ["Washington state", "WA", "Washington"], "duration": [10]}}]}
{"id": "multiple_10", "model_name": "Qwen_Qwen2.5-14B-Instruct-FC", "test_category": "multiple", "valid": false, "error": ["Wrong number of functions."], "error_type": "multiple_function_checker:wrong_count", "prompt": {"id": "multiple_10", "question": [[{"role": "user", "content": "I need to delete some columns from my employees database on personal_data table. I want to remove their email addresses and social security numbers to respect privacy."}]], "function": [{"name": "database.modify_columns", "description": "This function allows deletion or addition of columns in a database", "parameters": {"type": "dict", "properties": {"db_name": {"type": "string", "description": "The name of the database to modify."}, "table": {"type": "string", "description": "The name of the table to modify."}, "operation": {"type": "string", "description": "The operation to carry out on the table. Can be 'delete' or 'add'."}, "columns": {"type": "array", "description": "List of the columns to add or delete from the table.", "items": {"type": "string"}}}, "required": ["db_name", "table", "operation", "columns"]}}, {"name": "database.create_backup", "description": "This function creates a backup of the database before modification", "parameters": {"type": "dict", "properties": {"db_name": {"type": "string", "description": "The name of the database to create a backup of."}, "backup_location": {"type": "string", "description": "The file path where the backup should be stored."}, "timestamp": {"type": "boolean", "description": "Option to append a timestamp to the backup file name.", "default": "False"}}, "required": ["db_name", "backup_location"]}}]}, "model_result_raw": [{"database.create_backup": {"db_name": "employees", "backup_location": "/backups/employees_db_backup.sql", "timestamp": true}}, {"database.modify_columns": {"db_name": "employees", "table": "personal_data", "operation": "delete", "columns": ["email", "social_security_number"]}}], "model_result_decoded": [{"database.create_backup": {"db_name": "employees", "backup_location": "/backups/employees_db_backup.sql", "timestamp": true}}, {"database.modify_columns": {"db_name": "employees", "table": "personal_data", "operation": "delete", "columns": ["email", "social_security_number"]}}], "possible_answer": [{"database.modify_columns": {"db_name": ["employees"], "table": ["personal_data"], "operation": ["delete"], "columns": [["email", "ssn"], ["ssn", "email"], ["email", "social_security_number"], ["social_security_number", "email"], ["email", "social security number"], ["social security number", "email"]]}}]}
{"id": "multiple_53", "model_name": "Qwen_Qwen2.5-14B-Instruct-FC", "test_category": "multiple", "valid": false, "error": ["Function name 'linear_regression' not found in model output."], "error_type": "simple_function_checker:wrong_func_name", "prompt": {"id": "multiple_53", "question": [[{"role": "user", "content": "Predict the house prices for next 5 years based on interest rates and unemployment rates."}]], "function": [{"name": "linear_regression", "description": "Applies linear regression to a given set of independent variables to make a prediction.", "parameters": {"type": "dict", "properties": {"independent_var": {"type": "array", "items": {"type": "string"}, "description": "The independent variables."}, "dependent_var": {"type": "string", "description": "The dependent variable."}, "forecast_period": {"type": "integer", "description": "The number of years to forecast the prices. Default 1", "optional": true}}, "required": ["independent_var", "dependent_var"]}}, {"name": "random_forest_regression", "description": "Applies Random Forest Regression to a given set of independent variables to make a prediction.", "parameters": {"type": "dict", "properties": {"independent_var": {"type": "array", "items": {"type": "string"}, "description": "The independent variables."}, "dependent_var": {"type": "string", "description": "The dependent variable."}, "n_estimators": {"type": "integer", "description": "The number of trees in the forest. Default 1", "optional": true}, "forecast_period": {"type": "integer", "description": "The number of years to forecast the prices. Default 1", "optional": true}}, "required": ["independent_var", "dependent_var"]}}]}, "model_result_raw": [{"random_forest_regression": {"independent_var": ["interest_rates", "unemployment_rates"], "dependent_var": "house_prices", "n_estimators": 100, "forecast_period": 5}}], "model_result_decoded": [{"random_forest_regression": {"independent_var": ["interest_rates", "unemployment_rates"], "dependent_var": "house_prices", "n_estimators": 100, "forecast_period": 5}}], "possible_answer": [{"linear_regression": {"independent_var": [["interest rates", "unemployment rates"], ["interest_rate", "unemployment_rate"], ["interest rate", "unemployment rate"]], "dependent_var": ["house_price", "house price"], "forecast_period": [5]}}]}
{"id": "multiple_58", "model_name": "Qwen_Qwen2.5-14B-Instruct-FC", "test_category": "multiple", "valid": false, "error": ["Invalid value for parameter 'expertise': 'Marriage'. Expected one of ['Divorce']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "multiple_58", "question": [[{"role": "user", "content": "Search for divorce law specialists in Los Angeles"}]], "function": [{"name": "doctor.search", "description": "Search for a doctor based on area of expertise and location", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Los Angeles, CA"}, "specialization": {"type": "string", "description": "Medical specialization. For example, 'Cardiology', 'Orthopedics', 'Gynecology'."}}, "required": ["location", "specialization"]}}, {"name": "lawyer.search", "description": "Search for a lawyer based on area of expertise and location", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Los Angeles, CA"}, "expertise": {"type": "string", "description": "Area of legal expertise. For example, 'Marriage', 'Criminal', 'Business'."}}, "required": ["location", "expertise"]}}]}, "model_result_raw": [{"lawyer.search": {"location": "Los Angeles, CA", "expertise": "Marriage"}}], "model_result_decoded": [{"lawyer.search": {"location": "Los Angeles, CA", "expertise": "Marriage"}}], "possible_answer": [{"lawyer.search": {"location": ["Los Angeles", "Los Angeles, CA", "LA"], "expertise": ["Divorce"]}}]}
{"id": "multiple_67", "model_name": "Qwen_Qwen2.5-14B-Instruct-FC", "test_category": "multiple", "valid": false, "error": ["Invalid value for parameter 'source_language': 'en'. Expected one of ['English']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "multiple_67", "question": [[{"role": "user", "content": "Translate Hello, how are you? from English to French."}]], "function": [{"name": "translate", "description": "Translate text from a specified source language to a specified target language.", "parameters": {"type": "dict", "properties": {"text": {"type": "string", "description": "The text to be translated."}, "source_language": {"type": "string", "description": "The language the text is currently in."}, "target_language": {"type": "string", "description": "The language the text will be translated to."}}, "required": ["text", "source_language", "target_language"]}}, {"name": "sentiment_analysis", "description": "Analyze the sentiment of a specified text.", "parameters": {"type": "dict", "properties": {"text": {"type": "string", "description": "The text whose sentiment is to be analyzed."}}, "required": ["text"]}}, {"name": "word_count", "description": "Count the number of words in the given text.", "parameters": {"type": "dict", "properties": {"text": {"type": "string", "description": "The text that the number of words is to be calculated."}}, "required": ["text"]}}]}, "model_result_raw": [{"translate": {"text": "Hello, how are you?", "source_language": "en", "target_language": "fr"}}], "model_result_decoded": [{"translate": {"text": "Hello, how are you?", "source_language": "en", "target_language": "fr"}}], "possible_answer": [{"translate": {"text": ["Hello, how are you?"], "source_language": ["English"], "target_language": ["French"]}}]}
{"id": "multiple_91", "model_name": "Qwen_Qwen2.5-14B-Instruct-FC", "test_category": "multiple", "valid": false, "error": ["Invalid value for parameter 'ingredientLimit': 5. Expected one of [4]."], "error_type": "value_error:others", "prompt": {"id": "multiple_91", "question": [[{"role": "user", "content": "Can I find a good cooking recipe for apple pie using less than 5 ingredients?"}]], "function": [{"name": "restaurant.find", "description": "Locate restaurants based on specific criteria such as cuisine and price range", "parameters": {"type": "dict", "properties": {"cuisine": {"type": "string", "description": "The type of cuisine preferred."}, "price": {"type": "array", "items": {"type": "string"}, "description": "Price range of the restaurant in format ['low', 'mid', 'high']. Default ['low', 'mid', 'high']"}}, "required": ["cuisine"]}}, {"name": "recipe.find", "description": "Locate cooking recipes based on specific criteria such as main ingredient and number of ingredients", "parameters": {"type": "dict", "properties": {"mainIngredient": {"type": "string", "description": "Main ingredient for the recipe."}, "ingredientLimit": {"type": "integer", "description": "Max number of ingredients the recipe should use."}}, "required": ["mainIngredient", "ingredientLimit"]}}]}, "model_result_raw": [{"recipe.find": {"mainIngredient": "apple", "ingredientLimit": 5}}], "model_result_decoded": [{"recipe.find": {"mainIngredient": "apple", "ingredientLimit": 5}}], "possible_answer": [{"recipe.find": {"mainIngredient": ["apple pie", "apple"], "ingredientLimit": [4]}}]}
{"id": "multiple_149", "model_name": "Qwen_Qwen2.5-14B-Instruct-FC", "test_category": "multiple", "valid": false, "error": ["Invalid value for parameter 'language': 'en'. Expected one of ['english', 'English']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "multiple_149", "question": [[{"role": "user", "content": "Analyze the sentiment of a customer review 'I love the food here! It's always fresh and delicious.'."}]], "function": [{"name": "hotel_booking", "description": "Books a hotel room for a specific date range.", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "location": {"type": "string", "description": "The city and state, e.g. New York, NY."}, "start_date": {"type": "string", "description": "The start date of the reservation. Use format 'YYYY-MM-DD'."}, "end_date": {"type": "string", "description": "The end date of the reservation. Use format 'YYYY-MM-DD'."}, "rooms": {"type": "integer", "default": 1, "description": "The number of rooms to reserve."}}, "required": ["hotel_name", "location", "start_date", "end_date"]}}, {"name": "sentiment_analysis", "description": "Perform sentiment analysis on a given piece of text.", "parameters": {"type": "dict", "properties": {"text": {"type": "string", "description": "The text on which to perform sentiment analysis."}, "language": {"type": "string", "description": "The language in which the text is written."}}, "required": ["text", "language"]}}, {"name": "get_time_difference", "description": "Get the time difference between two places.", "parameters": {"type": "dict", "properties": {"place1": {"type": "string", "description": "The first place for time difference."}, "place2": {"type": "string", "description": "The second place for time difference."}}, "required": ["place1", "place2"]}}, {"name": "calculate_bmi", "description": "Calculate the Body Mass Index (BMI) for a person based on their weight and height.", "parameters": {"type": "dict", "properties": {"weight": {"type": "integer", "description": "The weight of the person in kilograms."}, "height": {"type": "integer", "description": "The height of the person in centimeters."}, "system": {"type": "string", "description": "The system of units to be used, 'metric' or 'imperial'. Default is 'metric'."}}, "required": ["weight", "height"]}}]}, "model_result_raw": [{"sentiment_analysis": {"text": "I love the food here! It's always fresh and delicious.", "language": "en"}}], "model_result_decoded": [{"sentiment_analysis": {"text": "I love the food here! It's always fresh and delicious.", "language": "en"}}], "possible_answer": [{"sentiment_analysis": {"text": ["I love the food here! It's always fresh and delicious."], "language": ["english", "English"]}}]}
{"id": "multiple_165", "model_name": "Qwen_Qwen2.5-14B-Instruct-FC", "test_category": "multiple", "valid": false, "error": ["Invalid value for parameter 'model': 'Les Paul'. Expected one of ['Gibson Les Paul']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "multiple_165", "question": [[{"role": "user", "content": "Find the price of a used Gibson Les Paul guitar in excellent condition in the Chicago area."}]], "function": [{"name": "identify_color_rgb", "description": "This function identifies the RGB values of a named color.", "parameters": {"type": "dict", "properties": {"color_name": {"type": "string", "description": "Name of the color."}, "standard": {"type": "string", "description": "The color standard (e.g. basic, pantone). Default is 'basic'"}}, "required": ["color_name"]}}, {"name": "board_game.chess.get_top_players", "description": "Find top chess players in a location based on rating.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city you want to find the players from."}, "minimum_rating": {"type": "integer", "description": "Minimum rating to filter the players."}, "number_of_players": {"type": "integer", "default": 10, "description": "Number of players you want to retrieve, default value is 10"}}, "required": ["location", "minimum_rating"]}}, {"name": "guitar_price.find", "description": "Retrieve the price of a specific used guitar model based on its condition and location.", "parameters": {"type": "dict", "properties": {"model": {"type": "string", "description": "The model of the guitar."}, "condition": {"type": "string", "enum": ["Poor", "Good", "Excellent"], "description": "The condition of the guitar."}, "location": {"type": "string", "description": "The location where the guitar is being sold."}}, "required": ["model", "condition", "location"]}}]}, "model_result_raw": [{"guitar_price.find": {"model": "Les Paul", "condition": "Excellent", "location": "Chicago"}}], "model_result_decoded": [{"guitar_price.find": {"model": "Les Paul", "condition": "Excellent", "location": "Chicago"}}], "possible_answer": [{"guitar_price.find": {"model": ["Gibson Les Paul"], "condition": ["Excellent"], "location": ["Chicago", "Chicago area"]}}]}
{"id": "multiple_186", "model_name": "Qwen_Qwen2.5-14B-Instruct-FC", "test_category": "multiple", "valid": false, "error": ["Invalid value for parameter 'recipe_type': 'brownies'. Expected one of ['dessert']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "multiple_186", "question": [[{"role": "user", "content": "Find me a vegan recipe for brownies which prep time is under 30 minutes."}]], "function": [{"name": "find_recipe", "description": "Find a recipe based on the dietary restrictions, recipe type, and time constraints.", "parameters": {"type": "dict", "properties": {"dietary_restrictions": {"type": "string", "description": "Dietary restrictions e.g. vegan, vegetarian, gluten free, dairy free."}, "recipe_type": {"type": "string", "description": "Type of the recipe. E.g. dessert, main course, breakfast."}, "time": {"type": "integer", "description": "Time limit in minutes to prep the meal."}}, "required": ["dietary_restrictions", "recipe_type", "time"]}}, {"name": "science_history.get_discovery_details", "description": "Retrieve the details of a scientific discovery based on the discovery name.", "parameters": {"type": "dict", "properties": {"discovery": {"type": "string", "description": "The name of the discovery, e.g. Gravity"}, "method_used": {"type": "string", "description": "The method used for the discovery, default value is 'default' which gives the most accepted method."}}, "required": ["discovery"]}}]}, "model_result_raw": [{"find_recipe": {"dietary_restrictions": "vegan", "recipe_type": "brownies", "time": 30}}], "model_result_decoded": [{"find_recipe": {"dietary_restrictions": "vegan", "recipe_type": "brownies", "time": 30}}], "possible_answer": [{"find_recipe": {"dietary_restrictions": ["vegan"], "recipe_type": ["dessert"], "time": [30]}}]}