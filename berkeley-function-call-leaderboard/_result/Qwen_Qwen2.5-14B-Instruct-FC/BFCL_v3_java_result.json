{"id": "java_0", "result": [{"GeometryPresentation.createPresentation": {"controller": "mapController", "parent": "mapArea"}}], "input_token_count": 318, "output_token_count": 29, "latency": 1.278554916381836}
{"id": "java_1", "result": [{"SQLCompletionAnalyzer.makeProposalsFromObject": {"object": "Customers", "useShortName": "true", "params": "{\\\"LIMIT\\\": 50, \\\"schemaFilter\\\": \\\"public\\\"}"}}], "input_token_count": 369, "output_token_count": 59, "latency": 2.9643425941467285}
{"id": "java_2", "result": [{"FireBirdUtils.getViewSourceWithHeader": {"monitor": "dbMonitor", "view": "EmployeeView", "source": "SELECT * FROM Employee WHERE status = 'active'"}}], "input_token_count": 381, "output_token_count": 47, "latency": 2.22220778465271}
{"id": "java_3", "result": [{"DB2Tablespace.resolveTablespaceReference": {"monitor": "dbMonitor", "dataSource": "db2DataSource", "reference": "USERSPACE1"}}], "input_token_count": 389, "output_token_count": 43, "latency": 1.8963711261749268}
{"id": "java_4", "result": [{"DB2ViewBaseDepCache.prepareObjectsStatement": {"session": "jdbcSession", "db2ViewBase": "DB2ViewBase{viewName='EmployeeView', schemaName='HR'}"}}], "input_token_count": 331, "output_token_count": 51, "latency": 2.5563435554504395}
{"id": "java_5", "result": [{"PlainTextPresentation.createPresentation": {"controller": "dataController", "parent": "compositeParent"}}], "input_token_count": 339, "output_token_count": 29, "latency": 1.2411274909973145}
{"id": "java_6", "result": [{"SpreadsheetPresentation.refreshData": {"refreshMetadata": "true", "append": "true", "keepState": "true"}}], "input_token_count": 356, "output_token_count": 36, "latency": 1.5444793701171875}
{"id": "java_7", "result": [{"EFSNIOResource.copy": {"destination": "/backup/data.txt", "force": "true", "monitor": "progressTracker"}}], "input_token_count": 397, "output_token_count": 39, "latency": 1.8038148880004883}
{"id": "java_8", "result": [{"EFSNIOFile.setContents": {"source": "fileStream", "force": "true", "keepHistory": "false", "monitor": "progressMonitor"}}], "input_token_count": 431, "output_token_count": 45, "latency": 2.3515753746032715}
{"id": "java_9", "result": [{"writeMultiPoint": {"multiPoint": "MultiPoint(Point(1, 2), Point(3, 4), Point(5, 6), Point(7, 8), Point(9, 10))", "xyzmMode": "XyzmMode.XYZ", "buffer": "ByteBuffer.allocate(1024)"}}], "input_token_count": 447, "output_token_count": 84, "latency": 4.573561429977417}
{"id": "java_10", "result": [{"JNIBridge.setLauncherInfo": {"launcher": "/usr/local/bin/dbeaver", "name": "DBeaverLauncher"}}], "input_token_count": 297, "output_token_count": 38, "latency": 1.765373706817627}
{"id": "java_11", "result": [{"BasePolicyDataProvider.getRegistryPolicyValue": {"root": "HKEY_LOCAL_MACHINE", "property": "EnableExtensions"}}], "input_token_count": 332, "output_token_count": 34, "latency": 1.5026414394378662}
{"id": "java_12", "result": [{"ExasolExecutionContext.setCurrentSchema": {"monitor": "progressMonitor", "schemaName": "AnalyticsDB"}}], "input_token_count": 319, "output_token_count": 32, "latency": 1.3297834396362305}
{"id": "java_13", "result": [{"AltibaseGrantee.prepareObjectsStatement": {"session": "JDBC_session", "owner": "JohnDoe"}}], "input_token_count": 328, "output_token_count": 34, "latency": 1.4856531620025635}
{"id": "java_14", "result": [{"FunGameBase.onFinish": {"layout": "gameLayout", "success": "true"}}], "input_token_count": 313, "output_token_count": 29, "latency": 1.2224974632263184}
{"id": "java_15", "result": [{"Res9patchStreamDecoder.decode": {"input": "imageInputStream", "out": "imageOutputStream"}}], "input_token_count": 332, "output_token_count": 31, "latency": 1.3244144916534424}
{"id": "java_16", "result": [{"InsnDecoder.invokePolymorphic": {"insn": "instructionData", "isRange": "true"}}], "input_token_count": 316, "output_token_count": 130, "latency": 7.576087713241577}
{"id": "java_17", "result": [{"GenericTypesVisitor.attachGenericTypesInfo": {"mth": "initMethod", "insn": "newConstructorInsn"}}], "input_token_count": 330, "output_token_count": 171, "latency": 9.574824810028076}
{"id": "java_18", "result": [{"SysRoleController.queryPageRoleCount": {"pageNo": "3", "pageSize": "20"}}], "input_token_count": 327, "output_token_count": 32, "latency": 1.616173267364502}
{"id": "java_19", "result": [{"PersonController.personal": {"model": "webModel", "request": "userRequest"}}], "input_token_count": 330, "output_token_count": 29, "latency": 1.4051713943481445}
{"id": "java_20", "result": [{"HbaseAdapter.updateConfig": {"fileName": "user-mapping.yml", "config": "newMappingConfig"}}], "input_token_count": 325, "output_token_count": 33, "latency": 1.7192957401275635}
{"id": "java_21", "result": [{"SessionHandler.exceptionCaught": {"ctx": "nettyChannelContext", "e": "ioExceptionEvent"}}], "input_token_count": 327, "output_token_count": 32, "latency": 1.89945387840271}
{"id": "java_22", "result": [{"PmsProductServiceImpl.updateNewStatus": {"ids": "[101, 202, 303]", "newStatus": "2"}}], "input_token_count": 331, "output_token_count": 43, "latency": 2.31923770904541}
{"id": "java_23", "result": [{"SmsHomeNewProductServiceImpl.list": {"productName": "LED TV", "recommendStatus": "1", "pageSize": "20", "pageNum": "3"}}], "input_token_count": 410, "output_token_count": 45, "latency": 2.376674175262451}
{"id": "java_24", "result": [{"PmsProductCategoryController.updateShowStatus": {"ids": "[101, 102, 103]", "showStatus": "0"}}], "input_token_count": 339, "output_token_count": 44, "latency": 2.340247392654419}
{"id": "java_25", "result": [{"SmsHomeRecommendSubjectController.updateSort": {"id": "42", "sort": "5"}}], "input_token_count": 306, "output_token_count": 32, "latency": 1.8811945915222168}
{"id": "java_26", "result": [{"ProxyConnection.prepareCall": {"sql": "CALL totalSales(?)", "resultSetType": "ResultSet.TYPE_SCROLL_INSENSITIVE", "concurrency": "ResultSet.CONCUR_READ_ONLY", "holdability": "ResultSet.CLOSE_CURSORS_AT_COMMIT"}}], "input_token_count": 454, "output_token_count": 62, "latency": 3.675183057785034}
{"id": "java_27", "result": [{"TwoSum.twoSum": {"nums": "[2, 7, 11, 15]", "target": "9"}}], "input_token_count": 321, "output_token_count": 38, "latency": 2.074434757232666}
{"id": "java_28", "result": [{"configStorage.dynamicCredentialsScheduledExecutorService": {"credentialsFile": "es_credentials.properties", "credentialsRefreshInterval": "30", "basicCredentials": "basicAuthCredentials"}}], "input_token_count": 373, "output_token_count": 45, "latency": 2.66914963722229}
{"id": "java_29", "result": [{"propertyTransferredToCollectorBuilder": {"property": "zipkin.collector.activemq.concurrency", "value": "10", "builderExtractor": "ActiveMQCollector.Builder::getConcurrencySetting"}}], "input_token_count": 370, "output_token_count": 52, "latency": 3.1263010501861572}
{"id": "java_30", "result": [{"RedissonAsyncCache.putIfAbsent": {"key": "answer", "value": "42"}}], "input_token_count": 345, "output_token_count": 31, "latency": 1.8136959075927734}
{"id": "java_31", "result": [{"RedissonRx.getQueue": {"name": "taskQueue", "codec": "jsonCodec"}}], "input_token_count": 300, "output_token_count": 30, "latency": 1.8154890537261963}
{"id": "java_32", "result": [{"RedissonPermitExpirableSemaphore.tryAcquireAsync": {"waitTime": "5", "leaseTime": "120", "unit": "SECONDS"}}], "input_token_count": 387, "output_token_count": 46, "latency": 2.7530195713043213}
{"id": "java_33", "result": [{"RedissonMapCache.putOperationAsync": {"key": "employee:1234", "value": "John Doe"}}], "input_token_count": 318, "output_token_count": 36, "latency": 2.2237863540649414}
{"id": "java_34", "result": [{"ServiceManager.newTimeout": {"task": "cleanupTask", "delay": "300", "unit": "TimeUnit.SECONDS"}}], "input_token_count": 364, "output_token_count": 38, "latency": 2.3437445163726807}
{"id": "java_35", "result": [{"RedissonConnection.bitOp": {"op": "BitOperation.AND", "destination": ["user:online:both"], "keys": ["user:online:today", "user:online:yesterday"]}}], "input_token_count": 416, "output_token_count": 52, "latency": 3.2978594303131104}
{"id": "java_36", "result": [{"ObjectMapEntryReplayDecoder.decode": {"parts": "['userID', 42, 'username', 'johndoe', 'isActive', true]", "state": "processingState"}}], "input_token_count": 344, "output_token_count": 50, "latency": 3.19576096534729}
{"id": "java_37", "result": [{"ConsoleAnnotator.annotate": {"context": "jenkinsBuild", "text": "buildOutput"}}], "input_token_count": 312, "output_token_count": 31, "latency": 2.****************}
{"id": "java_38", "result": [{"NestedValueFetcher.createSourceMapStub": {"filteredSource": "docFields"}}], "input_token_count": 282, "output_token_count": 26, "latency": 1.***************}
{"id": "java_39", "result": [{"NodeIdConverter.format": {"event": "logEvent", "toAppendTo": "logBuilder"}}], "input_token_count": 316, "output_token_count": 30, "latency": 1.****************}
{"id": "java_40", "result": [{"RoutingNodesChangedObserver.shardInitialized": {"unassignedShard": "shardA", "initializedShard": "shardB"}}], "input_token_count": 319, "output_token_count": 39, "latency": 2.****************}
{"id": "java_41", "result": [{"SearchHit.declareInnerHitsParseFields": {"parser": "searchHitParser"}}], "input_token_count": 258, "output_token_count": 27, "latency": 1.****************}
{"id": "java_42", "result": [{"TermQueryBuilderTests.termQuery": {"mapper": "usernameField", "value": "JohnDoe", "caseInsensitive": "true"}}], "input_token_count": 356, "output_token_count": 38, "latency": 2.***************}
{"id": "java_43", "result": [{"SecureMockMaker.createSpy": {"settings": "mockSettings", "handler": "mockHandler", "object": "testObject"}}], "input_token_count": 368, "output_token_count": 37, "latency": 2.***************}
{"id": "java_44", "result": [{"DesAPITest.init": {"crypt": "DESede", "mode": "CBC", "padding": "PKCS5Padding"}}], "input_token_count": 363, "output_token_count": 38, "latency": 2.3079519271850586}
{"id": "java_45", "result": [{"Basic.checkSizes": {"environ": "envVariables", "size": "5"}}], "input_token_count": 298, "output_token_count": 28, "latency": 1.8444855213165283}
{"id": "java_46", "result": [{"MethodInvokeTest.checkInjectedInvoker": {"csm": "csmInstance", "expected": "MyExpectedClass.class"}}], "input_token_count": 344, "output_token_count": 130, "latency": 8.021958112716675}
{"id": "java_47", "result": [{"LargeHandshakeTest.format": {"name": "CERTIFICATE", "value": "MIIFdTCCBF2gAwIBAgISESG...[1024 characters long Base64 string]..."}}], "input_token_count": 344, "output_token_count": 54, "latency": 3.3197202682495117}
{"id": "java_48", "result": [{"CookieHeaderTest.create": {"sa": "new InetSocketAddress(\"************\", 8080)", "sslContext": "testSSLContext"}}], "input_token_count": 381, "output_token_count": 51, "latency": 3.164592742919922}
{"id": "java_49", "result": [{"Http2TestExchangeImpl.sendResponseHeaders": {"rCode": "404", "responseLength": "1500"}}], "input_token_count": 341, "output_token_count": 38, "latency": 2.2978644371032715}
{"id": "java_50", "result": [{"TransformIndexerStateTests.doDeleteByQuery": {"deleteByQueryRequest": "deleteQueryRequest", "responseListener": "testListener"}}], "input_token_count": 353, "output_token_count": 39, "latency": 2.3876407146453857}
{"id": "java_51", "result": [{"CCRUsageTransportAction.masterOperation": {"task": "gatherCCRUsage", "request": "usageRequest", "state": "clusterState", "listener": "actionListener"}}], "input_token_count": 429, "output_token_count": 46, "latency": 2.8278608322143555}
{"id": "java_52", "result": [{"SamlObjectSignerTests.getChildren": {"node": "SAMLAssertionNode", "node_type": "org.w3c.dom.Element"}}], "input_token_count": 325, "output_token_count": 39, "latency": 2.****************}
{"id": "java_53", "result": [{"VotingOnlyNodePlugin.fullMasterWithOlderState": {"localAcceptedTerm": "42", "localAcceptedVersion": "7"}}], "input_token_count": 329, "output_token_count": 39, "latency": 2.***************}
{"id": "java_54", "result": "{\n  \"name\": \"AbstractTransportSearchableSnapshotsAction.shardOperation\",\n  \"arguments\": {\n    \"request\": \"snapshotRequest\",\n    \"shardRouting\": \"shardRouteInfo\",\n    \"task\": \"snapshotTask\",\n    \"listener\": \"operationListener\"\n  }\n}", "input_token_count": 436, "output_token_count": 61, "latency": 3.****************}
{"id": "java_55", "result": [{"SearchableSnapshotDirectory.create": {"repositories": "repositoriesService", "cache": "cacheService", "indexSettings": "indexSettingsForLogs", "shardPath": "/data/nodes/0/indices/logs/5", "currentTimeNanosSupplier": "currentTimeNanos", "threadPool": "threadPool", "blobStoreCacheService": "blobStoreCacheService", "sharedBlobCacheService": "sharedBlobCacheService"}}], "input_token_count": 681, "output_token_count": 100, "latency": 6.***************}
{"id": "java_56", "result": [{"CCSDuelIT.parseEntity": {"entity": "httpResponseEntity", "entityParser": "responseParser", "parserConfig": "defaultParserConfig"}}], "input_token_count": 381, "output_token_count": 42, "latency": 2.****************}
{"id": "java_57", "result": [{"Booleans.parseBooleanLenient": {"value": "yes", "defaultValue": "false"}}], "input_token_count": 330, "output_token_count": 29, "latency": 1.6633257865905762}
{"id": "java_58", "result": [{"XContentBuilder.map": {"values": "{'name': 'John Doe', 'age': 30, 'email': '<EMAIL>'}", "ensureNoSelfReferences": "true", "writeStartAndEndHeaders": "true"}}], "input_token_count": 407, "output_token_count": 61, "latency": 3.477001667022705}
{"id": "java_59", "result": [{"TruncateTranslogAction.execute": {"terminal": "java.lang.ProcessBuilder$ProcessImpl@1", "shardPath": "org.apache.lucene.store.NIOFSDirectory$NIOFSPath@/var/data/elasticsearch/nodes/0/indices/1shard", "indexDirectory": "org.apache.lucene.store.NIOFSDirectory@/var/data/elasticsearch/nodes/0/indices/1shard/index"}}], "input_token_count": 451, "output_token_count": 100, "latency": 6.279010772705078}
{"id": "java_60", "result": [{"NestedQueryBuilder.doBuild": {"parentSearchContext": "mainSearchContext", "innerHitsContext": "hitsContext"}}], "input_token_count": 351, "output_token_count": 34, "latency": 2.034336805343628}
{"id": "java_61", "result": [{"ScoreFunctionBuilders.exponentialDecayFunction": {"fieldName": "timestamp", "origin": "now", "scale": "10d", "offset": "2d", "decay": "0.5"}}], "input_token_count": 473, "output_token_count": 54, "latency": 3.0848071575164795}
{"id": "java_62", "result": [{"dvRangeQuery": {"field": "temperature", "queryType": "FLOAT", "from": "20.5", "to": "30.0", "includeFrom": "true", "includeTo": "false"}}], "input_token_count": 511, "output_token_count": 59, "latency": 3.3570733070373535}
{"id": "java_63", "result": [{"withinQuery": {"field": "age", "from": "30", "to": "40", "includeFrom": "true", "includeTo": "false"}}], "input_token_count": 451, "output_token_count": 47, "latency": 2.7093214988708496}
{"id": "java_64", "result": [{"DateScriptFieldType.createFieldType": {"name": "timestamp", "factory": "dateFactory", "script": "dateScript", "meta": "{\"format\": \"epoch_millis\"}", "onScriptError": "FAIL"}}], "input_token_count": 462, "output_token_count": 56, "latency": 3.1324148178100586}
{"id": "java_65", "result": [{"RootObjectMapper.doXContent": {"builder": "xContentBuilderInstance", "params": "[true, true, true, true, true]"}}], "input_token_count": 343, "output_token_count": 41, "latency": 2.4158642292022705}
{"id": "java_66", "result": "The function `CompositeRuntimeField.createChildRuntimeField` cannot be used to create a child runtime field for a composite field because composite fields in Elasticsearch do not support having children. Attempting to do so will result in an `IllegalArgumentException`. \n\nHowever, I will provide you with the JSON structure that would be used if such a function existed, and then explain why it won't work as expected.\n\nHere's how you would attempt to call the function:\n\n```json\n{\n  \"name\": \"CompositeRuntimeField.createChildRuntimeField\",\n  \"arguments\": {\n    \"parserContext\": \"mappingParserContext\",\n    \"parent\": \"compositeField1\",\n    \"parentScriptFactory\": \"compositeScriptFactory\",\n    \"onScriptError\": \"onScriptError.IGNORE\"\n  }\n}\n```\n\nBut since this function is designed to throw an exception when called, it won't actually create a child runtime field for a composite field. If you need to work with composite fields, you might want to look into how composite aggregation works in Elasticsearch, which is used for creating composite buckets based on multiple fields.\n\nWould you like to explore how composite aggregations work instead?", "input_token_count": 431, "output_token_count": 232, "latency": 12.936593532562256}
{"id": "java_67", "result": [{"MacDmgBundler.prepareDMGSetupScript": {"appLocation": "/Applications/PhotoEditor.app", "params": "{\"appName\": \"PhotoEditor\", \"imagesRoot\": \"/path/to/images\", \"backgroundImageFolder\": \"background\", \"packagingParams\": {}}"}}], "input_token_count": 349, "output_token_count": 68, "latency": 4.523958921432495}
{"id": "java_68", "result": "Error during inference: unterminated string literal (detected at line 1) (<string>, line 1)"}
{"id": "java_69", "result": [{"DurationImpl.alignSigns": {"buf": "[1, -2, 3, -4, 5, -6, 7]", "start": "2", "end": "5"}}], "input_token_count": 392, "output_token_count": 52, "latency": 3.011336088180542}
{"id": "java_70", "result": "{\n  \"name\": \"XMLNamespaceBinder.endElement\",\n  \"arguments\": {\n    \"element\": \"{'namespaceURI': 'http://www.example.com', 'localPart': 'item', 'prefix': 'ex'}\",\n    \"augs\": \"augmentations\"\n  }\n}", "input_token_count": 355, "output_token_count": 60, "latency": 4.084383010864258}
{"id": "java_71", "result": "{\n  \"name\": \"CoroutineManager.co_exit_to\",\n  \"arguments\": {\n    \"arg_object\": \"resultData\",\n    \"thisCoroutine\": \"5\",\n    \"toCoroutine\": \"10\"\n  }\n}", "input_token_count": 388, "output_token_count": 46, "latency": 2.7201600074768066}
{"id": "java_72", "result": [{"ToTextStream.characters": {"ch": "[C@1", "start": "5", "length": "10"}}], "input_token_count": 391, "output_token_count": 36, "latency": 2.318145513534546}
{"id": "java_73", "result": [{"Encodings.getEncodingInfo": {"encoding": "UTF-8", "allowJavaNames": "true"}}], "input_token_count": 303, "output_token_count": 32, "latency": 2.1910908222198486}
{"id": "java_74", "result": [{"BaseMarkupSerializer.surrogates": {"high": "55357", "low": "56832", "inContent": "false"}}], "input_token_count": 374, "output_token_count": 45, "latency": 2.9788315296173096}
{"id": "java_75", "result": [{"JdkXmlFeatures.getSystemProperty": {"feature": "XML_SECURITY", "sysPropertyName": "enableXmlSecurityFeature"}}], "input_token_count": 319, "output_token_count": 35, "latency": 2.3127498626708984}
{"id": "java_76", "result": [{"Intro.step": {"w": "800", "h": "600"}}], "input_token_count": 292, "output_token_count": 29, "latency": 2.009143829345703}
{"id": "java_77", "result": [{"JndiLoginModule.verifyPassword": {"encryptedPassword": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855", "password": "P@ssw0rd!"}}], "input_token_count": 356, "output_token_count": 94, "latency": 6.2133941650390625}
{"id": "java_78", "result": [{"OptionSpecBuilder.requiredUnless": {"dependent": "output-format", "otherDependents": "[\"quiet\", \"verbose\"]"}}], "input_token_count": 320, "output_token_count": 37, "latency": 2.3587863445281982}
{"id": "java_79", "result": [{"SAXFilterFactoryImpl.resolveEntity": {"publicid": "1234", "sysId": "http://astro.com/stylesheets/toptemplate"}}], "input_token_count": 347, "output_token_count": 43, "latency": 2.857135772705078}
{"id": "java_80", "result": [{"RegexConstraint.initIRPattern": {"category": "failOn", "ruleIdx": "42"}}], "input_token_count": 315, "output_token_count": 31, "latency": 2.119868278503418}
{"id": "java_81", "result": [{"TestObjectGraphAfterGC.doTesting": {"testcaseData": "humongous-test-case.json", "doGC": "customGCFunction", "checker": "referenceChecker", "gcLogName": "gc-analysis.log", "shouldContain": ["GC pause"], "shouldNotContain": ["OutOfMemoryError"]}}], "input_token_count": 592, "output_token_count": 76, "latency": 5.18369197845459}
{"id": "java_82", "result": [{"clear001a.runIt": {"args": "[\"arg1\", \"arg2\"]", "out": "System.out"}}], "input_token_count": 358, "output_token_count": 38, "latency": 2.3820414543151855}
{"id": "java_83", "result": [{"thrcputime002.runIt": {"argv": ["-waitTime", "120000", "-iterations", "500"], "out": "System.out"}}], "input_token_count": 388, "output_token_count": 51, "latency": 3.752974271774292}
{"id": "java_84", "result": [{"checkInnerFields": {"redefCls": "myRedefClass", "expValue": "100"}}], "input_token_count": 335, "output_token_count": 34, "latency": 2.1617608070373535}
{"id": "java_85", "result": [{"classfloadhk005.runIt": {"argv": "[Ljava/lang/String;@13f4c13f", "out": "java.io.PrintStream@1af2f82d"}}], "input_token_count": 385, "output_token_count": 55, "latency": 3.92246150970459}
{"id": "java_86", "result": [{"argumenttypes001.runThis": {"argv": "['-v', '--no-strict']", "out": "debugOutput"}}], "input_token_count": 353, "output_token_count": 38, "latency": 2.2780280113220215}
{"id": "java_87", "result": [{"suspendpolicy017.settingVMDeathRequest": {"suspendPolicy": "1", "property": "'testProperty=deathEvent001'"}}], "input_token_count": 322, "output_token_count": 41, "latency": 2.5258169174194336}
{"id": "java_88", "result": [{"filter_s002.setting22MethodEntryRequest": {"thread": "mainThread", "testedClass": "com.example.MainClass", "suspendPolicy": "EventRequest.SUSPEND_ALL", "property": "testProperty"}}], "input_token_count": 431, "output_token_count": 58, "latency": 3.9386520385742188}
{"id": "java_89", "result": [{"runThis": {"argv": "-waitTime, 120000, -debuggeeName, TestDebuggee", "out": "testLogStream"}}], "input_token_count": 369, "output_token_count": 45, "latency": 3.343937635421753}
{"id": "java_90", "result": [{"sourcepaths002.runIt": {"args": "['-v', '-p']", "out": "System.out"}}], "input_token_count": 345, "output_token_count": 36, "latency": 2.193664789199829}
{"id": "java_91", "result": [{"invokemethod007.runIt": {"args": "[\"suspend\", \"log\"]", "out": "debugLog"}}], "input_token_count": 341, "output_token_count": 38, "latency": 2.78696870803833}
{"id": "java_92", "result": [{"ClassFileFinder.findClassFile": {"name": "com.example.MyClass", "classPath": "/usr/local/classes:/home/<USER>/java/libs"}}], "input_token_count": 324, "output_token_count": 40, "latency": 3.1169753074645996}
{"id": "java_93", "result": [{"AbstractJarAgent.runJarAgent": {"options": "trace log", "inst": "appInstrumentation"}}], "input_token_count": 333, "output_token_count": 32, "latency": 2.148319959640503}
{"id": "java_94", "result": [{"NFILibrary.isMemberReadable": {"symbol": "getVersion", "recursive": null}}], "input_token_count": 301, "output_token_count": 29, "latency": 1.8403732776641846}
{"id": "java_95", "result": [{"ExportNodeTest.doGeneric": {"receiver": "ExportInlinedObject1Instance", "argument": "HelloWorld", "node": "InlinableNodeInstance", "library": "NodeLibraryInstance"}}], "input_token_count": 435, "output_token_count": 52, "latency": 3.684525489807129}
{"id": "java_96", "result": [{"InstrumentableProcessor.createCallConverter": {"converterMethod": "convertValue", "frameParameterName": "frameVar", "returnName": "returnValueCode"}}], "input_token_count": 390, "output_token_count": 42, "latency": 3.1388378143310547}
{"id": "java_97", "result": [{"FlatNodeGenFactory.generateIntrospectionInfo": {"clazz": "NodeClass", "inlined": "false"}}], "input_token_count": 320, "output_token_count": 34, "latency": 2.2313363552093506}
{"id": "java_98", "result": [{"LoopConditionProfile.calculateProbability": {"trueCountLocal": "150", "falseCountLocal": "50"}}], "input_token_count": 314, "output_token_count": 35, "latency": 2.3765828609466553}
{"id": "java_99", "result": [{"LibraryExport.createDelegate": {"factory": "myFactory", "delegate": "existingDelegate"}}], "input_token_count": 329, "output_token_count": 29, "latency": 1.8318350315093994}
