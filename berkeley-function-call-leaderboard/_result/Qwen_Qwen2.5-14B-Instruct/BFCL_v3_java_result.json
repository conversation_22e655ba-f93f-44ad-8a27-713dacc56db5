{"id": "java_0", "result": "[GeometryPresentation.createPresentation(controller=\"mapController\", parent=\"mapArea\")]", "input_token_count": 418, "output_token_count": 15, "latency": 1.8362078666687012}
{"id": "java_1", "result": "[SQLCompletionAnalyzer.makeProposalsFromObject(object=\"Customers\", useShortName=\"true\", params=\"{'limit': '50', 'schemaFilter': 'public'}\")]", "input_token_count": 463, "output_token_count": 36, "latency": 4.333461761474609}
{"id": "java_2", "result": "[FireBirdUtils.getViewSourceWithHeader(monitor=\"dbMonitor\", view=\"EmployeeView\", source=\"SELECT * FROM Employee WHERE status = 'active'\")]", "input_token_count": 475, "output_token_count": 32, "latency": 3.996368169784546}
{"id": "java_3", "result": "[DB2Tablespace.resolveTablespaceReference(monitor=\"dbMonitor\", dataSource=\"db2DataSource\", reference=\"USERSPACE1\")]", "input_token_count": 483, "output_token_count": 28, "latency": 3.2966244220733643}
{"id": "java_4", "result": "[DB2ViewBaseDepCache.prepareObjectsStatement(session=\"jdbcSession\", db2ViewBase=\"'EmployeeView'\")]", "input_token_count": 431, "output_token_count": 24, "latency": 2.7197046279907227}
{"id": "java_5", "result": "[PlainTextPresentation.createPresentation(controller=\"dataController\", parent=\"compositeParent\")]", "input_token_count": 439, "output_token_count": 15, "latency": 1.8159689903259277}
{"id": "java_6", "result": "[SpreadsheetPresentation.refreshData(refreshMetadata=\"true\", append=\"true\", keepState=\"true\")]", "input_token_count": 450, "output_token_count": 21, "latency": 2.3704404830932617}
{"id": "java_7", "result": "[EFSNIOResource.copy(destination=\"/backup/data.txt\", force=\"true\", monitor=\"progressTracker\")]", "input_token_count": 491, "output_token_count": 22, "latency": 2.523653745651245}
{"id": "java_8", "result": "[EFSNIOFile.setContents(source='fileStream', force='true', keepHistory='false', monitor='progressMonitor')]", "input_token_count": 519, "output_token_count": 26, "latency": 3.0760319232940674}
{"id": "java_9", "result": "[writeMultiPoint(multiPoint=\"MultiPoint(Point(1, 2), Point(3, 4), Point(5, 6), Point(7, 8), Point(9, 10))\", xyzmMode=\"'XyzmMode.XYZ'\", buffer=\"ByteBuffer.allocate(1024)\")]", "input_token_count": 541, "output_token_count": 69, "latency": 8.329798221588135}
{"id": "java_10", "result": "[JNIBridge.setLauncherInfo(launcher='/usr/local/bin/dbeaver', name='DBeaverLauncher)]", "input_token_count": 397, "output_token_count": 25, "latency": 3.3068480491638184}
{"id": "java_11", "result": "[BasePolicyDataProvider.getRegistryPolicyValue(root=\"HKEY_LOCAL_MACHINE\", property=\"EnableExtensions\")]", "input_token_count": 432, "output_token_count": 20, "latency": 2.4539804458618164}
{"id": "java_12", "result": "[ExasolExecutionContext.setCurrentSchema(monitor=\"progressMonitor\", schemaName=\"AnalyticsDB\")]", "input_token_count": 419, "output_token_count": 19, "latency": 2.340226411819458}
{"id": "java_13", "result": "[AltibaseGrantee.prepareObjectsStatement(session=\"JDBC_session\", owner=\"JohnDoe\")]", "input_token_count": 428, "output_token_count": 20, "latency": 2.622594118118286}
{"id": "java_14", "result": "[FunGameBase.onFinish(layout=\"gameLayout\", success=\"true\")]", "input_token_count": 413, "output_token_count": 15, "latency": 1.9043283462524414}
{"id": "java_15", "result": "[Res9patchStreamDecoder.decode(input=\"imageInputStream\", out=\"imageOutputStream\")]", "input_token_count": 432, "output_token_count": 17, "latency": 2.174954652786255}
{"id": "java_16", "result": "[InsnDecoder.invokePolymorphic(insn=instructionData, isRange=\"true\")]", "input_token_count": 416, "output_token_count": 18, "latency": 2.2643632888793945}
{"id": "java_17", "result": "[GenericTypesVisitor.attachGenericTypesInfo(mth=\"initMethod\", insn=\"newConstructorInsn\")]", "input_token_count": 430, "output_token_count": 20, "latency": 2.540752649307251}
{"id": "java_18", "result": "[SysRoleController.queryPageRoleCount(pageNo=\"3\", pageSize=\"20\")]", "input_token_count": 427, "output_token_count": 18, "latency": 2.2526051998138428}
{"id": "java_19", "result": "[PersonController.personal(model=\"webModel\", request=\"userRequest\")]", "input_token_count": 430, "output_token_count": 15, "latency": 1.9330101013183594}
{"id": "java_20", "result": "[HbaseAdapter.updateConfig(fileName=\"user-mapping.yml\", config=\"newMappingConfig\")]", "input_token_count": 425, "output_token_count": 18, "latency": 2.284105062484741}
{"id": "java_21", "result": "[SessionHandler.exceptionCaught(ctx=\"nettyChannelContext\", e=\"ioExceptionEvent\")]", "input_token_count": 427, "output_token_count": 18, "latency": 2.2853145599365234}
{"id": "java_22", "result": "[PmsProductServiceImpl.updateNewStatus(ids=\"[101, 202, 303]\", newStatus=\"2\")]", "input_token_count": 431, "output_token_count": 28, "latency": 3.5936131477355957}
{"id": "java_23", "result": "[SmsHomeNewProductServiceImpl.list(productName=\"'LED TV'\", recommendStatus=\"1\", pageSize=\"20\", pageNum=\"3\")]", "input_token_count": 498, "output_token_count": 27, "latency": 3.437445640563965}
{"id": "java_24", "result": "[PmsProductCategoryController.updateShowStatus(ids=\"[101, 102, 103]\", showStatus=\"0\")]", "input_token_count": 439, "output_token_count": 29, "latency": 3.6901662349700928}
{"id": "java_25", "result": "[SmsHomeRecommendSubjectController.updateSort(id=\"42\", sort=\"5\")]", "input_token_count": 406, "output_token_count": 17, "latency": 2.1016101837158203}
{"id": "java_26", "result": "[ProxyConnection.prepareCall(sql=\"CALL totalSales(?)\", resultSetType=\"ResultSet.TYPE_SCROLL_INSENSITIVE\", concurrency=\"ResultSet.CONCUR_READ_ONLY\", holdability=\"ResultSet.CLOSE_CURSORS_AT_COMMIT\")]", "input_token_count": 542, "output_token_count": 43, "latency": 5.582562446594238}
{"id": "java_27", "result": "[TwoSum.twoSum(nums=\"[2, 7, 11, 15]\", target=\"9\")]", "input_token_count": 421, "output_token_count": 24, "latency": 3.1378633975982666}
{"id": "java_28", "result": "[configStorage.dynamicCredentialsScheduledExecutorService(credentialsFile=\"es_credentials.properties\", credentialsRefreshInterval=\"30\", basicCredentials=basicAuthCredentials)]", "input_token_count": 467, "output_token_count": 29, "latency": 3.6591601371765137}
{"id": "java_29", "result": "[propertyTransferredToCollectorBuilder(property=\"zipkin.collector.activemq.concurrency\", value=\"10\", builderExtractor=\"b -> b.concurrency()\")]", "input_token_count": 464, "output_token_count": 33, "latency": 3.9944519996643066}
{"id": "java_30", "result": "[RedissonAsyncCache.putIfAbsent(key=\"answer\", value=\"42\")]", "input_token_count": 445, "output_token_count": 17, "latency": 2.2165305614471436}
{"id": "java_31", "result": "[RedissonRx.getQueue(name=\"taskQueue\", codec=\"jsonCodec\")]", "input_token_count": 400, "output_token_count": 16, "latency": 2.030294895172119}
{"id": "java_32", "result": "[RedissonPermitExpirableSemaphore.tryAcquireAsync(waitTime=\"5\", leaseTime=\"120\", unit=\"SECONDS\")]", "input_token_count": 481, "output_token_count": 30, "latency": 3.7247819900512695}
{"id": "java_33", "result": "[RedissonMapCache.putOperationAsync(key=\"employee:1234\", value=\"John Doe\")]", "input_token_count": 418, "output_token_count": 22, "latency": 2.8882834911346436}
{"id": "java_34", "result": "[ServiceManager.newTimeout(task=\"cleanupTask\", delay=\"300\", unit=\"TimeUnit.SECONDS\")]", "input_token_count": 458, "output_token_count": 22, "latency": 2.8618063926696777}
{"id": "java_35", "result": "[RedissonConnection.bitOp(op=\"BitOperation.AND\", destination=\"['user:online:both']\", keys=\"['user:online:today', 'user:online:yesterday']\")]", "input_token_count": 510, "output_token_count": 38, "latency": 4.502240180969238}
{"id": "java_36", "result": "[ObjectMapEntryReplayDecoder.decode(parts=\"['userID', 42, 'username', 'johndoe', 'isActive', true]\", state=\"processingState\")]", "input_token_count": 444, "output_token_count": 36, "latency": 4.347249746322632}
{"id": "java_37", "result": "[ConsoleAnnotator.annotate(context=\"jenkinsBuild\", text=\"buildOutput\")]", "input_token_count": 412, "output_token_count": 17, "latency": 2.****************}
{"id": "java_38", "result": "[NestedValueFetcher.createSourceMapStub(filteredSource=\"{'name': 'John Doe', 'address': {'street': '123 Elasticsearch Ave', 'city': 'Inverted Index'}}\")]", "input_token_count": 388, "output_token_count": 40, "latency": 5.****************}
{"id": "java_39", "result": "[NodeIdConverter.format(event=\"logEvent\", toAppendTo=\"logBuilder\")]", "input_token_count": 416, "output_token_count": 16, "latency": 2.***************}
{"id": "java_40", "result": "[RoutingNodesChangedObserver.shardInitialized(unassignedShard=\"shardA\", initializedShard=\"shardB\")]", "input_token_count": 419, "output_token_count": 25, "latency": 3.****************}
{"id": "java_41", "result": "[SearchHit.declareInnerHitsParseFields(parser=\"searchHitParser\")]", "input_token_count": 364, "output_token_count": 15, "latency": 1.****************}
{"id": "java_42", "result": "[TermQueryBuilderTests.termQuery(mapper=\"usernameField\", value=\"JohnDoe\", caseInsensitive=\"true\")]", "input_token_count": 450, "output_token_count": 23, "latency": 2.****************}
{"id": "java_43", "result": "[SecureMockMaker.createSpy(settings=\"mockSettings\", handler=\"mockHandler\", object=\"testObject\")]", "input_token_count": 462, "output_token_count": 21, "latency": 2.****************}
{"id": "java_44", "result": "[DesAPITest.init(crypt=\"DESede\", mode=\"CBC\", padding=\"PKCS5Padding)]", "input_token_count": 457, "output_token_count": 23, "latency": 2.****************}
{"id": "java_45", "result": "[Basic.checkSizes(environ=\"envVariables\", size=\"5\")]", "input_token_count": 398, "output_token_count": 14, "latency": 1.8983325958251953}
{"id": "java_46", "result": "[MethodInvokeTest.checkInjectedInvoker(csm=\"csmInstance\", expected=\"MyExpectedClass.class\")]", "input_token_count": 444, "output_token_count": 21, "latency": 2.698331594467163}
{"id": "java_47", "result": "[LargeHandshakeTest.format(name=\"CERTIFICATE\", value=\"MIIFdTCCBF2gAwIBAgISESG...\")]", "input_token_count": 444, "output_token_count": 27, "latency": 3.337033748626709}
{"id": "java_48", "result": "[CookieHeaderTest.create(sa=\"192.168.1.10:8080\", sslContext=\"testSSLContext\")]", "input_token_count": 481, "output_token_count": 32, "latency": 3.9515411853790283}
{"id": "java_49", "result": "[Http2TestExchangeImpl.sendResponseHeaders(rCode=\"404\", responseLength=\"1500\")]", "input_token_count": 441, "output_token_count": 24, "latency": 3.0781548023223877}
{"id": "java_50", "result": "[TransformIndexerStateTests.doDeleteByQuery(deleteByQueryRequest='deleteQueryRequest', responseListener='testListener')]", "input_token_count": 453, "output_token_count": 25, "latency": 3.132150411605835}
{"id": "java_51", "result": "[CCRUsageTransportAction.masterOperation(task=\"gatherCCRUsage\", request=\"usageRequest\", state=\"clusterState\", listener=\"actionListener\")]", "input_token_count": 517, "output_token_count": 28, "latency": 3.5318362712860107}
{"id": "java_52", "result": "[SamlObjectSignerTests.getChildren(node=\"SAMLAssertionNode\", node_type=\"Element.class\")]", "input_token_count": 425, "output_token_count": 20, "latency": 2.***************}
{"id": "java_53", "result": "[VotingOnlyNodePlugin.fullMasterWithOlderState(localAcceptedTerm=\"42\", localAcceptedVersion=\"7\")]", "input_token_count": 429, "output_token_count": 24, "latency": 2.****************}
{"id": "java_54", "result": "[AbstractTransportSearchableSnapshotsAction.shardOperation(request=\"snapshotRequest\", shardRouting=\"shardRouteInfo\", task=\"snapshotTask\", listener=\"operationListener\")]", "input_token_count": 524, "output_token_count": 34, "latency": 4.***************}
{"id": "java_55", "result": "[SearchableSnapshotDirectory.create(repositories=\"repositoriesService\", cache=\"cacheService\", indexSettings=\"indexSettingsForLogs\", shardPath=\"/data/nodes/0/indices/logs/5\", currentTimeNanosSupplier=\"currentTimeNanos\", threadPool=\"threadPool\", blobStoreCacheService=\"blobStoreCacheService\", sharedBlobCacheService=\"sharedBlobCacheService)]", "input_token_count": 745, "output_token_count": 74, "latency": 8.***************}
{"id": "java_56", "result": "[CCSDuelIT.parseEntity(entity=\"httpResponseEntity\", entityParser=\"responseParser\", parserConfig=\"defaultParserConfig\")]", "input_token_count": 475, "output_token_count": 26, "latency": 3.**************}
{"id": "java_57", "result": "[Booleans.parseBooleanLenient(value=\"yes\", defaultValue=\"false\")]", "input_token_count": 430, "output_token_count": 15, "latency": 2.090846300125122}
{"id": "java_58", "result": "[XContentBuilder.map(values=\"{'name': 'John Doe', 'age': 30, 'email': '<EMAIL>'}\", ensureNoSelfReferences=\"true\", writeStartAndEndHeaders=\"true\")]", "input_token_count": 501, "output_token_count": 44, "latency": 5.747856140136719}
{"id": "java_59", "result": "[TruncateTranslogAction.execute(terminal=\"terminal_object\", shardPath=\"ShardPath('/var/data/elasticsearch/nodes/0/indices/1shard')\", indexDirectory=\"FSDirectory.open(Paths.get('/var/data/elasticsearch/nodes/0/indices/1shard/index'))\")]", "input_token_count": 545, "output_token_count": 64, "latency": 7.69775390625}
{"id": "java_60", "result": "[NestedQueryBuilder.doBuild(parentSearchContext=\"mainSearchContext\", innerHitsContext=\"hitsContext\")]", "input_token_count": 451, "output_token_count": 20, "latency": 2.6311140060424805}
{"id": "java_61", "result": "[ScoreFunctionBuilders.exponentialDecayFunction(fieldName=\"timestamp\", origin=\"now\", scale=\"10d\", offset=\"2d\", decay=\"0.5\")]", "input_token_count": 556, "output_token_count": 34, "latency": 4.66792106628418}
{"id": "java_62", "result": "[dvRangeQuery(field=\"temperature\", queryType=\"FLOAT\", from=\"20.5\", to=\"30.0\", includeFrom=\"true\", includeTo=\"false\")]", "input_token_count": 587, "output_token_count": 37, "latency": 4.743241310119629}
{"id": "java_63", "result": "[withinQuery(field=\"age\", from=\"30\", to=\"40\", includeFrom=\"true\", includeTo=\"false\")]", "input_token_count": 533, "output_token_count": 27, "latency": 3.3531575202941895}
{"id": "java_64", "result": "[DateScriptFieldType.createFieldType(name=\"timestamp\", factory=\"dateFactory\", script=\"dateScript\", meta=\"{'format': 'epoch_millis'}\", onScriptError=\"FAIL\")]", "input_token_count": 544, "output_token_count": 37, "latency": 4.740312814712524}
{"id": "java_65", "result": "[RootObjectMapper.doXContent(builder=\"xContentBuilderInstance\", params=\"['true', 'true', 'true', 'true', 'true']\")]", "input_token_count": 443, "output_token_count": 32, "latency": 4.531176805496216}
{"id": "java_66", "result": "[CompositeRuntimeField.createChildRuntimeField(parserContext=\"mappingParserContext\", parent=\"compositeField1\", parentScriptFactory=\"compositeScriptFactory\", onScriptError=\"onScriptError.IGNORE\")]", "input_token_count": 519, "output_token_count": 38, "latency": 5.1534974575042725}
{"id": "java_67", "result": "[MacDmgBundler.prepareDMGSetupScript(appLocation=\"/Applications/PhotoEditor.app\", params=\"{'applicationName': 'PhotoEditor', 'imagesRoot': '/path/to/images', 'backgroundImageFolder': 'background', 'customParameters': 'volumeURL=/path/to/volumeURL'}\")]", "input_token_count": 449, "output_token_count": 62, "latency": 7.4951331615448}
{"id": "java_68", "result": "[MacBaseInstallerBundler.validateAppImageAndBundeler(params='{{\"appImagePath\": \"/Applications/MyApp.app\", \"appName\": \"MyApp\"}}')]", "input_token_count": 404, "output_token_count": 35, "latency": 4.5980658531188965}
{"id": "java_69", "result": "[DurationImpl.alignSigns(buf=durations, start='2', end='5')]", "input_token_count": 486, "output_token_count": 18, "latency": 2.4212474822998047}
{"id": "java_70", "result": "[XMLNamespaceBinder.endElement(element=\"QName('http://www.example.com', 'item', 'ex')\", augs=\"augmentations\")]", "input_token_count": 455, "output_token_count": 31, "latency": 4.3349244594573975}
{"id": "java_71", "result": "[CoroutineManager.co_exit_to(arg_object=\"'resultData'\", thisCoroutine=\"5\", toCoroutine=\"10\")]", "input_token_count": 482, "output_token_count": 23, "latency": 2.9670631885528564}
{"id": "java_72", "result": "[ToTextStream.characters(ch='textBuffer', start='5', length='10')]", "input_token_count": 485, "output_token_count": 19, "latency": 2.579817056655884}
{"id": "java_73", "result": "[Encodings.getEncodingInfo(encoding=\"UTF-8\", allowJavaNames=\"true\")]", "input_token_count": 403, "output_token_count": 18, "latency": 2.3870673179626465}
{"id": "java_74", "result": "[BaseMarkupSerializer.surrogates(high=\"55357\", low=\"56832\", inContent=\"false\")]", "input_token_count": 468, "output_token_count": 29, "latency": 4.101834297180176}
{"id": "java_75", "result": "[JdkXmlFeatures.getSystemProperty(feature=\"XML_SECURITY\", sysPropertyName=\"enableXmlSecurityFeature\")]", "input_token_count": 419, "output_token_count": 20, "latency": 2.6957244873046875}
{"id": "java_76", "result": "[Intro.step(w=\"800\", h=\"600\")]", "input_token_count": 392, "output_token_count": 15, "latency": 2.0594301223754883}
{"id": "java_77", "result": "[JndiLoginModule.verifyPassword(encryptedPassword=\"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\", password=\"P@ssw0rd!\")]", "input_token_count": 456, "output_token_count": 80, "latency": 10.28290343284607}
{"id": "java_78", "result": "[OptionSpecBuilder.requiredUnless(dependent=\"output-format\", otherDependents='[\"quiet\", \"verbose\"]')]", "input_token_count": 421, "output_token_count": 24, "latency": 3.3154642581939697}
{"id": "java_79", "result": "[SAXFilterFactoryImpl.resolveEntity(publicid=\"1234\", sysId=\"http://astro.com/stylesheets/toptemplate\")]", "input_token_count": 447, "output_token_count": 28, "latency": 4.005486249923706}
{"id": "java_80", "result": "[RegexConstraint.initIRPattern(category=\"failOn\", ruleIdx=\"42\")]", "input_token_count": 415, "output_token_count": 17, "latency": 2.382610559463501}
{"id": "java_81", "result": "[TestObjectGraphAfterGC.doTesting(testcaseData=\"humongous-test-case.json\", doGC=\"customGCExecutor.run()\", checker=\"referenceChecker.check()\", gcLogName=\"'gc-analysis.log'\", shouldContain=\"['GC pause']\", shouldNotContain=\"['OutOfMemoryError']\")]", "input_token_count": 668, "output_token_count": 57, "latency": 6.9558234214782715}
{"id": "java_82", "result": "[clear001a.runIt(args=\"['arg1', 'arg2']\", out=\"System.out\")]", "input_token_count": 458, "output_token_count": 23, "latency": 3.205543279647827}
{"id": "java_83", "result": "[thrcputime002.runIt(argv=\"['-waitTime', '120000', '-iterations', '500']\", out=\"System.out\")]", "input_token_count": 488, "output_token_count": 38, "latency": 5.162426710128784}
{"id": "java_84", "result": "[checkInnerFields(redefCls=\"myRedefClass\", expValue=\"100\")]", "input_token_count": 435, "output_token_count": 20, "latency": 2.57954740524292}
{"id": "java_85", "result": "[classfloadhk005.runIt(argv=\"['/path/to/classes', '60']\", out=\"logStream\")]", "input_token_count": 485, "output_token_count": 26, "latency": 3.7469594478607178}
{"id": "java_86", "result": "[argumenttypes001.runThis(argv=\"['-v', '--no-strict']\", out=\"debugOutput\")]", "input_token_count": 453, "output_token_count": 24, "latency": 3.4848947525024414}
{"id": "java_87", "result": "[suspendpolicy017.settingVMDeathRequest(suspendPolicy=\"1\", property=\"testProperty=deathEvent001\")]", "input_token_count": 422, "output_token_count": 27, "latency": 3.680274248123169}
{"id": "java_88", "result": "[filter_s002.setting22MethodEntryRequest(thread=\"mainThread\", testedClass=\"com.example.MainClass\", suspendPolicy=\"EventRequest.SUSPEND_ALL\", property=\"testProperty\")]", "input_token_count": 519, "output_token_count": 40, "latency": 5.214836120605469}
{"id": "java_89", "result": "[runThis(argv=\"-waitTime, 120000, -debuggeeName, TestDebuggee\", out=\"testLogStream\")]", "input_token_count": 469, "output_token_count": 31, "latency": 4.168246507644653}
{"id": "java_90", "result": "[sourcepaths002.runIt(args=\"['-v', '-p']\", out=\"System.out\")]", "input_token_count": 445, "output_token_count": 21, "latency": 2.850684642791748}
{"id": "java_91", "result": "[invokemethod007.runIt(args=\"['suspend', 'log']\", out=\"debugLog\")]", "input_token_count": 441, "output_token_count": 23, "latency": 3.301908254623413}
{"id": "java_92", "result": "[ClassFileFinder.findClassFile(name=\"com.example.MyClass\", classPath=\"/usr/local/classes:/home/<USER>/java/libs\")]", "input_token_count": 424, "output_token_count": 26, "latency": 3.556173086166382}
{"id": "java_93", "result": "[AbstractJarAgent.runJarAgent(options=\"trace log\", inst=\"appInstrumentation\")]", "input_token_count": 433, "output_token_count": 18, "latency": 2.29266619682312}
{"id": "java_94", "result": "[NFILibrary.isMemberReadable(symbol=\"getVersion\")]", "input_token_count": 402, "output_token_count": 12, "latency": 1.6308672428131104}
{"id": "java_95", "result": "[ExportNodeTest.doGeneric(receiver=\"ExportInlinedObject1Instance\", argument=\"'HelloWorld'\", node=\"InlinableNodeInstance\", library=\"NodeLibraryInstance\")]", "input_token_count": 523, "output_token_count": 34, "latency": 4.544511556625366}
{"id": "java_96", "result": "[InstrumentableProcessor.createCallConverter(converterMethod=\"convertValue\", frameParameterName=\"frameVar\", returnName=\"returnValueCode\")]", "input_token_count": 484, "output_token_count": 27, "latency": 3.552295446395874}
{"id": "java_97", "result": "[FlatNodeGenFactory.generateIntrospectionInfo(clazz=\"NodeClass\", inlined=\"false\")]", "input_token_count": 420, "output_token_count": 20, "latency": 2.7486650943756104}
{"id": "java_98", "result": "[LoopConditionProfile.calculateProbability(trueCountLocal=\"150\", falseCountLocal=\"50\")]", "input_token_count": 414, "output_token_count": 21, "latency": 2.988450050354004}
{"id": "java_99", "result": "[LibraryExport.createDelegate(factory=\"myFactory\", delegate=\"existingDelegate\", _force_adoption=True)]", "input_token_count": 429, "output_token_count": 21, "latency": 2.988102912902832}
