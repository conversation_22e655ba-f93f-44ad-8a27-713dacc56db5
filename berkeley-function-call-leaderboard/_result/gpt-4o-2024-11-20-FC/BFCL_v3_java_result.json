{"id": "java_0", "result": [{"GeometryPresentation_createPresentation": "{\"controller\":\"mapController\",\"parent\":\"mapArea\"}"}], "input_token_count": 165, "output_token_count": 23, "latency": 0.914846658706665}
{"id": "java_1", "result": [{"SQLCompletionAnalyzer_makeProposalsFromObject": "{\"object\":\"Customers\",\"useShortName\":\"true\",\"params\":\"{\\\"limit\\\": \\\"50\\\", \\\"schemaFilter\\\": \\\"public\\\"}\"}"}], "input_token_count": 193, "output_token_count": 44, "latency": 1.0211944580078125}
{"id": "java_2", "result": [{"FireBirdUtils_getViewSourceWithHeader": "{\"monitor\":\"dbMonitor\",\"view\":\"EmployeeView\",\"source\":\"SELECT * FROM Employee WHERE status = 'active'\"}"}], "input_token_count": 209, "output_token_count": 40, "latency": 1.4000225067138672}
{"id": "java_3", "result": [{"DB2Tablespace_resolveTablespaceReference": "{\"monitor\":\"dbMonitor\",\"dataSource\":\"db2DataSource\",\"reference\":\"USERSPACE1\"}"}], "input_token_count": 220, "output_token_count": 37, "latency": 1.7961766719818115}
{"id": "java_4", "result": [{"DB2ViewBaseDepCache_prepareObjectsStatement": "{\"session\":\"jdbcSession\",\"db2ViewBase\":\"EmployeeView\"}"}], "input_token_count": 174, "output_token_count": 31, "latency": 1.2130815982818604}
{"id": "java_5", "result": [{"PlainTextPresentation_createPresentation": "{\"controller\":\"dataController\",\"parent\":\"compositeParent\"}"}], "input_token_count": 188, "output_token_count": 26, "latency": 1.0499086380004883}
{"id": "java_6", "result": [{"SpreadsheetPresentation_refreshData": "{\"refreshMetadata\":\"true\",\"append\":\"true\",\"keepState\":\"true\"}"}], "input_token_count": 178, "output_token_count": 29, "latency": 1.0188004970550537}
{"id": "java_7", "result": [{"EFSNIOResource_copy": "{\"destination\":\"/backup/data.txt\",\"force\":\"true\",\"monitor\":\"progressTracker\"}"}], "input_token_count": 223, "output_token_count": 31, "latency": 1.2560439109802246}
{"id": "java_8", "result": [{"EFSNIOFile_setContents": "{\"source\":\"fileStream\",\"force\":\"true\",\"keepHistory\":\"false\",\"monitor\":\"progressMonitor\"}"}], "input_token_count": 241, "output_token_count": 35, "latency": 1.2696938514709473}
{"id": "java_9", "result": [{"writeMultiPoint": "{\"multiPoint\":\"new MultiPoint(Arrays.asList(new Point(1, 2), new Point(3, 4), new Point(5, 6), new Point(7, 8), new Point(9, 10)))\",\"xyzmMode\":\"XyzmMode.XYZ\",\"buffer\":\"ByteBuffer.allocate(1024)\"}"}], "input_token_count": 273, "output_token_count": 83, "latency": 1.6007843017578125}
{"id": "java_10", "result": [{"JNIBridge_setLauncherInfo": "{\"launcher\":\"/usr/local/bin/dbeaver\",\"name\":\"DBeaverLauncher\"}"}], "input_token_count": 144, "output_token_count": 33, "latency": 1.0279781818389893}
{"id": "java_11", "result": [{"BasePolicyDataProvider_getRegistryPolicyValue": "{\"root\":\"HKEY_LOCAL_MACHINE\",\"property\":\"EnableExtensions\"}"}], "input_token_count": 178, "output_token_count": 29, "latency": 0.759084939956665}
{"id": "java_12", "result": [{"ExasolExecutionContext_setCurrentSchema": "{\"monitor\":\"progressMonitor\",\"schemaName\":\"AnalyticsDB\"}"}], "input_token_count": 164, "output_token_count": 27, "latency": 1.1068387031555176}
{"id": "java_13", "result": [{"AltibaseGrantee_prepareObjectsStatement": "{\"session\":\"JDBC_session\",\"owner\":\"JohnDoe\"}"}], "input_token_count": 173, "output_token_count": 28, "latency": 1.2529594898223877}
{"id": "java_14", "result": [{"FunGameBase_onFinish": "{\"layout\":\"gameLayout\",\"success\":\"true\"}"}], "input_token_count": 160, "output_token_count": 24, "latency": 1.637148141860962}
{"id": "java_15", "result": [{"Res9patchStreamDecoder_decode": "{\"input\":\"imageInputStream\",\"out\":\"imageOutputStream\"}"}], "input_token_count": 181, "output_token_count": 27, "latency": 1.7805485725402832}
{"id": "java_16", "result": [{"InsnDecoder_invokePolymorphic": "{\"insn\":\"instructionData\",\"isRange\":\"true\"}"}], "input_token_count": 164, "output_token_count": 28, "latency": 1.0300896167755127}
{"id": "java_17", "result": [{"GenericTypesVisitor_attachGenericTypesInfo": "{\"mth\":\"initMethod\",\"insn\":\"newConstructorInsn\"}"}], "input_token_count": 177, "output_token_count": 29, "latency": 1.1470694541931152}
{"id": "java_18", "result": [{"SysRoleController_queryPageRoleCount": "{\"pageNo\":\"3\",\"pageSize\":\"20\"}"}], "input_token_count": 172, "output_token_count": 26, "latency": 1.3011679649353027}
{"id": "java_19", "result": [{"PersonController_personal": "{\"model\":\"webModel\",\"request\":\"userRequest\"}"}], "input_token_count": 179, "output_token_count": 23, "latency": 0.922804594039917}
{"id": "java_20", "result": [{"HbaseAdapter_updateConfig": "{\"fileName\":\"user-mapping.yml\",\"config\":\"newMappingConfig\"}"}], "input_token_count": 173, "output_token_count": 28, "latency": 1.180232048034668}
{"id": "java_21", "result": [{"SessionHandler_exceptionCaught": "{\"ctx\":\"nettyChannelContext\",\"e\":\"ioExceptionEvent\"}"}], "input_token_count": 176, "output_token_count": 26, "latency": 1.1712872982025146}
{"id": "java_22", "result": [{"PmsProductServiceImpl_updateNewStatus": "{\"ids\":\"[101, 202, 303]\",\"newStatus\":\"2\"}"}], "input_token_count": 173, "output_token_count": 34, "latency": 1.2576210498809814}
{"id": "java_23", "result": [{"SmsHomeNewProductServiceImpl_list": "{\"productName\":\"LED TV\",\"recommendStatus\":\"1\",\"pageSize\":\"20\",\"pageNum\":\"3\"}"}], "input_token_count": 218, "output_token_count": 38, "latency": 0.9694874286651611}
{"id": "java_24", "result": [{"PmsProductCategoryController_updateShowStatus": "{\"ids\":\"[101,102,103]\",\"showStatus\":\"0\"}"}], "input_token_count": 180, "output_token_count": 32, "latency": 1.5184566974639893}
{"id": "java_25", "result": [{"SmsHomeRecommendSubjectController_updateSort": "{\"id\":\"42\",\"sort\":\"5\"}"}], "input_token_count": 151, "output_token_count": 25, "latency": 1.277165174484253}
{"id": "java_26", "result": [{"ProxyConnection_prepareCall": "{\"sql\":\"CALL totalSales(?)\",\"resultSetType\":\"ResultSet.TYPE_SCROLL_INSENSITIVE\",\"concurrency\":\"ResultSet.CONCUR_READ_ONLY\",\"holdability\":\"ResultSet.CLOSE_CURSORS_AT_COMMIT\"}"}], "input_token_count": 268, "output_token_count": 55, "latency": 1.746239185333252}
{"id": "java_27", "result": [{"TwoSum_twoSum": "{\"nums\":\"[2, 7, 11, 15]\",\"target\":\"9\"}"}], "input_token_count": 165, "output_token_count": 33, "latency": 1.0505633354187012}
{"id": "java_28", "result": [{"configStorage_dynamicCredentialsScheduledExecutorService": "{\"credentialsFile\":\"es_credentials.properties\",\"credentialsRefreshInterval\":\"30\",\"basicCredentials\":\"basicAuthCredentials\"}"}], "input_token_count": 196, "output_token_count": 36, "latency": 0.8441939353942871}
{"id": "java_29", "result": [{"propertyTransferredToCollectorBuilder": "{\"property\":\"zipkin.collector.activemq.concurrency\",\"value\":\"10\",\"builderExtractor\":\"getConcurrency\"}"}], "input_token_count": 194, "output_token_count": 35, "latency": 1.3242924213409424}
{"id": "java_30", "result": [{"RedissonAsyncCache_putIfAbsent": "{\"key\":\"answer\",\"value\":\"42\"}"}], "input_token_count": 193, "output_token_count": 24, "latency": 1.4302752017974854}
{"id": "java_31", "result": [{"RedissonRx_getQueue": "{\"name\":\"taskQueue\",\"codec\":\"jsonCodec\"}"}], "input_token_count": 145, "output_token_count": 24, "latency": 1.136460542678833}
{"id": "java_32", "result": [{"RedissonPermitExpirableSemaphore_tryAcquireAsync": "{\"waitTime\":\"5\",\"leaseTime\":\"120\",\"unit\":\"SECONDS\"}"}], "input_token_count": 211, "output_token_count": 32, "latency": 1.1015217304229736}
{"id": "java_33", "result": [{"RedissonMapCache_putOperationAsync": "{\"key\":\"employee:1234\",\"value\":\"John Doe\"}"}], "input_token_count": 162, "output_token_count": 28, "latency": 1.318995714187622}
{"id": "java_34", "result": [{"ServiceManager_newTimeout": "{\"task\":\"cleanupTask\",\"delay\":\"5\",\"unit\":\"TimeUnit.SECONDS\"}"}], "input_token_count": 193, "output_token_count": 28, "latency": 1.3632872104644775}
{"id": "java_35", "result": [{"RedissonConnection_bitOp": "{\"op\":\"AND\",\"destination\":\"user:online:both\",\"keys\":\"[\\\"user:online:today\\\",\\\"user:online:yesterday\\\"]\"}"}], "input_token_count": 247, "output_token_count": 43, "latency": 1.3970768451690674}
{"id": "java_36", "result": [{"ObjectMapEntryReplayDecoder_decode": "{\"parts\":\"['userID', 42, 'username', 'johndoe', 'isActive', true]\",\"state\":\"processingState\"}"}], "input_token_count": 192, "output_token_count": 45, "latency": 1.0489273071289062}
{"id": "java_37", "result": [{"ConsoleAnnotator_annotate": "{\"context\":\"jenkinsBuild\",\"text\":\"buildOutput\"}"}], "input_token_count": 162, "output_token_count": 26, "latency": 0.****************}
{"id": "java_38", "result": [{"NestedValueFetcher_createSourceMapStub": "{\"filteredSource\":\"{\\\"name\\\":{},\\\"address\\\":{}}\"}"}], "input_token_count": 149, "output_token_count": 30, "latency": 1.****************}
{"id": "java_39", "result": [{"NodeIdConverter_format": "{\"event\":\"logEvent\",\"toAppendTo\":\"logBuilder\"}"}], "input_token_count": 165, "output_token_count": 25, "latency": 2.****************}
{"id": "java_40", "result": [{"RoutingNodesChangedObserver_shardInitialized": "{\"unassignedShard\":\"shardA\",\"initializedShard\":\"shardB\"}"}], "input_token_count": 159, "output_token_count": 32, "latency": 0.****************}
{"id": "java_41", "result": [{"SearchHit_declareInnerHitsParseFields": "{\"parser\":\"searchHitParser\"}"}], "input_token_count": 124, "output_token_count": 23, "latency": 0.****************}
{"id": "java_42", "result": [{"TermQueryBuilderTests_termQuery": "{\"mapper\":\"usernameField\",\"value\":\"JohnDoe\",\"caseInsensitive\":\"true\"}"}], "input_token_count": 183, "output_token_count": 30, "latency": 1.****************}
{"id": "java_43", "result": [{"SecureMockMaker_createSpy": "{\"settings\":\"mockSettings\",\"handler\":\"mockHandler\",\"object\":\"testObject\"}"}], "input_token_count": 196, "output_token_count": 30, "latency": 1.****************}
{"id": "java_44", "result": [{"DesAPITest_init": "{\"crypt\":\"DESede\",\"mode\":\"CBC\",\"padding\":\"PKCS5Padding\"}"}], "input_token_count": 188, "output_token_count": 29, "latency": 1.4697191715240479}
{"id": "java_45", "result": [{"Basic_checkSizes": "{\"environ\":\"envVariables\",\"size\":\"5\"}"}], "input_token_count": 145, "output_token_count": 22, "latency": 0.8315176963806152}
{"id": "java_46", "result": [{"MethodInvokeTest_checkInjectedInvoker": "{\"csm\":\"csmInstance\",\"expected\":\"MyExpectedClass.class\"}"}], "input_token_count": 190, "output_token_count": 29, "latency": 0.9050204753875732}
{"id": "java_47", "result": [{"LargeHandshakeTest_format": "{\"name\":\"CERTIFICATE\",\"value\":\"MIIFdTCCBF2gAwIBAgISESG\"}"}], "input_token_count": 185, "output_token_count": 35, "latency": 0.8525369167327881}
{"id": "java_48", "result": [{"CookieHeaderTest_create": "{\"sa\":\"new InetSocketAddress(\\\"************\\\", 8080)\",\"sslContext\":\"testSSLContext\"}"}], "input_token_count": 215, "output_token_count": 40, "latency": 1.4616665840148926}
{"id": "java_49", "result": [{"Http2TestExchangeImpl_sendResponseHeaders": "{\"rCode\":\"404\",\"responseLength\":\"1500\"}"}], "input_token_count": 181, "output_token_count": 28, "latency": 1.2476558685302734}
{"id": "java_50", "result": [{"TransformIndexerStateTests_doDeleteByQuery": "{\"deleteByQueryRequest\":\"deleteQueryRequest\",\"responseListener\":\"testListener\"}"}], "input_token_count": 195, "output_token_count": 32, "latency": 0.8742411136627197}
{"id": "java_51", "result": [{"CCRUsageTransportAction_masterOperation": "{\"task\":\"CCRUsageStatsTask\",\"request\":\"usageRequest\",\"state\":\"clusterState\",\"listener\":\"actionListener\"}"}], "input_token_count": 239, "output_token_count": 38, "latency": 10.485058546066284}
{"id": "java_52", "result": [{"SamlObjectSignerTests_getChildren": "{\"node\":\"SAMLAssertionNode\",\"node_type\":\"Element.class\"}"}], "input_token_count": 169, "output_token_count": 29, "latency": 1.****************}
{"id": "java_53", "result": [{"VotingOnlyNodePlugin_fullMasterWithOlderState": "{\"localAcceptedTerm\":\"42\",\"localAcceptedVersion\":\"7\"}"}], "input_token_count": 168, "output_token_count": 31, "latency": 1.****************}
{"id": "java_54", "result": [{"AbstractTransportSearchableSnapshotsAction_shardOperation": "{\"request\":\"snapshotRequest\",\"shardRouting\":\"shardRouteInfo\",\"task\":\"snapshotTask\",\"listener\":\"operationListener\"}"}], "input_token_count": 244, "output_token_count": 42, "latency": 1.****************}
{"id": "java_55", "result": [{"SearchableSnapshotDirectory_create": "{\"repositories\":\"repositoriesService\",\"cache\":\"cacheService\",\"indexSettings\":\"indexSettingsForLogs\",\"shardPath\":\"/data/nodes/0/indices/logs/5\",\"currentTimeNanosSupplier\":\"currentTimeNanos\",\"threadPool\":\"threadPool\",\"blobStoreCacheService\":\"blobStoreCacheService\",\"sharedBlobCacheService\":\"sharedBlobCacheService\"}"}], "input_token_count": 404, "output_token_count": 86, "latency": 1.****************}
{"id": "java_56", "result": [{"CCSDuelIT_parseEntity": "{\"entity\":\"httpResponseEntity\",\"entityParser\":\"responseParser\",\"parserConfig\":\"defaultParserConfig\"}"}], "input_token_count": 206, "output_token_count": 35, "latency": 0.****************}
{"id": "java_57", "result": [{"Booleans_parseBooleanLenient": "{\"value\":\"yes\",\"defaultValue\":\"false\"}"}], "input_token_count": 177, "output_token_count": 26, "latency": 1.1623353958129883}
{"id": "java_58", "result": [{"XContentBuilder_map": "{\"values\":\"{name: 'name', age: 'age', email: 'email'}\",\"ensureNoSelfReferences\":\"true\",\"writeStartAndEndHeaders\":\"true\"}"}], "input_token_count": 229, "output_token_count": 47, "latency": 2.3990468978881836}
{"id": "java_59", "result": [{"TruncateTranslogAction_execute": "{\"terminal\":\"Terminal()\",\"shardPath\":\"ShardPath(Paths.get(\\\"/var/data/elasticsearch/nodes/0/indices/1shard\\\"))\",\"indexDirectory\":\"FSDirectory.open(Paths.get(\\\"/var/data/elasticsearch/nodes/0/indices/1shard/index\\\"))\"}"}], "input_token_count": 277, "output_token_count": 75, "latency": 1.7239830493927002}
{"id": "java_60", "result": [{"NestedQueryBuilder_doBuild": "{\"parentSearchContext\":\"mainSearchContext\",\"innerHitsContext\":\"hitsContext\"}"}], "input_token_count": 194, "output_token_count": 30, "latency": 1.3099496364593506}
{"id": "java_61", "result": [{"ScoreFunctionBuilders_exponentialDecayFunction": "{\"fieldName\":\"timestamp\",\"origin\":\"now\",\"scale\":\"10d\",\"offset\":\"2d\",\"decay\":\"0.5\"}"}], "input_token_count": 266, "output_token_count": 42, "latency": 1.0857462882995605}
{"id": "java_62", "result": [{"dvRangeQuery": "{\"field\":\"temperature\",\"queryType\":\"FLOAT\",\"from\":\"20.5\",\"to\":\"30.0\",\"includeFrom\":\"true\",\"includeTo\":\"false\"}"}], "input_token_count": 277, "output_token_count": 43, "latency": 1.3528711795806885}
{"id": "java_63", "result": [{"withinQuery": "{\"field\":\"age\",\"from\":\"30\",\"to\":\"40\",\"includeFrom\":\"true\",\"includeTo\":\"false\"}"}], "input_token_count": 235, "output_token_count": 34, "latency": 1.0189476013183594}
{"id": "java_64", "result": [{"DateScriptFieldType_createFieldType": "{\"name\":\"timestamp\",\"factory\":\"dateFactory\",\"script\":\"dateScript\",\"meta\":\"{\\\"format\\\":\\\"epoch_millis\\\"}\",\"onScriptError\":\"FAIL\"}"}], "input_token_count": 253, "output_token_count": 47, "latency": 1.252920389175415}
{"id": "java_65", "result": [{"RootObjectMapper_doXContent": "{\"builder\":\"xContentBuilderInstance\",\"params\":\"[\\\"INCLUDE_DEFAULTS\\\", \\\"SKIP_RUNTIME_FIELDS\\\"]\"}"}], "input_token_count": 191, "output_token_count": 38, "latency": 1.256047010421753}
{"id": "java_66", "result": [{"CompositeRuntimeField_createChildRuntimeField": "{\"parserContext\":\"mappingParserContext\",\"parent\":\"compositeField1\",\"parentScriptFactory\":\"compositeScriptFactory\",\"onScriptError\":\"onScriptError.IGNORE\"}"}], "input_token_count": 240, "output_token_count": 49, "latency": 3.1057393550872803}
{"id": "java_67", "result": [{"MacDmgBundler_prepareDMGSetupScript": "{\"appLocation\":\"/Applications/PhotoEditor.app\",\"params\":\"{\\\"appName\\\":\\\"PhotoEditor\\\",\\\"backgroundImage\\\":\\\"/path/to/custom/background.png\\\",\\\"volumeURL\\\":\\\"/Volumes/PhotoEditor\\\",\\\"installDir\\\":\\\"/Applications\\\"}\"}"}], "input_token_count": 197, "output_token_count": 66, "latency": 2.6486353874206543}
{"id": "java_68", "result": [{"MacBaseInstallerBundler_validateAppImageAndBundeler": "{\"params\":\"{\\\"applicationImagePath\\\":\\\"/Applications/MyApp.app\\\",\\\"applicationName\\\":\\\"MyApp\\\"}\"}"}], "input_token_count": 163, "output_token_count": 42, "latency": 1.6932995319366455}
{"id": "java_69", "result": [{"DurationImpl_alignSigns": "{\"buf\":\"durations\",\"start\":\"2\",\"end\":\"5\"}"}], "input_token_count": 222, "output_token_count": 26, "latency": 0.7916686534881592}
{"id": "java_70", "result": [{"XMLNamespaceBinder_endElement": "{\"element\":\"{namespaceURI='http://www.example.com', localPart='item', prefix='ex'}\",\"augs\":\"augmentations\"}"}], "input_token_count": 200, "output_token_count": 42, "latency": 1.4182884693145752}
{"id": "java_71", "result": [{"CoroutineManager_co_exit_to": "{\"arg_object\":\"resultData\",\"thisCoroutine\":\"5\",\"toCoroutine\":\"10\"}"}], "input_token_count": 211, "output_token_count": 32, "latency": 0.8259589672088623}
{"id": "java_72", "result": [{"ToTextStream_characters": "{\"ch\":\"textBuffer\",\"start\":\"5\",\"length\":\"10\"}"}], "input_token_count": 219, "output_token_count": 27, "latency": 1.4628496170043945}
{"id": "java_73", "result": [{"Encodings_getEncodingInfo": "{\"encoding\":\"UTF-8\",\"allowJavaNames\":\"true\"}"}], "input_token_count": 146, "output_token_count": 27, "latency": 0.9464788436889648}
{"id": "java_74", "result": [{"BaseMarkupSerializer_surrogates": "{\"high\":\"55357\",\"low\":\"56832\",\"inContent\":\"true\"}"}], "input_token_count": 194, "output_token_count": 30, "latency": 0.9221830368041992}
{"id": "java_75", "result": [{"JdkXmlFeatures_getSystemProperty": "{\"feature\":\"XML_SECURITY\",\"sysPropertyName\":\"enableXmlSecurityFeature\"}"}], "input_token_count": 167, "output_token_count": 30, "latency": 0.7880234718322754}
{"id": "java_76", "result": [{"Intro_step": "{\"w\":\"800\",\"h\":\"600\"}"}], "input_token_count": 135, "output_token_count": 20, "latency": 0.7421410083770752}
{"id": "java_77", "result": [{"JndiLoginModule_verifyPassword": "{\"encryptedPassword\":\"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855\",\"password\":\"P@ssw0rd!\"}"}], "input_token_count": 178, "output_token_count": 64, "latency": 1.8128442764282227}
{"id": "java_78", "result": [{"OptionSpecBuilder_requiredUnless": "{\"dependent\": \"quiet\", \"otherDependents\": \"verbose\"}"}, {"OptionSpecBuilder_requiredUnless": "{\"dependent\": \"verbose\", \"otherDependents\": \"quiet\"}"}], "input_token_count": 170, "output_token_count": 63, "latency": 2.29384446144104}
{"id": "java_79", "result": [{"SAXFilterFactoryImpl_resolveEntity": "{\"publicid\":\"1234\",\"sysId\":\"http://astro.com/stylesheets/toptemplate\"}"}], "input_token_count": 189, "output_token_count": 35, "latency": 0.9102640151977539}
{"id": "java_80", "result": [{"RegexConstraint_initIRPattern": "{\"category\":\"failOn\",\"ruleIdx\":\"42\"}"}], "input_token_count": 159, "output_token_count": 24, "latency": 1.4524853229522705}
{"id": "java_81", "result": [{"TestObjectGraphAfterGC_doTesting": "{\"testcaseData\":\"humongous-test-case.json\",\"doGC\":\"customGarbageCollector\",\"checker\":\"referenceChecker\",\"gcLogName\":\"gc-analysis.log\",\"shouldContain\":\"[\\\"GC pause\\\"]\",\"shouldNotContain\":\"[\\\"OutOfMemoryError\\\"]\"}"}], "input_token_count": 357, "output_token_count": 69, "latency": 1.7681920528411865}
{"id": "java_82", "result": [{"clear001a_runIt": "{\"args\":\"testArgs\",\"out\":\"System.out\"}"}], "input_token_count": 202, "output_token_count": 24, "latency": 1.4434714317321777}
{"id": "java_83", "result": [{"thrcputime002_runIt": "{\"argv\":\"-waitTime, 2, -iterations, 500\",\"out\":\"System.out\"}"}], "input_token_count": 230, "output_token_count": 36, "latency": 1.3333957195281982}
{"id": "java_84", "result": [{"checkInnerFields": "{\"redefCls\": \"myRedefClass\", \"expValue\": \"100\"}"}, {"checkInnerFields": "{\"redefCls\": \"myRedefClass\", \"expValue\": \"100\"}"}, {"checkInnerFields": "{\"redefCls\": \"myRedefClass\", \"expValue\": \"100\"}"}], "input_token_count": 177, "output_token_count": 92, "latency": 1.2919330596923828}
{"id": "java_85", "result": [{"classfloadhk005_runIt": "{\"argv\":\"['/path/to/classes', '60']\",\"out\":\"logStream\"}"}], "input_token_count": 222, "output_token_count": 33, "latency": 1.0519828796386719}
{"id": "java_86", "result": [{"argumenttypes001_runThis": "{\"argv\":\"['-v', '--no-strict']\",\"out\":\"debugOutput\"}"}], "input_token_count": 197, "output_token_count": 31, "latency": 1.7306807041168213}
{"id": "java_87", "result": [{"suspendpolicy017_settingVMDeathRequest": "{\"suspendPolicy\":\"EVENT_THREAD\",\"property\":\"deathEvent001\"}"}], "input_token_count": 166, "output_token_count": 30, "latency": 0.8465876579284668}
{"id": "java_88", "result": [{"filter_s002_setting22MethodEntryRequest": "{\"thread\":\"mainThread\",\"testedClass\":\"com.example.MainClass\",\"suspendPolicy\":\"EventRequest.SUSPEND_ALL\",\"property\":\"testProperty\"}"}], "input_token_count": 237, "output_token_count": 46, "latency": 0.9327049255371094}
{"id": "java_89", "result": [{"runThis": "{\"argv\":\"-waitTime, 2, -debuggeeName, TestDebuggee\",\"out\":\"testLogStream\"}"}], "input_token_count": 216, "output_token_count": 35, "latency": 1.2852985858917236}
{"id": "java_90", "result": [{"sourcepaths002_runIt": "{\"args\":\"['-v', '-p']\",\"out\":\"System.out\"}"}], "input_token_count": 189, "output_token_count": 29, "latency": 1.0092809200286865}
{"id": "java_91", "result": [{"invokemethod007_runIt": "{\"args\":\"['suspend', 'log']\",\"out\":\"debugLog\"}"}], "input_token_count": 187, "output_token_count": 31, "latency": 1.1344308853149414}
{"id": "java_92", "result": [{"ClassFileFinder_findClassFile": "{\"name\": \"com.example.MyClass\", \"classPath\": \"/usr/local/classes\"}"}, {"ClassFileFinder_findClassFile": "{\"name\": \"com.example.MyClass\", \"classPath\": \"/home/<USER>/java/libs\"}"}], "input_token_count": 169, "output_token_count": 76, "latency": 1.1390047073364258}
{"id": "java_93", "result": [{"AbstractJarAgent_runJarAgent": "{\"options\":\"trace log\",\"inst\":\"appInstrumentation\"}"}], "input_token_count": 179, "output_token_count": 25, "latency": 1.0760831832885742}
{"id": "java_94", "result": [{"NFILibrary_isMemberReadable": "{\"symbol\":\"getVersion\"}"}], "input_token_count": 152, "output_token_count": 20, "latency": 1.0332493782043457}
{"id": "java_95", "result": [{"ExportNodeTest_doGeneric": "{\"receiver\":\"ExportInlinedObject1Instance\",\"argument\":\"HelloWorld\",\"node\":\"InlinableNodeInstance\",\"library\":\"NodeLibraryInstance\"}"}], "input_token_count": 243, "output_token_count": 42, "latency": 1.199976921081543}
{"id": "java_96", "result": [{"InstrumentableProcessor_createCallConverter": "{\"converterMethod\":\"convertValue\",\"frameParameterName\":\"frameVar\",\"returnName\":\"returnValueCode\"}"}], "input_token_count": 214, "output_token_count": 36, "latency": 0.9370334148406982}
{"id": "java_97", "result": [{"FlatNodeGenFactory_generateIntrospectionInfo": "{\"clazz\":\"NodeClass\",\"inlined\":\"false\"}"}], "input_token_count": 163, "output_token_count": 27, "latency": 0.8067531585693359}
{"id": "java_98", "result": [{"LoopConditionProfile_calculateProbability": "{\"trueCountLocal\":\"150\",\"falseCountLocal\":\"50\"}"}], "input_token_count": 154, "output_token_count": 28, "latency": 0.8052008152008057}
{"id": "java_99", "result": [{"LibraryExport_createDelegate": "{\"factory\":\"myFactory\",\"delegate\":\"existingDelegate\"}"}], "input_token_count": 176, "output_token_count": 23, "latency": 0.994316577911377}
