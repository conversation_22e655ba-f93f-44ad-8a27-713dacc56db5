{"id": "java_0", "result": "[GeometryPresentation.createPresentation(controller='mapController', parent='mapArea')]", "input_token_count": 415, "output_token_count": 15, "latency": 2.7622103691101074}
{"id": "java_1", "result": "[SQLCompletionAnalyzer.makeProposalsFromObject(object='Customers', useShortName='true', params='{\"limit\":\"50\", \"schema\":\"public\"}')]", "input_token_count": 459, "output_token_count": 32, "latency": 0.8986477851867676}
{"id": "java_2", "result": "[FireBirdUtils.getViewSourceWithHeader(monitor='dbMonitor', view='EmployeeView', source=\"SELECT * FROM Employee WHERE status = 'active'\")]", "input_token_count": 472, "output_token_count": 33, "latency": 1.3905839920043945}
{"id": "java_3", "result": "[DB2Tablespace.resolveTablespaceReference(monitor='dbMonitor', dataSource='db2DataSource', reference='USERSPACE1')]", "input_token_count": 482, "output_token_count": 29, "latency": 0.8183302879333496}
{"id": "java_4", "result": "[DB2ViewBaseDepCache.prepareObjectsStatement(session=jdbcSession, db2ViewBase=EmployeeView)]", "input_token_count": 426, "output_token_count": 23, "latency": 0.8121626377105713}
{"id": "java_5", "result": "[PlainTextPresentation.createPresentation(controller='dataController', parent='compositeParent')]", "input_token_count": 438, "output_token_count": 17, "latency": 0.9818246364593506}
{"id": "java_6", "result": "[SpreadsheetPresentation.refreshData(refreshMetadata=\"true\", append=\"true\", keepState=\"true\")]", "input_token_count": 445, "output_token_count": 19, "latency": 0.8764724731445312}
{"id": "java_7", "result": "[EFSNIOResource.copy(destination='/backup/data.txt', force='true', monitor='progressTracker')]", "input_token_count": 485, "output_token_count": 21, "latency": 0.766287088394165}
{"id": "java_8", "result": "[EFSNIOFile.setContents(source=\"fileStream\", force=\"true\", keepHistory=\"false\", monitor=\"progressMonitor\")]", "input_token_count": 517, "output_token_count": 26, "latency": 0.8231608867645264}
{"id": "java_9", "result": "[writeMultiPoint(multiPoint=\"new MultiPoint(Arrays.asList(new Point(1, 2), new Point(3, 4), new Point(5, 6), new Point(7, 8), new Point(9, 10)))\", xyzmMode=\"XyzmMode.XYZ\", buffer=\"ByteBuffer.allocate(1024)\")]", "input_token_count": 538, "output_token_count": 76, "latency": 1.1664817333221436}
{"id": "java_10", "result": "[JNIBridge.setLauncherInfo(launcher='/usr/local/bin/dbeaver', name='DBeaverLauncher')]", "input_token_count": 392, "output_token_count": 24, "latency": 0.7546923160552979}
{"id": "java_11", "result": "[BasePolicyDataProvider.getRegistryPolicyValue(root='HKEY_LOCAL_MACHINE', property='EnableExtensions')]", "input_token_count": 428, "output_token_count": 21, "latency": 0.689558744430542}
{"id": "java_12", "result": "[ExasolExecutionContext.setCurrentSchema(monitor='progressMonitor', schemaName='AnalyticsDB')]", "input_token_count": 414, "output_token_count": 20, "latency": 3.301375150680542}
{"id": "java_13", "result": "[AltibaseGrantee.prepareObjectsStatement(session='JDBC_session', owner='JohnDoe')]", "input_token_count": 422, "output_token_count": 19, "latency": 0.7136187553405762}
{"id": "java_14", "result": "[FunGameBase.onFinish(layout='gameLayout', success='true')]", "input_token_count": 410, "output_token_count": 15, "latency": 0.7436637878417969}
{"id": "java_15", "result": "[Res9patchStreamDecoder.decode(input='imageInputStream', out='imageOutputStream')]", "input_token_count": 430, "output_token_count": 19, "latency": 0.7861196994781494}
{"id": "java_16", "result": "[InsnDecoder.invokePolymorphic(insn='instructionData', isRange='true')]", "input_token_count": 414, "output_token_count": 18, "latency": 1.9805078506469727}
{"id": "java_17", "result": "[GenericTypesVisitor.attachGenericTypesInfo(mth=\"initMethod\", insn=\"newConstructorInsn\")]", "input_token_count": 428, "output_token_count": 21, "latency": 0.964930534362793}
{"id": "java_18", "result": "[SysRoleController.queryPageRoleCount(pageNo=\"3\", pageSize=\"20\")]", "input_token_count": 423, "output_token_count": 18, "latency": 0.7928287982940674}
{"id": "java_19", "result": "[PersonController.personal(model=webModel, request=userRequest)]", "input_token_count": 427, "output_token_count": 13, "latency": 0.7705128192901611}
{"id": "java_20", "result": "[HbaseAdapter.updateConfig(fileName='user-mapping.yml', config='newMappingConfig')]", "input_token_count": 423, "output_token_count": 20, "latency": 0.8794112205505371}
{"id": "java_21", "result": "[SessionHandler.exceptionCaught(ctx=\"nettyChannelContext\", e=\"ioExceptionEvent\")]", "input_token_count": 425, "output_token_count": 18, "latency": 0.8262901306152344}
{"id": "java_22", "result": "[PmsProductServiceImpl.updateNewStatus(ids=\"[101, 202, 303]\", newStatus=\"2\")]", "input_token_count": 423, "output_token_count": 23, "latency": 0.8442466259002686}
{"id": "java_23", "result": "[SmsHomeNewProductServiceImpl.list(productName='LED TV', recommendStatus='1', pageSize='20', pageNum='3')]", "input_token_count": 498, "output_token_count": 29, "latency": 1.1510188579559326}
{"id": "java_24", "result": "[PmsProductCategoryController.updateShowStatus(ids=\"[101, 102, 103]\", showStatus=\"0\")]", "input_token_count": 430, "output_token_count": 23, "latency": 0.7923305034637451}
{"id": "java_25", "result": "[SmsHomeRecommendSubjectController.updateSort(id=\"42\", sort=\"5\")]", "input_token_count": 400, "output_token_count": 16, "latency": 0.7637388706207275}
{"id": "java_26", "result": "[ProxyConnection.prepareCall(sql=\"CALL totalSales(?)\", resultSetType=\"ResultSet.TYPE_SCROLL_INSENSITIVE\", concurrency=\"ResultSet.CONCUR_READ_ONLY\", holdability=\"ResultSet.CLOSE_CURSORS_AT_COMMIT\")]", "input_token_count": 547, "output_token_count": 46, "latency": 1.084986686706543}
{"id": "java_27", "result": "[TwoSum.twoSum(nums=\"[2, 7, 11, 15]\", target=\"9\")]", "input_token_count": 415, "output_token_count": 22, "latency": 0.8135085105895996}
{"id": "java_28", "result": "[configStorage.dynamicCredentialsScheduledExecutorService(credentialsFile='es_credentials.properties', credentialsRefreshInterval='30', basicCredentials='basicAuthCredentials')]", "input_token_count": 462, "output_token_count": 28, "latency": 0.7886040210723877}
{"id": "java_29", "result": "[propertyTransferredToCollectorBuilder(property='zipkin.collector.activemq.concurrency', value='10', builderExtractor='getConcurrency')]", "input_token_count": 457, "output_token_count": 26, "latency": 0.940493106842041}
{"id": "java_30", "result": "[RedissonAsyncCache.putIfAbsent(key='answer', value='42')]", "input_token_count": 442, "output_token_count": 16, "latency": 0.7302072048187256}
{"id": "java_31", "result": "[RedissonRx.getQueue(name='taskQueue', codec='jsonCodec')]", "input_token_count": 395, "output_token_count": 16, "latency": 0.8585813045501709}
{"id": "java_32", "result": "[RedissonPermitExpirableSemaphore.tryAcquireAsync(waitTime=\"5\", leaseTime=\"120\", unit=\"SECONDS\")]", "input_token_count": 475, "output_token_count": 24, "latency": 0.8013627529144287}
{"id": "java_33", "result": "[RedissonMapCache.putOperationAsync(key='employee:1234', value='John Doe')]", "input_token_count": 411, "output_token_count": 20, "latency": 1.8488531112670898}
{"id": "java_34", "result": "[ServiceManager.newTimeout(task='cleanupTask', delay='300', unit='TimeUnit.SECONDS')]", "input_token_count": 454, "output_token_count": 20, "latency": 0.7810013294219971}
{"id": "java_35", "result": "[RedissonConnection.bitOp(op=\"BitOperation.AND\", destination=\"user:online:both\", keys=\"['user:online:today', 'user:online:yesterday']\")]", "input_token_count": 510, "output_token_count": 38, "latency": 0.9200878143310547}
{"id": "java_36", "result": "[ObjectMapEntryReplayDecoder.decode(parts=\"['userID', 42, 'username', 'johndoe', 'isActive', true]\", state=\"processingState\")]", "input_token_count": 441, "output_token_count": 36, "latency": 0.917926549911499}
{"id": "java_37", "result": "[ConsoleAnnotator.annotate(context='jenkinsBuild', text='buildOutput')]", "input_token_count": 410, "output_token_count": 17, "latency": 0.****************}
{"id": "java_38", "result": "[NestedValueFetcher.createSourceMapStub(filteredSource=\"{'name': 'John Doe', 'address': '123 Main St'}\")]", "input_token_count": 386, "output_token_count": 27, "latency": 1.****************}
{"id": "java_39", "result": "[NodeIdConverter.format(event='logEvent', toAppendTo='logBuilder')]", "input_token_count": 416, "output_token_count": 17, "latency": 0.****************}
{"id": "java_40", "result": "[RoutingNodesChangedObserver.shardInitialized(unassignedShard='shardA', initializedShard='shardB')]", "input_token_count": 411, "output_token_count": 23, "latency": 0.****************}
{"id": "java_41", "result": "[SearchHit.declareInnerHitsParseFields(parser=\"searchHitParser\")]", "input_token_count": 360, "output_token_count": 15, "latency": 0.****************}
{"id": "java_42", "result": "[TermQueryBuilderTests.termQuery(mapper='usernameField', value='JohnDoe', caseInsensitive='true')]", "input_token_count": 447, "output_token_count": 23, "latency": 0.****************}
{"id": "java_43", "result": "[SecureMockMaker.createSpy(settings='mockSettings', handler='mockHandler', object='testObject')]", "input_token_count": 458, "output_token_count": 21, "latency": 0.****************}
{"id": "java_44", "result": "[DesAPITest.init(crypt='DESede', mode='CBC', padding='PKCS5Padding')]", "input_token_count": 451, "output_token_count": 22, "latency": 0.****************}
{"id": "java_45", "result": "[Basic.checkSizes(environ=\"envVariables\", size=\"5\")]", "input_token_count": 395, "output_token_count": 14, "latency": 0.7742245197296143}
{"id": "java_46", "result": "[MethodInvokeTest.checkInjectedInvoker(csm='csmInstance', expected='MyExpectedClass.class')]", "input_token_count": 440, "output_token_count": 21, "latency": 0.7994506359100342}
{"id": "java_47", "result": "[LargeHandshakeTest.format(name='CERTIFICATE', value='MIIFdTCCBF2gAwIBAgISESG')]", "input_token_count": 434, "output_token_count": 26, "latency": 0.8572103977203369}
{"id": "java_48", "result": "[CookieHeaderTest.create(sa=\"new InetSocketAddress(\\\"************\\\", 8080)\", sslContext=\"testSSLContext\")]", "input_token_count": 465, "output_token_count": 31, "latency": 0.8336670398712158}
{"id": "java_49", "result": "[Http2TestExchangeImpl.sendResponseHeaders(rCode=\"404\", responseLength=\"1500\")]", "input_token_count": 433, "output_token_count": 20, "latency": 0.8382143974304199}
{"id": "java_50", "result": "[TransformIndexerStateTests.doDeleteByQuery(deleteByQueryRequest=\"deleteQueryRequest\", responseListener=\"testListener\")]", "input_token_count": 448, "output_token_count": 24, "latency": 0.9186594486236572}
{"id": "java_51", "result": "[CCRUsageTransportAction.masterOperation(task=\"task_object\", request=\"usageRequest\", state=\"clusterState\", listener=\"actionListener\")]", "input_token_count": 514, "output_token_count": 27, "latency": 0.8939671516418457}
{"id": "java_52", "result": "[SamlObjectSignerTests.getChildren(node='SAMLAssertionNode', node_type='Element.class')]", "input_token_count": 420, "output_token_count": 20, "latency": 0.****************}
{"id": "java_53", "result": "[VotingOnlyNodePlugin.fullMasterWithOlderState(localAcceptedTerm=\"42\", localAcceptedVersion=\"7\")]", "input_token_count": 422, "output_token_count": 22, "latency": 0.****************}
{"id": "java_54", "result": "[AbstractTransportSearchableSnapshotsAction.shardOperation(request='snapshotRequest', shardRouting='shardRouteInfo', task='snapshotTask', listener='operationListener')]", "input_token_count": 522, "output_token_count": 33, "latency": 0.****************}
{"id": "java_55", "result": "[SearchableSnapshotDirectory.create(repositories='repositoriesService', cache='cacheService', indexSettings='indexSettingsForLogs', shardPath='/data/nodes/0/indices/logs/5', currentTimeNanosSupplier='currentTimeNanos', threadPool='threadPool', blobStoreCacheService='blobStoreCacheService', sharedBlobCacheService='sharedBlobCacheService')]", "input_token_count": 745, "output_token_count": 77, "latency": 1.***************}
{"id": "java_56", "result": "[CCSDuelIT.parseEntity(entity=httpResponseEntity, entityParser=responseParser, parserConfig=defaultParserConfig)]", "input_token_count": 471, "output_token_count": 24, "latency": 0.****************}
{"id": "java_57", "result": "[Booleans.parseBooleanLenient(value='yes', defaultValue='false')]", "input_token_count": 428, "output_token_count": 17, "latency": 0.7710886001586914}
{"id": "java_58", "result": "[XContentBuilder.map(values=\"{'name': 'John Doe', 'age': '30', 'email': '<EMAIL>'}\", ensureNoSelfReferences=\"true\", writeStartAndEndHeaders=\"true\")]", "input_token_count": 498, "output_token_count": 45, "latency": 1.3198065757751465}
{"id": "java_59", "result": "[TruncateTranslogAction.execute(terminal=\"TerminalInterface\", shardPath=\"ShardPath(Paths.get('/var/data/elasticsearch/nodes/0/indices/1shard'))\", indexDirectory=\"FSDirectory.open(Paths.get('/var/data/elasticsearch/nodes/0/indices/1shard/index'))\")]", "input_token_count": 542, "output_token_count": 63, "latency": 1.298088550567627}
{"id": "java_60", "result": "[NestedQueryBuilder.doBuild(parentSearchContext='mainSearchContext', innerHitsContext='hitsContext')]", "input_token_count": 448, "output_token_count": 21, "latency": 0.8818016052246094}
{"id": "java_61", "result": "[ScoreFunctionBuilders.exponentialDecayFunction(fieldName='timestamp', origin='now', scale='10d', offset='2d', decay='0.5')]", "input_token_count": 553, "output_token_count": 33, "latency": 0.8733482360839844}
{"id": "java_62", "result": "[dvRangeQuery(field='temperature', queryType='FLOAT', from='20.5', to='30.0', includeFrom='true', includeTo='false')]", "input_token_count": 581, "output_token_count": 35, "latency": 0.8669323921203613}
{"id": "java_63", "result": "[withinQuery(field='age', from='30', to='40', includeFrom='true', includeTo='false')]", "input_token_count": 525, "output_token_count": 25, "latency": 0.8313860893249512}
{"id": "java_64", "result": "[DateScriptFieldType.createFieldType(name='timestamp', factory='dateFactory', script='dateScript', meta='{\"format\": \"epoch_millis\"}', onScriptError='FAIL')]", "input_token_count": 543, "output_token_count": 39, "latency": 1.2321267127990723}
{"id": "java_65", "result": "[RootObjectMapper.doXContent(builder=\"xContentBuilderInstance\", params=\"['includeDefaults=true', 'skipRuntimeFields=true']\")]", "input_token_count": 440, "output_token_count": 28, "latency": 0.9206845760345459}
{"id": "java_66", "result": "[CompositeRuntimeField.createChildRuntimeField(parserContext='mappingParserContext', parent='compositeField1', parentScriptFactory='compositeScriptFactory', onScriptError='onScriptError.IGNORE')]", "input_token_count": 520, "output_token_count": 41, "latency": 0.9530062675476074}
{"id": "java_67", "result": "[MacDmgBundler.prepareDMGSetupScript(appLocation='/Applications/PhotoEditor.app', params='{\"applicationName\":\"PhotoEditor\",\"backgroundImageFolder\":\"customBackgrounds\",\"volumeURL\":\"PhotoEditorVolume\",\"installationDirectory\":\"/Applications\"}')]", "input_token_count": 447, "output_token_count": 51, "latency": 2.117922306060791}
{"id": "java_68", "result": "[MacBaseInstallerBundler.validateAppImageAndBundeler(params=\"{'applicationImagePath': '/Applications/MyApp.app', 'applicationName': 'MyApp'}\")]", "input_token_count": 399, "output_token_count": 34, "latency": 0.9694898128509521}
{"id": "java_69", "result": "[DurationImpl.alignSigns(buf=\"durations\", start=\"2\", end=\"5\")]", "input_token_count": 484, "output_token_count": 18, "latency": 0.7865273952484131}
{"id": "java_70", "result": "[XMLNamespaceBinder.endElement(element=\"{namespaceURI='http://www.example.com', localPart='item', prefix='ex'}\", augs=\"augmentations\")]", "input_token_count": 450, "output_token_count": 33, "latency": 0.8464527130126953}
{"id": "java_71", "result": "[CoroutineManager.co_exit_to(arg_object='resultData', thisCoroutine='5', toCoroutine='10')]", "input_token_count": 476, "output_token_count": 22, "latency": 0.8210287094116211}
{"id": "java_72", "result": "[ToTextStream.characters(ch='textBuffer', start='5', length='10')]", "input_token_count": 480, "output_token_count": 18, "latency": 0.9155385494232178}
{"id": "java_73", "result": "[Encodings.getEncodingInfo(encoding=\"UTF-8\", allowJavaNames=\"true\")]", "input_token_count": 398, "output_token_count": 19, "latency": 1.19517183303833}
{"id": "java_74", "result": "[BaseMarkupSerializer.surrogates(high='55357', low='56832', inContent='true')]", "input_token_count": 458, "output_token_count": 23, "latency": 0.7676537036895752}
{"id": "java_75", "result": "[JdkXmlFeatures.getSystemProperty(feature='XML_SECURITY', sysPropertyName='enableXmlSecurityFeature')]", "input_token_count": 418, "output_token_count": 21, "latency": 1.3247344493865967}
{"id": "java_76", "result": "[Intro.step(w=\"800\", h=\"600\")]", "input_token_count": 384, "output_token_count": 11, "latency": 0.7637453079223633}
{"id": "java_77", "result": "[JndiLoginModule.verifyPassword(encryptedPassword='e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855', password='P@ssw0rd!')]", "input_token_count": 428, "output_token_count": 56, "latency": 0.9139506816864014}
{"id": "java_78", "result": "[OptionSpecBuilder.requiredUnless(dependent='quiet', otherDependents='verbose')]", "input_token_count": 416, "output_token_count": 17, "latency": 1.169184923171997}
{"id": "java_79", "result": "[SAXFilterFactoryImpl.resolveEntity(publicid='1234', sysId='http://astro.com/stylesheets/toptemplate')]", "input_token_count": 440, "output_token_count": 25, "latency": 0.81870436668396}
{"id": "java_80", "result": "[RegexConstraint.initIRPattern(category='failOn', ruleIdx='42')]", "input_token_count": 410, "output_token_count": 16, "latency": 0.7962734699249268}
{"id": "java_81", "result": "[TestObjectGraphAfterGC.doTesting(testcaseData=\"'humongous-test-case.json'\", doGC=\"'customGarbageCollector'\", checker=\"'referenceChecker'\", gcLogName=\"'gc-analysis.log'\", shouldContain=\"['GC pause']\", shouldNotContain=\"['OutOfMemoryError']\")]", "input_token_count": 667, "output_token_count": 57, "latency": 1.1553072929382324}
{"id": "java_82", "result": "[clear001a.runIt(args=\"testArgs\", out=\"System.out\")]", "input_token_count": 452, "output_token_count": 16, "latency": 0.9232842922210693}
{"id": "java_83", "result": "[thrcputime002.runIt(argv=\"-waitTime, 2, -iterations, 500\", out=\"System.out\")]", "input_token_count": 480, "output_token_count": 27, "latency": 1.3964290618896484}
{"id": "java_84", "result": "[checkInnerFields(redefCls='myRedefClass', expValue='100')]", "input_token_count": 429, "output_token_count": 18, "latency": 1.8633666038513184}
{"id": "java_85", "result": "[classfloadhk005.runIt(argv=\"['/path/to/classes', '60']\", out=\"logStream\")]", "input_token_count": 472, "output_token_count": 23, "latency": 1.112032175064087}
{"id": "java_86", "result": "[argumenttypes001.runThis(argv=\"['-v', '--no-strict']\", out=\"debugOutput\")]", "input_token_count": 447, "output_token_count": 22, "latency": 0.8043868541717529}
{"id": "java_87", "result": "[suspendpolicy017.settingVMDeathRequest(suspendPolicy=\"EVENT_THREAD\", property=\"deathEvent001\")]", "input_token_count": 418, "output_token_count": 21, "latency": 0.8913958072662354}
{"id": "java_88", "result": "[filter_s002.setting22MethodEntryRequest(thread=\"mainThread\", testedClass=\"com.example.MainClass\", suspendPolicy=\"EventRequest.SUSPEND_ALL\", property=\"testProperty\")]", "input_token_count": 515, "output_token_count": 37, "latency": 2.7360427379608154}
{"id": "java_89", "result": "[runThis(argv=\"-waitTime, 2, -debuggeeName, TestDebuggee\", out=\"testLogStream\")]", "input_token_count": 466, "output_token_count": 26, "latency": 0.9785077571868896}
{"id": "java_90", "result": "[sourcepaths002.runIt(args=\"['-v', '-p']\", out=\"System.out\")]", "input_token_count": 439, "output_token_count": 19, "latency": 0.9205586910247803}
{"id": "java_91", "result": "[invokemethod007.runIt(args=\"['suspend', 'log']\", out=\"debugLog\")]", "input_token_count": 436, "output_token_count": 22, "latency": 0.8646953105926514}
{"id": "java_92", "result": "[ClassFileFinder.findClassFile(name='com.example.MyClass', classPath='/usr/local/classes:/home/<USER>/java/libs')]", "input_token_count": 420, "output_token_count": 26, "latency": 0.7804131507873535}
{"id": "java_93", "result": "[AbstractJarAgent.runJarAgent(options='trace log', inst='appInstrumentation')]", "input_token_count": 428, "output_token_count": 17, "latency": 0.9583241939544678}
{"id": "java_94", "result": "[NFILibrary.isMemberReadable(symbol='getVersion')]", "input_token_count": 398, "output_token_count": 11, "latency": 0.7996561527252197}
{"id": "java_95", "result": "[ExportNodeTest.doGeneric(receiver='ExportInlinedObject1Instance', argument='HelloWorld', node='InlinableNodeInstance', library='NodeLibraryInstance')]", "input_token_count": 519, "output_token_count": 34, "latency": 0.8824679851531982}
{"id": "java_96", "result": "[InstrumentableProcessor.createCallConverter(converterMethod='convertValue', frameParameterName='frameVar', returnName='returnValueCode')]", "input_token_count": 481, "output_token_count": 28, "latency": 0.8437554836273193}
{"id": "java_97", "result": "[FlatNodeGenFactory.generateIntrospectionInfo(clazz='NodeClass', inlined='false')]", "input_token_count": 415, "output_token_count": 19, "latency": 0.8955662250518799}
{"id": "java_98", "result": "[LoopConditionProfile.calculateProbability(trueCountLocal='150', falseCountLocal='50')]", "input_token_count": 407, "output_token_count": 18, "latency": 0.823190450668335}
{"id": "java_99", "result": "[LibraryExport.createDelegate(factory='myFactory', delegate='existingDelegate')]", "input_token_count": 425, "output_token_count": 15, "latency": 0.8473639488220215}
