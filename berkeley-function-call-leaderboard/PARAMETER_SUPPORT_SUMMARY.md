# Temperature、Top-k、Top-p 参数支持实现总结

## 概述

本次更新为 Berkeley Function Call Leaderboard 添加了对 temperature、top-k、top-p 参数的全面支持，允许用户更精细地控制大语言模型的输出行为。

## 实现的更改

### 1. 命令行接口更新 (`bfcl/__main__.py`)

添加了两个新的命令行参数：
- `--top-k`: Top-k 参数，默认值为 -1（禁用）
- `--top-p`: Top-p 参数，默认值为 1.0

```bash
python -m bfcl generate \
    --model gpt-4o-mini-2024-07-18-FC \
    --temperature 0.7 \
    --top-k 50 \
    --top-p 0.9
```

### 2. 基础处理器更新 (`bfcl/model_handler/base_handler.py`)

- 更新了 `BaseHandler.__init__()` 方法以接受 `top_k` 和 `top_p` 参数
- 所有模型处理器现在都继承这些参数

### 3. 构建函数更新 (`bfcl/_llm_response_generation.py`)

- 更新了 `build_handler()` 函数以传递新参数
- 更新了 `generate_results()` 函数以使用新参数

### 4. 模型处理器更新

#### API 模型处理器

**OpenAI 处理器** (`bfcl/model_handler/api_inference/openai.py`)
- 支持 temperature 和 top_p 参数
- 注意：OpenAI API 不支持 top_k 参数

**Claude 处理器** (`bfcl/model_handler/api_inference/claude.py`)
- 完全支持 temperature、top_k、top_p 参数
- 使用 Anthropic API 的原生参数支持

**Gemini 处理器** (`bfcl/model_handler/api_inference/gemini.py`)
- 完全支持 temperature、top_k、top_p 参数
- 通过 GenerationConfig 传递参数

**Amazon Nova 处理器** (`bfcl/model_handler/api_inference/nova.py`)
- 完全支持 temperature、top_k、top_p 参数
- 使用 inferenceConfig 传递参数

**Cohere 处理器** (`bfcl/model_handler/api_inference/cohere.py`)
- 支持 temperature、top_k（作为 k）、top_p（作为 p）参数

**其他 API 处理器**
- Fireworks (`fireworks.py`)
- Novita (`novita.py`)
- Functionary (`functionary.py`)

#### 本地模型处理器

**基础 OSS 处理器** (`bfcl/model_handler/local_inference/base_oss_handler.py`)
- 更新了构造函数以接受新参数
- 在 API 调用中传递 top_k 和 top_p 参数

**具体本地模型处理器**
- Hermes (`hermes.py`)
- GLM (`glm.py`)
- MiniCPM FC (`minicpm_fc.py`)
- Salesforce Llama (`salesforce_llama.py`)
- Salesforce Qwen (`salesforce_qwen.py`)

## 参数说明

### Temperature
- **范围**: 0.0 - 2.0
- **默认值**: 0.001
- **作用**: 控制输出的随机性和创造性

### Top-k
- **范围**: 正整数，-1 表示禁用
- **默认值**: -1（禁用）
- **作用**: 限制每步只考虑概率最高的 k 个词汇

### Top-p (核采样)
- **范围**: 0.0 - 1.0
- **默认值**: 1.0
- **作用**: 动态选择累积概率达到 p 的词汇集合

## 模型支持情况

### 完全支持 (temperature, top-k, top-p)
- Claude 模型系列
- Gemini 模型系列
- Amazon Nova 模型系列
- Cohere 模型系列
- 本地开源模型（通过 vLLM/SGLang）

### 部分支持 (temperature, top-p)
- OpenAI 模型系列（不支持 top-k）

### 特殊情况
- OpenAI 推理模型（o1, o3 系列）不支持 temperature 参数

## 向后兼容性

所有更改都保持了向后兼容性：
- 现有代码无需修改即可继续工作
- 新参数都有合理的默认值
- 旧的 API 调用方式仍然有效

## 使用示例

### 基本用法
```bash
# 使用默认参数
python -m bfcl generate --model gpt-4o-mini-2024-07-18-FC

# 设置所有参数
python -m bfcl generate \
    --model claude-3-5-sonnet-20241022-FC \
    --temperature 0.8 \
    --top-k 50 \
    --top-p 0.9 \
    --test-category simple
```

### 编程接口
```python
from bfcl._llm_response_generation import build_handler

# 创建带有自定义参数的处理器
handler = build_handler(
    model_name="claude-3-5-sonnet-20241022-FC",
    temperature=0.7,
    top_k=50,
    top_p=0.9
)
```

## 测试验证

- 所有更新的处理器都通过了参数传递测试
- 验证了向后兼容性
- 确认了命令行接口的正确性

## 文件清单

### 修改的文件
1. `bfcl/__main__.py` - 命令行接口
2. `bfcl/model_handler/base_handler.py` - 基础处理器
3. `bfcl/_llm_response_generation.py` - 生成函数
4. 多个模型处理器文件（详见上述列表）

### 新增的文件
1. `example_usage.md` - 使用示例文档
2. `PARAMETER_SUPPORT_SUMMARY.md` - 本总结文档

## 注意事项

1. **参数验证**: 系统会自动处理参数的有效性
2. **API 限制**: 不同模型 API 对参数的支持程度不同
3. **性能影响**: 某些参数组合可能影响推理速度
4. **默认行为**: 未指定参数时使用各自的默认值

## 未来改进

1. 可以考虑添加参数范围验证
2. 可以添加更多采样策略的支持
3. 可以为不同任务类型提供预设参数组合

---

此实现为用户提供了对模型输出的精细控制，同时保持了系统的易用性和向后兼容性。
