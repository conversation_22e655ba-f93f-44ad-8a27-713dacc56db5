# 使用 Temperature、Top-k、Top-p 参数控制模型输出

本文档展示了如何使用新添加的 temperature、top-k、top-p 参数来控制大模型的输出。

## 参数说明

### Temperature (温度)
- **范围**: 0.0 - 2.0
- **默认值**: 0.001
- **作用**: 控制输出的随机性
  - 较低的值 (0.1-0.3): 输出更确定、一致
  - 较高的值 (0.7-1.0): 输出更有创造性、多样性

### Top-k
- **范围**: 正整数，-1 表示禁用
- **默认值**: -1 (禁用)
- **作用**: 限制每步只考虑概率最高的 k 个词汇
  - 较小的值 (10-50): 输出更集中、保守
  - 较大的值 (100+): 输出更多样

### Top-p (核采样)
- **范围**: 0.0 - 1.0
- **默认值**: 1.0
- **作用**: 动态选择累积概率达到 p 的词汇集合
  - 较小的值 (0.1-0.5): 输出更集中
  - 较大的值 (0.8-0.95): 输出更多样

## 使用示例

### 基本用法

```bash
# 使用默认参数
python -m bfcl generate --model gpt-4o-mini-2024-07-18-FC

# 设置温度参数
python -m bfcl generate --model gpt-4o-mini-2024-07-18-FC --temperature 0.7

# 同时设置多个参数
python -m bfcl generate \
    --model gpt-4o-mini-2024-07-18-FC \
    --temperature 0.8 \
    --top-k 50 \
    --top-p 0.9
```

### 不同场景的推荐设置

#### 1. 精确任务 (如代码生成、数学计算)
```bash
python -m bfcl generate \
    --model gpt-4o-mini-2024-07-18-FC \
    --temperature 0.1 \
    --top-k 10 \
    --top-p 0.3
```

#### 2. 创造性任务 (如文本生成、头脑风暴)
```bash
python -m bfcl generate \
    --model gpt-4o-mini-2024-07-18-FC \
    --temperature 0.9 \
    --top-k 100 \
    --top-p 0.95
```

#### 3. 平衡设置 (通用任务)
```bash
python -m bfcl generate \
    --model gpt-4o-mini-2024-07-18-FC \
    --temperature 0.7 \
    --top-k 50 \
    --top-p 0.9
```

### 多模型比较

```bash
# 比较不同参数设置对同一模型的影响
python -m bfcl generate \
    --model gpt-4o-mini-2024-07-18-FC,claude-3-5-sonnet-20241022-FC \
    --temperature 0.5 \
    --top-k 40 \
    --top-p 0.8 \
    --test-category simple
```

## 支持的模型

以下模型类型支持这些参数：

### 完全支持 (temperature, top-k, top-p)
- **Claude 模型**: claude-3-5-sonnet-20241022-FC, claude-3-opus-20240229 等
- **Gemini 模型**: gemini-1.5-pro-002-FC 等
- **Amazon Nova 模型**: nova-pro-v1, nova-lite-v1 等
- **Cohere 模型**: command-a-03-2025-FC 等
- **本地模型**: 通过 vLLM/SGLang 部署的开源模型

### 部分支持 (temperature, top-p)
- **OpenAI 模型**: gpt-4o-mini-2024-07-18-FC, gpt-4o-2024-08-06-FC 等
  - 注意: OpenAI API 不支持 top-k 参数

### 特殊情况
- **推理模型**: o1-preview, o1-mini, o3-mini 等不支持 temperature 参数

## 注意事项

1. **参数兼容性**: 不同的模型 API 对参数的支持程度不同
2. **默认值**: 如果不指定参数，将使用各自的默认值
3. **参数验证**: 系统会自动验证参数范围的有效性
4. **性能影响**: 某些参数组合可能会影响推理速度

## 故障排除

如果遇到参数相关的错误：

1. 检查模型是否支持指定的参数
2. 确认参数值在有效范围内
3. 查看模型的 API 文档了解具体限制
4. 尝试使用默认值进行测试

## 更多信息

- 查看支持的模型列表: `python -m bfcl models`
- 查看测试类别: `python -m bfcl test-categories`
- 获取帮助: `python -m bfcl generate --help`
