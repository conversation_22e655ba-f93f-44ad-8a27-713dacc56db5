{"idx": 58, "ground_truth": ["get_weather_data(coordinates=[90.00, 0.00])"], "execution_result": [-0.4], "execution_result_type": ["structural_match"]}
{"idx": 36, "ground_truth": ["get_coordinates_from_city(city_name='Paris')"], "execution_result": [["48.8588897", "2.3200410217200766"]], "execution_result_type": ["exact_match"]}
{"idx": 22, "ground_truth": ["convert_currency(amount=5000, from_currency='EUR', to_currency='JPY')"], "execution_result": [830542.59265], "execution_result_type": ["structural_match"]}
{"idx": 26, "ground_truth": ["find_term_on_urban_dictionary(term='lit')"], "execution_result": ["When [something] is [turned up] or [popping] ..."], "execution_result_type": ["exact_match"]}
{"idx": 34, "ground_truth": ["get_coordinate_by_ip_address(ip_address='***********')"], "execution_result": ["private range"], "execution_result_type": ["exact_match"]}
{"idx": 60, "ground_truth": ["get_zipcode_by_ip_address(ip_address='***********')"], "execution_result": ["private range"], "execution_result_type": ["exact_match"]}
{"idx": 38, "ground_truth": ["get_covid_death_by_country(country='Brazil')"], "execution_result": [711380], "execution_result_type": ["real_time_match"]}
{"idx": 30, "ground_truth": ["get_active_covid_case_by_country(country='Brazil')"], "execution_result": [1783377], "execution_result_type": ["structural_match"]}
{"idx": 50, "ground_truth": ["get_rating_by_amazon_ASIN(ASIN='B08PPDJWC8')"], "execution_result": ["4.1"], "execution_result_type": ["structural_match"]}
{"idx": 50, "ground_truth": ["get_rating_by_amazon_ASIN(ASIN='B07ZPKBL9V')"], "execution_result": ["4.3"], "execution_result_type": ["structural_match"]}
{"idx": 50, "ground_truth": ["get_rating_by_amazon_ASIN(ASIN='B08BHXG144')"], "execution_result": ["4.3"], "execution_result_type": ["structural_match"]}
{"idx": 50, "ground_truth": ["get_rating_by_amazon_ASIN(ASIN='B075H2B962')"], "execution_result": ["4.5"], "execution_result_type": ["structural_match"]}
{"idx": 44, "ground_truth": ["get_price_by_amazon_ASIN(ASIN='B08PPDJWC8')"], "execution_result": ["$220.00"], "execution_result_type": ["structural_match"]}
{"idx": 44, "ground_truth": ["get_price_by_amazon_ASIN(ASIN='B07ZPKBL9V')"], "execution_result": ["$215.00"], "execution_result_type": ["structural_match"]}
{"idx": 44, "ground_truth": ["get_price_by_amazon_ASIN(ASIN='B08BHXG144')"], "execution_result": ["$360.00"], "execution_result_type": ["structural_match"]}
{"idx": 44, "ground_truth": ["get_price_by_amazon_ASIN(ASIN='B075H2B962')"], "execution_result": ["$7.99"], "execution_result_type": ["structural_match"]}
{"idx": 48, "ground_truth": ["get_product_name_by_amazon_ASIN(ASIN='B08PPDJWC8')"], "execution_result": ["Apple iPhone 12 Mini, 64GB, Black - Unlocked (Renewed)"], "execution_result_type": ["exact_match"]}
{"idx": 48, "ground_truth": ["get_product_name_by_amazon_ASIN(ASIN='B07ZPKBL9V')"], "execution_result": ["Apple iPhone 11, 64GB, (PRODUCT)RED - Fully Unlocked (Renewed)"], "execution_result_type": ["exact_match"]}
{"idx": 48, "ground_truth": ["get_product_name_by_amazon_ASIN(ASIN='B08BHXG144')"], "execution_result": ["Apple iPhone 11 Pro Max, 64GB, Midnight Green - Unlocked (Renewed Premium)"], "execution_result_type": ["exact_match"]}
{"idx": 48, "ground_truth": ["get_product_name_by_amazon_ASIN(ASIN='B075H2B962')"], "execution_result": ["Aloderma 99% Organic Aloe Vera Gel Made within 12 Hours of Harvest, Refreshing Travel Size Aloe Vera Gel for Face & Body, Cooling, Soothing Instant Relief for Skin & Sunburn, Hydrating Aloe Gel, 1.5oz"], "execution_result_type": ["exact_match"]}
{"idx": 32, "ground_truth": ["get_company_name_by_stock_name(stock_name='AAPL')"], "execution_result": ["Apple Inc."], "execution_result_type": ["exact_match"]}
{"idx": 54, "ground_truth": ["get_stock_price_by_stock_name(stock_name='AAPL')"], "execution_result": [169.02], "execution_result_type": ["structural_match"]}
{"idx": 52, "ground_truth": ["get_stock_history(stock_name='AAPL', interval='1mo', diffandsplits='true')"], "execution_result": [{"1690862400": {"date": "01-08-2023", "date_utc": 1690862400, "open": 196.24, "high": 196.73, "low": 171.96, "close": 187.87, "volume": 1322439400, "adjclose": 187.13}, "1693540800": {"date": "01-09-2023", "date_utc": 1693540800, "open": 189.49, "high": 189.98, "low": 167.62, "close": 171.21, "volume": 1337586600, "adjclose": 170.77}, "1696132800": {"date": "01-10-2023", "date_utc": 1696132800, "open": 171.22, "high": 182.34, "low": 165.67, "close": 170.77, "volume": 1172719600, "adjclose": 170.33}, "1698811200": {"date": "01-11-2023", "date_utc": 1698811200, "open": 171, "high": 192.93, "low": 170.12, "close": 189.95, "volume": 1099586100, "adjclose": 189.46}, "1701406800": {"date": "01-12-2023", "date_utc": 1701406800, "open": 190.33, "high": 199.62, "low": 187.45, "close": 192.53, "volume": 1062774800, "adjclose": 192.28}, "1704085200": {"date": "01-01-2024", "date_utc": 1704085200, "open": 187.15, "high": 196.38, "low": 180.17, "close": 184.4, "volume": 1187219300, "adjclose": 184.16}, "1706763600": {"date": "01-02-2024", "date_utc": 1706763600, "open": 183.99, "high": 191.05, "low": 179.25, "close": 180.75, "volume": 1161627000, "adjclose": 180.52}, "1709269200": {"date": "01-03-2024", "date_utc": 1709269200, "open": 179.55, "high": 180.53, "low": 168.49, "close": 171.48, "volume": 1432782800, "adjclose": 171.48}, "1711944000": {"date": "01-04-2024", "date_utc": 1711944000, "open": 171.19, "high": 178.36, "low": 164.08, "close": 169.02, "volume": 1016085600, "adjclose": 169.02}, "1713988801": {"date": "24-04-2024", "date_utc": 1713988801, "open": 166.4, "high": 169.3, "low": 166.21, "close": 169.02, "volume": 47315677, "adjclose": 169.02}}], "execution_result_type": ["structural_match"]}
{"idx": 74, "ground_truth": ["retrieve_city_based_on_zipcode(zipcode='90210')"], "execution_result": ["BEVERLY HILLS"], "execution_result_type": ["exact_match"]}
{"idx": 76, "ground_truth": ["retrieve_holiday_by_year(year='2010', country='FR')"], "execution_result": [[{"date": "2010-01-01", "localName": "Jour de l'an", "name": "New Year's Day", "countryCode": "FR", "fixed": false, "global": true, "counties": null, "launchYear": null, "types": ["Public"]}, {"date": "2010-04-05", "localName": "Lundi de P\u00e2ques", "name": "Easter Monday", "countryCode": "FR", "fixed": false, "global": true, "counties": null, "launchYear": null, "types": ["Public"]}, {"date": "2010-05-01", "localName": "F\u00eate du Travail", "name": "Labour Day", "countryCode": "FR", "fixed": false, "global": true, "counties": null, "launchYear": null, "types": ["Public"]}, {"date": "2010-05-08", "localName": "Victoire 1945", "name": "Victory in Europe Day", "countryCode": "FR", "fixed": false, "global": true, "counties": null, "launchYear": null, "types": ["Public"]}, {"date": "2010-05-13", "localName": "Ascension", "name": "Ascension Day", "countryCode": "FR", "fixed": false, "global": true, "counties": null, "launchYear": null, "types": ["Public"]}, {"date": "2010-05-24", "localName": "Lundi de Pentec\u00f4te", "name": "Whit Monday", "countryCode": "FR", "fixed": false, "global": true, "counties": null, "launchYear": null, "types": ["Public"]}, {"date": "2010-07-14", "localName": "F\u00eate nationale", "name": "Bastille Day", "countryCode": "FR", "fixed": false, "global": true, "counties": null, "launchYear": null, "types": ["Public"]}, {"date": "2010-08-15", "localName": "Assomption", "name": "Assumption Day", "countryCode": "FR", "fixed": false, "global": true, "counties": null, "launchYear": null, "types": ["Public"]}, {"date": "2010-11-01", "localName": "Toussaint", "name": "All Saints' Day", "countryCode": "FR", "fixed": false, "global": true, "counties": null, "launchYear": null, "types": ["Public"]}, {"date": "2010-11-11", "localName": "Armistice 1918", "name": "Armistice Day", "countryCode": "FR", "fixed": false, "global": true, "counties": null, "launchYear": null, "types": ["Public"]}, {"date": "2010-12-25", "localName": "No\u00ebl", "name": "Christmas Day", "countryCode": "FR", "fixed": false, "global": true, "counties": null, "launchYear": null, "types": ["Public"]}]], "execution_result_type": ["exact_match"]}
{"idx": 56, "ground_truth": ["get_time_zone_by_coord(long='123.45', lat='-67.89')"], "execution_result": ["Antarctica/DumontDUrville"], "execution_result_type": ["exact_match"]}
{"idx": 96, "ground_truth": ["get_movie_rating(movie_name='Avatar')"], "execution_result": ["PG-13"], "execution_result_type": ["exact_match"]}
{"idx": 94, "ground_truth": ["get_movie_director(movie_name='Avatar')"], "execution_result": ["James Cameron"], "execution_result_type": ["exact_match"]}