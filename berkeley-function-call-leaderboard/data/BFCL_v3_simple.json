{"id": "simple_0", "question": [[{"role": "user", "content": "Find the area of a triangle with a base of 10 units and height of 5 units."}]], "function": [{"name": "calculate_triangle_area", "description": "Calculate the area of a triangle given its base and height.", "parameters": {"type": "dict", "properties": {"base": {"type": "integer", "description": "The base of the triangle."}, "height": {"type": "integer", "description": "The height of the triangle."}, "unit": {"type": "string", "description": "The unit of measure (defaults to 'units' if not specified)"}}, "required": ["base", "height"]}}]}
{"id": "simple_1", "question": [[{"role": "user", "content": "Calculate the factorial of 5 using math functions."}]], "function": [{"name": "math.factorial", "description": "Calculate the factorial of a given number.", "parameters": {"type": "dict", "properties": {"number": {"type": "integer", "description": "The number for which factorial needs to be calculated."}}, "required": ["number"]}}]}
{"id": "simple_2", "question": [[{"role": "user", "content": "Calculate the hypotenuse of a right triangle given the lengths of the other two sides as 4 and 5."}]], "function": [{"name": "math.hypot", "description": "Calculate the Euclidean norm, sqrt(sum(squares)), the length of the vector from the origin to point (x, y) which is the hypotenuse of the right triangle.", "parameters": {"type": "dict", "properties": {"x": {"type": "integer", "description": "The x-coordinate value."}, "y": {"type": "integer", "description": "The y-coordinate value."}, "z": {"type": "integer", "description": "Optional. The z-coordinate value. Default is 0."}}, "required": ["x", "y"]}}]}
{"id": "simple_3", "question": [[{"role": "user", "content": "Find the roots of a quadratic equation with coefficients a=1, b=-3, c=2."}]], "function": [{"name": "algebra.quadratic_roots", "description": "Find the roots of a quadratic equation ax^2 + bx + c = 0.", "parameters": {"type": "dict", "properties": {"a": {"type": "integer", "description": "Coefficient of x^2."}, "b": {"type": "integer", "description": "Coefficient of x."}, "c": {"type": "integer", "description": "Constant term."}}, "required": ["a", "b", "c"]}}]}
{"id": "simple_4", "question": [[{"role": "user", "content": "Solve a quadratic equation where a=2, b=6, and c=5"}]], "function": [{"name": "solve_quadratic_equation", "description": "Function solves the quadratic equation and returns its roots.", "parameters": {"type": "dict", "properties": {"a": {"type": "integer", "description": "Coefficient of x squared"}, "b": {"type": "integer", "description": "Coefficient of x"}, "c": {"type": "integer", "description": "Constant term in the quadratic equation."}}, "required": ["a", "b", "c"]}}]}
{"id": "simple_5", "question": [[{"role": "user", "content": "Find all the roots of a quadratic equation given coefficients a = 3, b = -11, and c = -4."}]], "function": [{"name": "solve_quadratic", "description": "Solve a quadratic equation given coefficients a, b, and c. If optional 'root_type' is 'real', the function will only return real roots. If not specified, function may return complex roots.", "parameters": {"type": "dict", "properties": {"a": {"type": "integer", "description": "The coefficient of the squared term in the quadratic equation."}, "b": {"type": "integer", "description": "The coefficient of the linear term in the quadratic equation."}, "c": {"type": "integer", "description": "The constant term in the quadratic equation."}, "root_type": {"type": "string", "description": "The type of roots to return: 'real' for real roots, 'all' for both real and complex roots. Default value is 'real'."}}, "required": ["a", "b", "c"]}}]}
{"id": "simple_6", "question": [[{"role": "user", "content": "What are the roots of the quadratic equation where a=2, b=5 and c=3 ?"}]], "function": [{"name": "solve_quadratic", "description": "Find the roots of a quadratic equation. Returns both roots.", "parameters": {"type": "dict", "properties": {"a": {"type": "integer", "description": "Coefficient of x\u00b2."}, "b": {"type": "integer", "description": "Coefficient of x."}, "c": {"type": "integer", "description": "Constant term."}}, "required": ["a", "b", "c"]}}]}
{"id": "simple_7", "question": [[{"role": "user", "content": "What is the circumference of a circle with a radius of 4 inches?"}]], "function": [{"name": "calculate_circumference", "description": "Calculates the circumference of a circle with a given radius.", "parameters": {"type": "dict", "properties": {"radius": {"type": "integer", "description": "The radius of the circle in the unit given."}, "unit": {"type": "string", "description": "The unit of measurement for the radius. Default is 'cm'."}}, "required": ["radius"]}}]}
{"id": "simple_8", "question": [[{"role": "user", "content": "What's the area of a circle with a radius of 10?"}]], "function": [{"name": "geometry.area_circle", "description": "Calculate the area of a circle given the radius.", "parameters": {"type": "dict", "properties": {"radius": {"type": "integer", "description": "The radius of the circle."}, "units": {"type": "string", "description": "The units in which the radius is measured (defaults to 'meters')."}}, "required": ["radius"]}}]}
{"id": "simple_9", "question": [[{"role": "user", "content": "Calculate the area of a circle with a radius of 5 units."}]], "function": [{"name": "geometry.calculate_area_circle", "description": "Calculate the area of a circle given its radius.", "parameters": {"type": "dict", "properties": {"radius": {"type": "integer", "description": "The radius of the circle."}, "unit": {"type": "string", "description": "The measurement unit of the radius (optional parameter, default is 'units')."}}, "required": ["radius"]}}]}
{"id": "simple_10", "question": [[{"role": "user", "content": "Calculate the area of a right-angled triangle given the lengths of its base and height as 6cm and 10cm."}]], "function": [{"name": "calculate_area", "description": "Calculate the area of a right-angled triangle given the lengths of its base and height.", "parameters": {"type": "dict", "properties": {"base": {"type": "integer", "description": "The length of the base of the right-angled triangle."}, "height": {"type": "integer", "description": "The height of the right-angled triangle."}, "unit": {"type": "string", "description": "The unit of measure used. Defaults to 'cm'."}}, "required": ["base", "height"]}}]}
{"id": "simple_11", "question": [[{"role": "user", "content": "What is the area of a triangle with base of 10 units and height of 5 units?"}]], "function": [{"name": "calculate_triangle_area", "description": "Calculate the area of a triangle using its base and height.", "parameters": {"type": "dict", "properties": {"base": {"type": "integer", "description": "The length of the base of the triangle."}, "height": {"type": "integer", "description": "The height of the triangle from the base."}}, "required": ["base", "height"]}}]}
{"id": "simple_12", "question": [[{"role": "user", "content": "Calculate the circumference of a circle with radius 3"}]], "function": [{"name": "geometry.circumference", "description": "Calculate the circumference of a circle given the radius.", "parameters": {"type": "dict", "properties": {"radius": {"type": "integer", "description": "The radius of the circle."}, "units": {"type": "string", "description": "Units for the output circumference measurement. Default is 'cm'."}}, "required": ["radius"]}}]}
{"id": "simple_13", "question": [[{"role": "user", "content": "Calculate the area under the curve y=x^2 from x=1 to x=3."}]], "function": [{"name": "calculate_area_under_curve", "description": "Calculate the area under a mathematical function within a given interval.", "parameters": {"type": "dict", "properties": {"function": {"type": "string", "description": "The mathematical function as a string."}, "interval": {"type": "array", "items": {"type": "float"}, "description": "An array that defines the interval to calculate the area under the curve from the start to the end point."}, "method": {"type": "string", "description": "The numerical method to approximate the area under the curve. The default value is 'trapezoidal'."}}, "required": ["function", "interval"]}}]}
{"id": "simple_14", "question": [[{"role": "user", "content": "Calculate the derivative of the function 3x^2 + 2x - 1."}]], "function": [{"name": "calculate_derivative", "description": "Calculate the derivative of a polynomial function.", "parameters": {"type": "dict", "properties": {"function": {"type": "string", "description": "The polynomial function."}, "x_value": {"type": "float", "description": "The x-value at which the derivative is calculated. Optional, default to 0.00."}}, "required": ["function"]}}]}
{"id": "simple_15", "question": [[{"role": "user", "content": "Calculate the area under the curve from x = -2 to x = 3 for the function y = x^3 using simpson method."}]], "function": [{"name": "integrate", "description": "Calculate the area under a curve for a specified function between two x values.", "parameters": {"type": "dict", "properties": {"function": {"type": "string", "description": "The function to integrate, represented as a string. For example, 'x^3'"}, "start_x": {"type": "integer", "description": "The starting x-value to integrate over."}, "end_x": {"type": "integer", "description": "The ending x-value to integrate over."}, "method": {"type": "string", "description": "The method of numerical integration to use. Choices are 'trapezoid' or 'simpson'. Default is 'trapezoid'."}}, "required": ["function", "start_x", "end_x"]}}]}
{"id": "simple_16", "question": [[{"role": "user", "content": "Calculate the derivative of the function 2x^2 at x = 1."}]], "function": [{"name": "calculus.derivative", "description": "Compute the derivative of a function at a specific value.", "parameters": {"type": "dict", "properties": {"function": {"type": "string", "description": "The function to calculate the derivative of."}, "value": {"type": "integer", "description": "The value where the derivative needs to be calculated at."}, "function_variable": {"type": "string", "description": "The variable present in the function, for instance x or y, etc. Default is 'x'."}}, "required": ["function", "value"]}}]}
{"id": "simple_17", "question": [[{"role": "user", "content": "Find the prime factors of 450"}]], "function": [{"name": "get_prime_factors", "description": "Function to retrieve prime factors of a number.", "parameters": {"type": "dict", "properties": {"number": {"type": "integer", "description": "Number for which prime factors need to be calculated"}, "formatted": {"type": "boolean", "description": "Return formatted string if true, array if false. Default is true."}}, "required": ["number", "formatted"]}}]}
{"id": "simple_18", "question": [[{"role": "user", "content": "Find the prime factors of the number 123456."}]], "function": [{"name": "number_analysis.prime_factors", "description": "Compute the prime factors of a number.", "parameters": {"type": "dict", "properties": {"number": {"type": "integer", "description": "The number to be factored."}}, "required": ["number"]}}]}
{"id": "simple_19", "question": [[{"role": "user", "content": "Calculate the greatest common divisor of two numbers: 40 and 50"}]], "function": [{"name": "math.gcd", "description": "Compute the greatest common divisor of two numbers", "parameters": {"type": "dict", "properties": {"num1": {"type": "integer", "description": "The first number."}, "num2": {"type": "integer", "description": "The second number."}}, "required": ["num1", "num2"]}}]}
{"id": "simple_20", "question": [[{"role": "user", "content": "Find the highest common factor of 36 and 24."}]], "function": [{"name": "math.hcf", "description": "Calculate the highest common factor of two numbers.", "parameters": {"type": "dict", "properties": {"number1": {"type": "integer", "description": "First number."}, "number2": {"type": "integer", "description": "Second number."}}, "required": ["number1", "number2"]}}]}
{"id": "simple_21", "question": [[{"role": "user", "content": "Find the Greatest Common Divisor (GCD) of two numbers, say 36 and 48."}]], "function": [{"name": "number_theory.gcd", "description": "Compute the greatest common divisor of two given integers.", "parameters": {"type": "dict", "properties": {"number1": {"type": "integer", "description": "The first integer."}, "number2": {"type": "integer", "description": "The second integer."}}, "required": ["number1", "number2"]}}]}
{"id": "simple_22", "question": [[{"role": "user", "content": "Calculate the greatest common divisor of two given numbers, for example 12 and 15."}]], "function": [{"name": "math.gcd", "description": "Calculate the greatest common divisor (gcd) of the two integers.", "parameters": {"type": "dict", "properties": {"num1": {"type": "integer", "description": "The first number."}, "num2": {"type": "integer", "description": "The second number."}}, "required": ["num1", "num2"]}}]}
{"id": "simple_23", "question": [[{"role": "user", "content": "What is the prime factorization of the number 60? Return them in the form of dictionary"}]], "function": [{"name": "prime_factorize", "description": "Calculate the prime factorization of a given integer.", "parameters": {"type": "dict", "properties": {"number": {"type": "integer", "description": "The number for which to calculate the prime factorization."}, "return_type": {"type": "string", "description": "Determines the format of the returned prime factorization. Can be 'list' for a list of all prime factors or 'dictionary' for a count of each prime factor. Default is 'list'."}}, "required": ["number"]}}]}
{"id": "simple_24", "question": [[{"role": "user", "content": "Find the greatest common divisor (GCD) of 12 and 18"}]], "function": [{"name": "math.gcd", "description": "Calculate the greatest common divisor of two integers.", "parameters": {"type": "dict", "properties": {"num1": {"type": "integer", "description": "First number."}, "num2": {"type": "integer", "description": "Second number."}}, "required": ["num1", "num2"]}}]}
{"id": "simple_25", "question": [[{"role": "user", "content": "Calculate the final velocity of an object falling from a 150 meter building, assuming initial velocity is zero."}]], "function": [{"name": "calculate_final_velocity", "description": "Calculate the final velocity of a free falling object given the height it's dropped from, the initial velocity and acceleration due to gravity. Ignore air resistance.", "parameters": {"type": "dict", "properties": {"height": {"type": "integer", "description": "The height the object is dropped from, in meters."}, "initial_velocity": {"type": "integer", "description": "The initial velocity of the object in m/s. Default is zero."}, "gravity": {"type": "float", "description": "Acceleration due to gravity. Default value is 9.81 m/s^2, earth's gravity."}}, "required": ["height"]}}]}
{"id": "simple_26", "question": [[{"role": "user", "content": "Calculate the velocity of a car that travels a distance of 50 kilometers for a duration of 2 hours?"}]], "function": [{"name": "calculate_velocity", "description": "Calculate the velocity for a certain distance travelled within a specific duration.", "parameters": {"type": "dict", "properties": {"distance": {"type": "integer", "description": "The distance travelled by the object, typically in kilometers."}, "duration": {"type": "integer", "description": "The duration of the journey, typically in hours."}, "unit": {"type": "string", "description": "Optional parameter. The unit to return the velocity in. If not provided, the default is km/h."}}, "required": ["distance", "duration"]}}]}
{"id": "simple_27", "question": [[{"role": "user", "content": "Calculate the final velocity of a vehicle after accelerating at 2 meters/second^2 for a duration of 5 seconds, starting from a speed of 10 meters/second."}]], "function": [{"name": "final_velocity", "description": "Calculate the final velocity of an object given its initial velocity, acceleration, and time.", "parameters": {"type": "dict", "properties": {"initial_velocity": {"type": "integer", "description": "The initial velocity of the object in meters/second."}, "acceleration": {"type": "integer", "description": "The acceleration of the object in meters/second^2."}, "time": {"type": "integer", "description": "The time over which the acceleration is applied in seconds."}}, "required": ["initial_velocity", "acceleration", "time"]}}]}
{"id": "simple_28", "question": [[{"role": "user", "content": "Calculate the displacement of a car given the initial velocity of 10 and acceleeration of 9.8 within 5 seconds."}]], "function": [{"name": "calculate_displacement", "description": "Calculates the displacement of an object in motion given initial velocity, time, and acceleration.", "parameters": {"type": "dict", "properties": {"initial_velocity": {"type": "integer", "description": "The initial velocity of the object in m/s."}, "time": {"type": "integer", "description": "The time in seconds that the object has been in motion."}, "acceleration": {"type": "float", "description": "The acceleration of the object in m/s^2.", "default": 0}}, "required": ["initial_velocity", "time"]}}]}
{"id": "simple_29", "question": [[{"role": "user", "content": "What is the final speed of an object dropped from rest after falling for 5 seconds if we neglect air resistance?"}]], "function": [{"name": "calculate_final_speed", "description": "Calculate the final speed of an object in free fall after a certain time, neglecting air resistance. The acceleration due to gravity is considered as -9.81 m/s^2", "parameters": {"type": "dict", "properties": {"initial_speed": {"type": "integer", "description": "The initial speed of the object in m/s. Default is 0 for an object at rest."}, "time": {"type": "integer", "description": "The time in seconds for which the object is in free fall."}, "gravity": {"type": "float", "description": "The acceleration due to gravity. Default is -9.81 m/s^2."}}, "required": ["time"]}}]}
{"id": "simple_30", "question": [[{"role": "user", "content": "What is the final velocity of a vehicle that started from rest and accelerated at 4 m/s^2 for a distance of 300 meters?"}]], "function": [{"name": "kinematics.final_velocity_from_distance", "description": "Calculate the final velocity of an object given the acceleration and distance travelled, assuming initial velocity is 0.", "parameters": {"type": "dict", "properties": {"acceleration": {"type": "integer", "description": "Acceleration of the object, m/s^2."}, "distance": {"type": "integer", "description": "Distance traveled by the object, m."}, "initial_velocity": {"type": "float", "description": "Initial velocity of the object. Default is 0, m/s"}}, "required": ["acceleration", "distance"]}}]}
{"id": "simple_31", "question": [[{"role": "user", "content": "Calculate the final velocity of an object, knowing that it started from rest, accelerated at a rate of 9.8 m/s^2 for a duration of 5 seconds."}]], "function": [{"name": "calculate_final_velocity", "description": "Calculate the final velocity of an object under constant acceleration, knowing its initial velocity, acceleration, and time of acceleration.", "parameters": {"type": "dict", "properties": {"initial_velocity": {"type": "integer", "description": "The initial velocity of the object."}, "acceleration": {"type": "float", "description": "The acceleration of the object."}, "time": {"type": "integer", "description": "The time of acceleration."}}, "required": ["initial_velocity", "acceleration", "time"]}}]}
{"id": "simple_32", "question": [[{"role": "user", "content": "Calculate the final speed of an object dropped from 100 m without air resistance."}]], "function": [{"name": "calculate_final_speed", "description": "Calculate the final speed of an object dropped from a certain height without air resistance.", "parameters": {"type": "dict", "properties": {"initial_velocity": {"type": "integer", "description": "The initial velocity of the object."}, "height": {"type": "integer", "description": "The height from which the object is dropped."}, "gravity": {"type": "float", "description": "The gravitational acceleration. Default is 9.8 m/s^2."}}, "required": ["initial_velocity", "height"]}}]}
{"id": "simple_33", "question": [[{"role": "user", "content": "Get directions from Sydney to Melbourne using the fastest route."}]], "function": [{"name": "get_directions", "description": "Retrieve directions from one location to another.", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "The starting point of the journey."}, "end_location": {"type": "string", "description": "The destination point of the journey."}, "route_type": {"type": "string", "description": "Type of route to use (e.g., 'fastest', 'scenic'). Default is 'fastest'.", "enum": ["fastest", "scenic"]}}, "required": ["start_location", "end_location"]}}]}
{"id": "simple_34", "question": [[{"role": "user", "content": "Create an itinerary for a 7 days trip to Tokyo with daily budgets not exceeding $100 and prefer exploring nature."}]], "function": [{"name": "travel_itinerary_generator", "description": "Generate a travel itinerary based on specific destination, duration and daily budget, with preferred exploration type.", "parameters": {"type": "dict", "properties": {"destination": {"type": "string", "description": "Destination city of the trip."}, "days": {"type": "integer", "description": "Number of days for the trip."}, "daily_budget": {"type": "integer", "description": "The maximum daily budget for the trip."}, "exploration_type": {"type": "string", "enum": ["nature", "urban", "history", "culture"], "description": "The preferred exploration type.", "default": "urban"}}, "required": ["destination", "days", "daily_budget"]}}]}
{"id": "simple_35", "question": [[{"role": "user", "content": "Find an all vegan restaurant in New York that opens until at least 11 PM."}]], "function": [{"name": "vegan_restaurant.find_nearby", "description": "Locate nearby vegan restaurants based on specific criteria like operating hours.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. New York, NY, you should format it as City, State."}, "operating_hours": {"type": "integer", "description": "Preferred latest closing time of the restaurant. E.g. if 11 is given, then restaurants that close at or after 11 PM will be considered. This is in 24 hour format. Default is 24."}}, "required": ["location"]}}]}
{"id": "simple_36", "question": [[{"role": "user", "content": "Find the shortest driving distance between New York City and Washington D.C."}]], "function": [{"name": "get_shortest_driving_distance", "description": "Calculate the shortest driving distance between two locations.", "parameters": {"type": "dict", "properties": {"origin": {"type": "string", "description": "Starting point of the journey. You should format it as city name like Boston."}, "destination": {"type": "string", "description": "End point of the journey. You should format it as city name like Boston."}, "unit": {"type": "string", "description": "Preferred unit of distance (optional, default is 'km')."}}, "required": ["origin", "destination"]}}]}
{"id": "simple_37", "question": [[{"role": "user", "content": "Find the estimated travel time by car from San Francisco to Los Angeles with stops at Santa Barbara and Monterey."}]], "function": [{"name": "route.estimate_time", "description": "Estimate the travel time for a specific route with optional stops.", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "The starting point for the journey. It should be format as city name such as Boston."}, "end_location": {"type": "string", "description": "The destination for the journey. It should be format as city name such as Boston."}, "stops": {"type": "array", "items": {"type": "string"}, "description": "Additional cities or points of interest to stop at during the journey. Default is an empty list."}}, "required": ["start_location", "end_location"]}}]}
{"id": "simple_38", "question": [[{"role": "user", "content": "What is the electrostatic potential between two charged bodies of 1e-9 and 2e-9 of distance 0.05?"}]], "function": [{"name": "calculate_electrostatic_potential", "description": "Calculate the electrostatic potential between two charged bodies using the principle of Coulomb's Law.", "parameters": {"type": "dict", "properties": {"charge1": {"type": "float", "description": "The quantity of charge on the first body."}, "charge2": {"type": "float", "description": "The quantity of charge on the second body."}, "distance": {"type": "float", "description": "The distance between the two bodies."}, "constant": {"type": "float", "description": "The value of the electrostatic constant. Default is 8.99e9."}}, "required": ["charge1", "charge2", "distance"]}}]}
{"id": "simple_39", "question": [[{"role": "user", "content": "Calculate the electric field at a point 3 meters away from a charge of 2 coulombs."}]], "function": [{"name": "calculate_electric_field", "description": "Calculate the electric field produced by a charge at a certain distance.", "parameters": {"type": "dict", "properties": {"charge": {"type": "integer", "description": "Charge in coulombs producing the electric field."}, "distance": {"type": "integer", "description": "Distance from the charge in meters where the field is being measured."}, "permitivity": {"type": "float", "description": "Permitivity of the space where field is being calculated, default is 8.854e-12."}}, "required": ["charge", "distance"]}}]}
{"id": "simple_40", "question": [[{"role": "user", "content": "Calculate the magnetic field produced at the center of a circular loop carrying current of 5 Ampere with a radius of 4 meters"}]], "function": [{"name": "calculate_magnetic_field", "description": "Calculate the magnetic field produced at the center of a circular loop carrying current.", "parameters": {"type": "dict", "properties": {"current": {"type": "integer", "description": "The current through the circular loop in Amperes."}, "radius": {"type": "integer", "description": "The radius of the circular loop in meters."}, "permeability": {"type": "float", "description": "The magnetic permeability. Default is 12.57e10 (Vacuum Permeability)."}}, "required": ["current", "radius"]}}]}
{"id": "simple_41", "question": [[{"role": "user", "content": "Calculate the electromagnetic force between two charges of 5C and 7C placed 3 meters apart."}]], "function": [{"name": "electromagnetic_force", "description": "Calculate the electromagnetic force between two charges placed at a certain distance.", "parameters": {"type": "dict", "properties": {"charge1": {"type": "integer", "description": "The magnitude of the first charge in coulombs."}, "charge2": {"type": "integer", "description": "The magnitude of the second charge in coulombs."}, "distance": {"type": "integer", "description": "The distance between the two charges in meters."}, "medium_permittivity": {"type": "float", "description": "The relative permittivity of the medium in which the charges are present. Default is 8.854e-12 (Vacuum Permittivity)."}}, "required": ["charge1", "charge2", "distance"]}}]}
{"id": "simple_42", "question": [[{"role": "user", "content": "Calculate the resonant frequency of an LC circuit given capacitance of 100\u00b5F and inductance of 50mH."}]], "function": [{"name": "calculate_resonant_frequency", "description": "Calculate the resonant frequency of an LC (inductor-capacitor) circuit.", "parameters": {"type": "dict", "properties": {"inductance": {"type": "float", "description": "The inductance (L) in henries (H)."}, "capacitance": {"type": "float", "description": "The capacitance (C) in farads (F)."}, "round_off": {"type": "integer", "description": "Rounding off the result to a certain decimal places, default is 2."}}, "required": ["inductance", "capacitance"]}}]}
{"id": "simple_43", "question": [[{"role": "user", "content": "Calculate the magnetic field strength 10 meters away from a long wire carrying a current of 20 Amperes."}]], "function": [{"name": "calculate_magnetic_field_strength", "description": "Calculate the magnetic field strength at a point a certain distance away from a long wire carrying a current.", "parameters": {"type": "dict", "properties": {"current": {"type": "integer", "description": "The current flowing through the wire in Amperes."}, "distance": {"type": "integer", "description": "The perpendicular distance from the wire to the point where the magnetic field is being calculated."}, "permeability": {"type": "float", "description": "The permeability of the medium. Default is 12.57e-7 (Vacuum Permeability)."}}, "required": ["current", "distance"]}}]}
{"id": "simple_44", "question": [[{"role": "user", "content": "Calculate the electric field strength 4 meters away from a charge of 0.01 Coulombs."}]], "function": [{"name": "calculate_electric_field_strength", "description": "Calculate the electric field strength at a certain distance from a point charge.", "parameters": {"type": "dict", "properties": {"charge": {"type": "float", "description": "The charge in Coulombs."}, "distance": {"type": "integer", "description": "The distance from the charge in meters."}, "medium": {"type": "string", "description": "The medium in which the charge and the point of calculation is located. Default is 'vacuum'."}}, "required": ["charge", "distance"]}}]}
{"id": "simple_45", "question": [[{"role": "user", "content": "Calculate the energy (in Joules) absorbed or released during the phase change of 100g of water from liquid to steam at its boiling point."}]], "function": [{"name": "thermo.calculate_energy", "description": "Calculate the energy required or released during a phase change using mass, the phase transition temperature and the specific latent heat.", "parameters": {"type": "dict", "properties": {"mass": {"type": "integer", "description": "Mass of the substance in grams."}, "phase_transition": {"type": "string", "description": "Phase transition. Can be 'melting', 'freezing', 'vaporization', 'condensation'."}, "substance": {"type": "string", "description": "The substance which is undergoing phase change, default is 'water'"}}, "required": ["mass", "phase_transition"]}}]}
{"id": "simple_46", "question": [[{"role": "user", "content": "Calculate the final temperature when 20 kg of water at 30 degree Celsius is mixed with 15 kg of water at 60 degree Celsius."}]], "function": [{"name": "calculate_final_temperature", "description": "Calculates the final equilibrium temperature after mixing two bodies with different masses and temperatures", "parameters": {"type": "dict", "properties": {"mass1": {"type": "integer", "description": "The mass of the first body (kg)."}, "temperature1": {"type": "integer", "description": "The initial temperature of the first body (Celsius)."}, "mass2": {"type": "integer", "description": "The mass of the second body (kg)."}, "temperature2": {"type": "integer", "description": "The initial temperature of the second body (Celsius)."}, "specific_heat_capacity": {"type": "float", "description": "The specific heat capacity of the bodies in kJ/kg/K. If not provided, will default to that of water at room temperature, which is 4.2 kJ/kg/K."}}, "required": ["mass1", "temperature1", "mass2", "temperature2"]}}]}
{"id": "simple_47", "question": [[{"role": "user", "content": "Find the boiling point and melting point of water under the sea level of 5000m."}]], "function": [{"name": "get_boiling_melting_points", "description": "Retrieve the boiling point and melting point of a substance based on its name and the sea level.", "parameters": {"type": "dict", "properties": {"substance": {"type": "string", "description": "The name of the substance."}, "sea_level": {"type": "integer", "description": "The sea level in meters."}}, "required": ["substance", "sea_level"]}}]}
{"id": "simple_48", "question": [[{"role": "user", "content": "What is the density of a substance with a mass of 45 kg and a volume of 15 m\u00b3?"}]], "function": [{"name": "calculate_density", "description": "Calculate the density of a substance based on its mass and volume.", "parameters": {"type": "dict", "properties": {"mass": {"type": "integer", "description": "The mass of the substance in kilograms."}, "volume": {"type": "integer", "description": "The volume of the substance in cubic meters."}, "unit": {"type": "string", "description": "The unit of density. Default is kg/m\u00b3"}}, "required": ["mass", "volume"]}}]}
{"id": "simple_49", "question": [[{"role": "user", "content": "Calculate the absolute pressure in pascals given atmospheric pressure of 1 atm and a gauge pressure of 2 atm."}]], "function": [{"name": "calc_absolute_pressure", "description": "Calculates the absolute pressure from gauge and atmospheric pressures.", "parameters": {"type": "dict", "properties": {"atm_pressure": {"type": "integer", "description": "The atmospheric pressure in atmospheres (atm). Default is 1 atm if not provided."}, "gauge_pressure": {"type": "integer", "description": "The gauge pressure in atmospheres (atm). Must be provided."}}, "required": ["gauge_pressure"]}}]}
{"id": "simple_50", "question": [[{"role": "user", "content": "What is the change in entropy in Joules per Kelvin of a 1kg ice block at 0\u00b0C if it is heated to 100\u00b0C under 1 atmosphere of pressure?"}]], "function": [{"name": "entropy_change.calculate", "description": "Calculate the change in entropy for a mass of a specific substance under set initial and final conditions.", "parameters": {"type": "dict", "properties": {"substance": {"type": "string", "description": "The substance for which the change in entropy is calculated."}, "mass": {"type": "integer", "description": "The mass of the substance in kg."}, "initial_temperature": {"type": "integer", "description": "The initial temperature of the substance in degree Celsius."}, "final_temperature": {"type": "integer", "description": "The final temperature of the substance in degree Celsius."}, "pressure": {"type": "integer", "default": 1, "description": "The pressure the substance is under in atmospheres."}}, "required": ["substance", "mass", "initial_temperature", "final_temperature"]}}]}
{"id": "simple_51", "question": [[{"role": "user", "content": "Calculate the entropy change for a certain process given an initial temperature of 300K, a final temperature of 400K, and a heat capacity of 5J/K."}]], "function": [{"name": "calculate_entropy_change", "description": "Calculate the entropy change for an isothermal and reversible process.", "parameters": {"type": "dict", "properties": {"initial_temp": {"type": "integer", "description": "The initial temperature in Kelvin."}, "final_temp": {"type": "integer", "description": "The final temperature in Kelvin."}, "heat_capacity": {"type": "integer", "description": "The heat capacity in J/K."}, "isothermal": {"type": "boolean", "description": "Whether the process is isothermal. Default is True."}}, "required": ["initial_temp", "final_temp", "heat_capacity"]}}]}
{"id": "simple_52", "question": [[{"role": "user", "content": "Calculate the heat capacity at constant pressure for air, given its temperature is 298K and volume is 10 m^3."}]], "function": [{"name": "calc_heat_capacity", "description": "Calculate the heat capacity at constant pressure of air using its temperature and volume.", "parameters": {"type": "dict", "properties": {"temp": {"type": "integer", "description": "The temperature of the gas in Kelvin."}, "volume": {"type": "integer", "description": "The volume of the gas in m^3."}, "gas": {"type": "string", "description": "Type of gas, with 'air' as default."}}, "required": ["temp", "volume"]}}]}
{"id": "simple_53", "question": [[{"role": "user", "content": "Retrieve the sequence of DNA molecule with id `DNA123`."}]], "function": [{"name": "fetch_DNA_sequence", "description": "Retrieve the sequence of a DNA molecule with the given id from a public database.", "parameters": {"type": "dict", "properties": {"DNA_id": {"type": "string", "description": "Unique ID of the DNA molecule in the database."}, "format": {"type": "string", "description": "Optional parameter to get sequence in specific format (default to 'fasta')."}, "upstream": {"type": "integer", "description": "Optional parameter to include certain number of base pairs upstream the DNA sequence (default to 0)."}}, "required": ["DNA_id"]}}]}
{"id": "simple_54", "question": [[{"role": "user", "content": "Identify the protein sequence of a given human gene 'BRCA1'."}]], "function": [{"name": "get_protein_sequence", "description": "Retrieve the protein sequence encoded by a human gene.", "parameters": {"type": "dict", "properties": {"gene": {"type": "string", "description": "The human gene of interest."}, "species": {"type": "string", "description": "The species for which the gene is to be analyzed.", "default": "Homo sapiens"}}, "required": ["gene"]}}]}
{"id": "simple_55", "question": [[{"role": "user", "content": "Find me detailed information about the structure of human cell"}]], "function": [{"name": "biology.get_cell_info", "description": "Retrieve information about the structure and functioning of a specified type of cell", "parameters": {"type": "dict", "properties": {"cell_type": {"type": "string", "description": "Type of cell you want information about"}, "detailed": {"type": "boolean", "description": "Indicate if you want a detailed description of the cell", "default": "false"}}, "required": ["cell_type"]}}]}
{"id": "simple_56", "question": [[{"role": "user", "content": "What are the names of proteins found in the plasma membrane?"}]], "function": [{"name": "cellbio.get_proteins", "description": "Get the list of proteins in a specific cell compartment.", "parameters": {"type": "dict", "properties": {"cell_compartment": {"type": "string", "description": "The specific cell compartment."}, "include_description": {"type": "boolean", "description": "Set true if you want a brief description of each protein.", "default": "false"}}, "required": ["cell_compartment"]}}]}
{"id": "simple_57", "question": [[{"role": "user", "content": "Calculate the cell density in a sample with an optical density of 0.6, where the experiment dilution is 5 times."}]], "function": [{"name": "calculate_cell_density", "description": "Calculate the cell density of a biological sample based on its optical density and the experiment dilution.", "parameters": {"type": "dict", "properties": {"optical_density": {"type": "float", "description": "The optical density of the sample, usually obtained from a spectrophotometer reading."}, "dilution": {"type": "integer", "description": "The dilution factor applied during the experiment."}, "calibration_factor": {"type": "float", "description": "The calibration factor to adjust the density, default value is 1e9 assuming cell density is in CFU/mL."}}, "required": ["optical_density", "dilution"]}}]}
{"id": "simple_58", "question": [[{"role": "user", "content": "What is the function of ATP synthase in mitochondria?"}]], "function": [{"name": "cell_biology.function_lookup", "description": "Look up the function of a given molecule in a specified organelle.", "parameters": {"type": "dict", "properties": {"molecule": {"type": "string", "description": "The molecule of interest."}, "organelle": {"type": "string", "description": "The organelle of interest."}, "specific_function": {"type": "boolean", "description": "If set to true, a specific function of the molecule within the organelle will be provided, if such information exists."}}, "required": ["molecule", "organelle", "specific_function"]}}]}
{"id": "simple_59", "question": [[{"role": "user", "content": "Calculate the molecular weight of Glucose (C6H12O6) in grams/mole."}]], "function": [{"name": "calculate_molecular_weight", "description": "Calculate the molecular weight of a compound given the compound formula.", "parameters": {"type": "dict", "properties": {"compound": {"type": "string", "description": "The molecular formula of the compound."}, "to_unit": {"type": "string", "description": "The unit in which to return the result."}}, "required": ["compound", "to_unit"]}}]}
{"id": "simple_60", "question": [[{"role": "user", "content": "Find the type of gene mutation based on SNP (Single Nucleotide Polymorphism) ID rs6034464."}]], "function": [{"name": "mutation_type.find", "description": "Finds the type of a genetic mutation based on its SNP (Single Nucleotide Polymorphism) ID.", "parameters": {"type": "dict", "properties": {"snp_id": {"type": "string", "description": "The ID of the Single Nucleotide Polymorphism (SNP) mutation."}, "species": {"type": "string", "description": "Species in which the SNP occurs, default is 'Homo sapiens' (Humans)."}}, "required": ["snp_id"]}}]}
{"id": "simple_61", "question": [[{"role": "user", "content": "Predict whether a person with weight 150lbs and height 5ft 10in who is lightly active will get type 2 diabetes."}]], "function": [{"name": "diabetes_prediction", "description": "Predict the likelihood of diabetes type 2 based on a person's weight and height.", "parameters": {"type": "dict", "properties": {"weight": {"type": "integer", "description": "Weight of the person in lbs."}, "height": {"type": "integer", "description": "Height of the person in inches."}, "activity_level": {"type": "string", "enum": ["sedentary", "lightly active", "moderately active", "very active", "extra active"], "description": "Physical activity level of the person."}}, "required": ["weight", "height", "activity_level"]}}]}
{"id": "simple_62", "question": [[{"role": "user", "content": "Analyze the DNA sequence 'AGTCGATCGAACGTACGTACG' for any potential substitution mutations based on a reference sequence 'AGTCCATCGAACGTACGTACG'."}]], "function": [{"name": "analyze_dna_sequence", "description": "Analyzes the DNA sequence based on a reference sequence and return any potential mutations.", "parameters": {"type": "dict", "properties": {"sequence": {"type": "string", "description": "The DNA sequence to be analyzed."}, "reference_sequence": {"type": "string", "description": "The reference DNA sequence."}, "mutation_type": {"type": "string", "enum": ["insertion", "deletion", "substitution"], "description": "Type of the mutation to be looked for in the sequence. Default to 'substitution'."}}, "required": ["sequence", "reference_sequence"]}}]}
{"id": "simple_63", "question": [[{"role": "user", "content": "Find out how genetically similar a human and a chimp are in percentage."}]], "function": [{"name": "genetics.calculate_similarity", "description": "Calculates the genetic similarity between two species based on their DNA sequences.", "parameters": {"type": "dict", "properties": {"species1": {"type": "string", "description": "The first species to compare."}, "species2": {"type": "string", "description": "The second species to compare."}, "format": {"type": "string", "description": "The format of the result (percentage or fraction). Default is percentage."}}, "required": ["species1", "species2"]}}]}
{"id": "simple_64", "question": [[{"role": "user", "content": "What is the genotype frequency of AA genotype in a population, given that allele frequency of A is 0.3?"}]], "function": [{"name": "calculate_genotype_frequency", "description": "Calculate the frequency of homozygous dominant genotype based on the allele frequency using Hardy Weinberg Principle.", "parameters": {"type": "dict", "properties": {"allele_frequency": {"type": "float", "description": "The frequency of the dominant allele in the population."}, "genotype": {"type": "string", "description": "The genotype which frequency is needed.", "enum": ["AA", "Aa", "aa"]}}, "required": ["allele_frequency", "genotype"]}}]}
{"id": "simple_65", "question": [[{"role": "user", "content": "Calculate the Population Density for Brazil in 2022 if the population is 213 million and the land area is 8.5 million square kilometers."}]], "function": [{"name": "calculate_density", "description": "Calculate the population density of a specific country in a specific year.", "parameters": {"type": "dict", "properties": {"country": {"type": "string", "description": "The country for which the density needs to be calculated."}, "year": {"type": "string", "description": "The year in which the density is to be calculated."}, "population": {"type": "integer", "description": "The population of the country."}, "land_area": {"type": "integer", "description": "The land area of the country in square kilometers."}}, "required": ["country", "year", "population", "land_area"]}}]}
{"id": "simple_66", "question": [[{"role": "user", "content": "Get me data on average precipitation in the Amazon rainforest for the last six months."}]], "function": [{"name": "ecology_data.precipitation_stats", "description": "Retrieve precipitation data for a specified location and time period.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The name of the location, e.g., 'Amazon rainforest'."}, "time_frame": {"type": "string", "enum": ["six_months", "year", "five_years"], "description": "The time period for which data is required."}}, "required": ["location", "time_frame"]}}]}
{"id": "simple_67", "question": [[{"role": "user", "content": "Identify a small green bird in forest."}]], "function": [{"name": "identify_bird", "description": "Identify a bird species based on certain characteristics.", "parameters": {"type": "dict", "properties": {"color": {"type": "string", "description": "Color of the bird."}, "habitat": {"type": "string", "description": "Habitat of the bird."}, "size": {"type": "string", "enum": ["small", "medium", "large"], "description": "Size of the bird. Default is 'small'"}}, "required": ["color", "habitat"]}}]}
{"id": "simple_68", "question": [[{"role": "user", "content": "Predict the growth of forest in Yellowstone National Park for the next 5 years including human impact."}]], "function": [{"name": "forest_growth_forecast", "description": "Predicts the forest growth over the next N years based on current trends.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location where you want to predict forest growth."}, "years": {"type": "integer", "description": "The number of years for the forecast."}, "include_human_impact": {"type": "boolean", "description": "Whether or not to include the impact of human activities in the forecast. If not provided, defaults to false."}}, "required": ["location", "years"]}}]}
{"id": "simple_69", "question": [[{"role": "user", "content": "Find out the population and species of turtles in Mississippi river in 2020."}]], "function": [{"name": "ecology.get_turtle_population", "description": "Get the population and species of turtles in a specific location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The name of the location."}, "year": {"type": "integer", "description": "The year of the data requested. Default is 2001."}, "species": {"type": "boolean", "description": "Whether to include species information. Default is false."}}, "required": ["location"]}}]}
{"id": "simple_70", "question": [[{"role": "user", "content": "What is the carbon footprint of a gas-powered vehicle driving 1500 miles in a year?"}]], "function": [{"name": "calculate_vehicle_emission", "description": "Calculate the annual carbon emissions produced by a specific type of vehicle based on mileage.", "parameters": {"type": "dict", "properties": {"vehicle_type": {"type": "string", "description": "The type of vehicle. 'gas' refers to a gasoline vehicle, 'diesel' refers to a diesel vehicle, and 'EV' refers to an electric vehicle."}, "miles_driven": {"type": "integer", "description": "The number of miles driven per year."}, "emission_factor": {"type": "float", "description": "Optional emission factor to calculate emissions, in g/mile. Default factor is 355.48."}}, "required": ["vehicle_type", "miles_driven"]}}]}
{"id": "simple_71", "question": [[{"role": "user", "content": "Generate a DNA sequence with 100 bases including more G (Guanine) and C (Cytosine)."}]], "function": [{"name": "generate_DNA_sequence", "description": "Generate a random DNA sequence with a specific length and nucleotide preference.", "parameters": {"type": "dict", "properties": {"length": {"type": "integer", "description": "The length of the DNA sequence to be generated."}, "preferences": {"type": "array", "items": {"type": "string", "enum": ["A", "T", "C", "G"]}, "description": "Preferred nucleotides to include more frequently in the DNA sequence."}}, "required": ["length", "preferences"]}}]}
{"id": "simple_72", "question": [[{"role": "user", "content": "Calculate the expected evolutionary fitness of a creature, with trait A contributing to 40% of the fitness and trait B contributing 60%, if trait A has a value of 0.8 and trait B a value of 0.7."}]], "function": [{"name": "calculate_fitness", "description": "Calculate the expected evolutionary fitness of a creature based on the individual values and contributions of its traits.", "parameters": {"type": "dict", "properties": {"trait_values": {"type": "array", "items": {"type": "float"}, "description": "List of trait values, which are decimal numbers between 0 and 1, where 1 represents the trait maximally contributing to fitness."}, "trait_contributions": {"type": "array", "items": {"type": "float"}, "description": "List of the percentage contributions of each trait to the overall fitness, which must sum to 1."}}, "required": ["trait_values", "trait_contributions"]}}]}
{"id": "simple_73", "question": [[{"role": "user", "content": "What's the projected population growth in United States in the next 20 years?"}]], "function": [{"name": "population_projections", "description": "Calculates the projected population growth based on the current growth rate.", "parameters": {"type": "dict", "properties": {"country": {"type": "string", "description": "The country for which to calculate the population projection."}, "years": {"type": "integer", "description": "Number of years for the projection."}, "growth_rate": {"type": "float", "description": "Optional parameter to specify the growth rate, in percentage. Default is 1.2."}}, "required": ["country", "years"]}}]}
{"id": "simple_74", "question": [[{"role": "user", "content": "Calculate the evolution rate of a bacteria population, start with 5000 bacteria, each bacteria duplicates every hour for 6 hours."}]], "function": [{"name": "calculate_bacteria_evolution_rate", "description": "Calculate the evolution rate of bacteria given the starting number, duplication frequency and total duration.", "parameters": {"type": "dict", "properties": {"start_population": {"type": "integer", "description": "The starting population of bacteria."}, "duplication_frequency": {"type": "integer", "description": "The frequency of bacteria duplication per hour."}, "duration": {"type": "integer", "description": "Total duration in hours."}, "generation_time": {"type": "integer", "description": "The average generation time of the bacteria in minutes. Default is 20 minutes"}}, "required": ["start_population", "duplication_frequency", "duration"]}}]}
{"id": "simple_75", "question": [[{"role": "user", "content": "Estimate the population size of elephants of 35000 in the next 5 years given the current growth rate of 0.015."}]], "function": [{"name": "elephant_population_estimate", "description": "Estimate future population of elephants given current population and growth rate.", "parameters": {"type": "dict", "properties": {"current_population": {"type": "integer", "description": "The current number of elephants."}, "growth_rate": {"type": "float", "description": "The annual population growth rate of elephants."}, "years": {"type": "integer", "description": "The number of years to project the population."}}, "required": ["current_population", "growth_rate", "years"]}}]}
{"id": "simple_76", "question": [[{"role": "user", "content": "Get me the predictions of the evolutionary rate for Homo Sapiens for next 50 years using Darwin model"}]], "function": [{"name": "prediction.evolution", "description": "Predict the evolutionary rate for a specific species for a given timeframe.", "parameters": {"type": "dict", "properties": {"species": {"type": "string", "description": "The species that the evolution rate will be predicted for."}, "years": {"type": "integer", "description": "Number of years for the prediction."}, "model": {"type": "string", "description": "The model used to make the prediction, options: 'Darwin', 'Lamarck', default is 'Darwin'."}}, "required": ["species", "years"]}}]}
{"id": "simple_77", "question": [[{"role": "user", "content": "Find a nearby restaurant that serves vegan food in Los Angeles."}]], "function": [{"name": "restaurant.find_nearby", "description": "Locate nearby restaurants based on specific dietary preferences.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Los Angeles, CA"}, "dietary_preference": {"type": "array", "items": {"type": "string", "enum": ["Vegan", "Vegetarian", "Gluten-free", "Dairy-free", "Nut-free"]}, "description": "Dietary preference. Default is empty list."}}, "required": ["location"]}}]}
{"id": "simple_78", "question": [[{"role": "user", "content": "Get the average temperature in Austin for the next 3 days in Celsius."}]], "function": [{"name": "average_temperature", "description": "Retrieves the average temperature for a specific location over the defined timeframe.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city to get the average temperature for. It should format as city name such as Boston."}, "days": {"type": "integer", "description": "The number of days to get the average temperature for."}, "temp_unit": {"type": "string", "description": "The temperature unit ('Celsius' or 'Fahrenheit'). Default is 'Fahrenheit'."}}, "required": ["location", "days"]}}]}
{"id": "simple_79", "question": [[{"role": "user", "content": "Create a histogram for student scores with the following data: 85, 90, 88, 92, 86, 89, 91 and set bin range to 5."}]], "function": [{"name": "create_histogram", "description": "Create a histogram based on provided data.", "parameters": {"type": "dict", "properties": {"data": {"type": "array", "items": {"type": "integer"}, "description": "The data for which histogram needs to be plotted."}, "bins": {"type": "integer", "description": "The number of equal-width bins in the range. Default is 10."}}, "required": ["data", "bins"]}}]}
{"id": "simple_80", "question": [[{"role": "user", "content": "I want to find 5 restaurants nearby my location, Manhattan, offering Thai food and a vegan menu."}]], "function": [{"name": "find_restaurants", "description": "Locate nearby restaurants based on location and food preferences.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The specific location or area. The location should be in the format of District, City."}, "food_type": {"type": "string", "description": "The type of food preferred."}, "number": {"type": "integer", "description": "Number of results to return."}, "dietary_requirements": {"type": "array", "items": {"type": "string"}, "description": "Special dietary requirements, e.g. vegan, gluten-free. Default is empty list."}}, "required": ["location", "food_type", "number"]}}]}
{"id": "simple_81", "question": [[{"role": "user", "content": "Find the fastest route from San Francisco to Los Angeles with toll roads avoided."}]], "function": [{"name": "map_routing.fastest_route", "description": "Finds the fastest route from one location to another, with an option to avoid toll roads.", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "The starting location for the journey."}, "end_location": {"type": "string", "description": "The destination for the journey."}, "avoid_tolls": {"type": "boolean", "description": "Option to avoid toll roads during the journey. Default is false."}}, "required": ["start_location", "end_location"]}}]}
{"id": "simple_82", "question": [[{"role": "user", "content": "Calculate the average of list of integers [12, 15, 18, 20, 21, 26, 30]."}]], "function": [{"name": "calculate_average", "description": "Calculates the average of a list of numbers.", "parameters": {"type": "dict", "properties": {"numbers": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to calculate the average of."}}, "required": ["numbers"]}}]}
{"id": "simple_83", "question": [[{"role": "user", "content": "Calculate the distance between two GPS coordinates (33.4484 N, 112.0740 W) and (34.0522 N, 118.2437 W) in miles."}]], "function": [{"name": "calculate_distance", "description": "Calculate the distance between two GPS coordinates.", "parameters": {"type": "dict", "properties": {"coord1": {"type": "tuple", "description": "The first coordinate as (latitude, longitude).", "items": {"type": "float"}}, "coord2": {"type": "tuple", "description": "The second coordinate as (latitude, longitude).", "items": {"type": "float"}}, "unit": {"type": "string", "description": "The unit of distance. Options: 'miles', 'kilometers'."}}, "required": ["coord1", "coord2", "unit"]}}]}
{"id": "simple_84", "question": [[{"role": "user", "content": "Calculate the Body Mass Index (BMI) of a person with a weight of 85 kilograms and height of 180 cm."}]], "function": [{"name": "calculate_bmi", "description": "Calculate the Body Mass Index (BMI) of a person.", "parameters": {"type": "dict", "properties": {"weight": {"type": "integer", "description": "Weight of the person in kilograms."}, "height": {"type": "integer", "description": "Height of the person in centimeters."}, "unit": {"type": "string", "description": "Optional parameter to choose between 'imperial' and 'metric' systems. Default is 'metric'."}}, "required": ["weight", "height"]}}]}
{"id": "simple_85", "question": [[{"role": "user", "content": "What's the approximate distance between Boston, MA, and Washington, D.C. in mile?"}]], "function": [{"name": "geo_distance.calculate", "description": "Calculate the geographic distance between two given locations.", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "The starting location for the distance calculation. Specify the location in the format of City, State."}, "end_location": {"type": "string", "description": "The destination location for the distance calculation. Specify the location in the format of City, State."}, "units": {"type": "string", "description": "Optional. The desired units for the resulting distance ('miles' or 'kilometers'). Defaults to 'miles'."}}, "required": ["start_location", "end_location"]}}]}
{"id": "simple_86", "question": [[{"role": "user", "content": "Find the shortest distance between two cities, New York and Los Angeles, through the train and you can transfer."}]], "function": [{"name": "city_distance.find_shortest", "description": "Calculates the shortest distance between two cities via available public transportation.", "parameters": {"type": "dict", "properties": {"start_city": {"type": "string", "description": "The city you are starting from. The parameter is in the format of city name."}, "end_city": {"type": "string", "description": "The city you are heading to.The parameter is in the format of city name."}, "transportation": {"type": "string", "description": "Preferred mode of public transportation. Default is 'bus'."}, "allow_transfer": {"type": "boolean", "description": "Allows transfer between different transportation if true. Default is false."}}, "required": ["start_city", "end_city"]}}]}
{"id": "simple_87", "question": [[{"role": "user", "content": "Sort the list [5, 3, 4, 1, 2] in ascending order."}]], "function": [{"name": "array_sort", "description": "Sorts a given list in ascending or descending order.", "parameters": {"type": "dict", "properties": {"list": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to be sorted."}, "order": {"type": "string", "enum": ["ascending", "descending"], "description": "Order of sorting."}}, "required": ["list", "order"]}}]}
{"id": "simple_88", "question": [[{"role": "user", "content": "Calculate the BMI (Body Mass Index) of a person who weighs 70kg and is 1.75m tall."}]], "function": [{"name": "calculate_BMI", "description": "Calculate the Body Mass Index (BMI) given a person's weight and height.", "parameters": {"type": "dict", "properties": {"weight_kg": {"type": "integer", "description": "The weight of the person in kilograms."}, "height_m": {"type": "float", "description": "The height of the person in meters."}}, "required": ["weight_kg", "height_m"]}}]}
{"id": "simple_89", "question": [[{"role": "user", "content": "Fetch all records for students studying Science in 'Bluebird High School' from the StudentDB."}]], "function": [{"name": "db_fetch_records", "description": "Fetch records from a specified database table based on certain conditions.", "parameters": {"type": "dict", "properties": {"database_name": {"type": "string", "description": "The name of the database."}, "table_name": {"type": "string", "description": "The name of the table from which records need to be fetched."}, "conditions": {"type": "dict", "properties": {"department": {"type": "string", "description": "The name of the department of students."}, "school": {"type": "string", "description": "The name of the school students are enrolled in."}}, "description": "The conditions based on which records are to be fetched."}, "fetch_limit": {"type": "integer", "description": "Limits the number of records to be fetched. Default is 0, which means no limit."}}, "required": ["database_name", "table_name", "conditions"]}}]}
{"id": "simple_90", "question": [[{"role": "user", "content": "Retrieve Personal Info and Job History data of a specific employee whose ID is 345 in company 'ABC Ltd.'"}]], "function": [{"name": "employee.fetch_data", "description": "Fetches the detailed data for a specific employee in a given company.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "The name of the company."}, "employee_id": {"type": "integer", "description": "The unique ID of the employee."}, "data_field": {"type": "array", "items": {"type": "string", "enum": ["Personal Info", "Job History", "Payroll", "Attendance"]}, "description": "Fields of data to be fetched for the employee (Optional). Default is ['Personal Info']"}}, "required": ["company_name", "employee_id"]}}]}
{"id": "simple_91", "question": [[{"role": "user", "content": "Get the highest rated sushi restaurant in Boston, that opens on Sundays."}]], "function": [{"name": "get_restaurant", "description": "Retrieve highest rated restaurant given cuisine, location, and a condition.", "parameters": {"type": "dict", "properties": {"cuisine": {"type": "string", "description": "Cuisine of the restaurant."}, "location": {"type": "string", "description": "City where restaurant is located."}, "condition": {"type": "string", "description": "Condition to be met by the restaurant (e.g., operating days, amenities, etc.)"}}, "required": ["cuisine", "location", "condition"]}}]}
{"id": "simple_92", "question": [[{"role": "user", "content": "Find all movies starring Leonardo DiCaprio in the year 2010 from IMDB database."}]], "function": [{"name": "imdb.find_movies_by_actor", "description": "Searches the database to find all movies by a specific actor within a certain year.", "parameters": {"type": "dict", "properties": {"actor_name": {"type": "string", "description": "The name of the actor."}, "year": {"type": "integer", "description": "The specific year to search in."}, "category": {"type": "string", "description": "The category of the film (e.g. Drama, Comedy, etc). Default is 'all'"}}, "required": ["actor_name", "year"]}}]}
{"id": "simple_93", "question": [[{"role": "user", "content": "Fetch me the list of IMAX movie releases in theaters near LA for the next week."}]], "function": [{"name": "get_theater_movie_releases", "description": "Retrieve the list of movie releases in specific theaters for a specified period. in the format of city shorten name like SF.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location of the theaters."}, "timeframe": {"type": "integer", "description": "The number of days for which releases are required from current date."}, "format": {"type": "string", "description": "Format of movies - could be 'IMAX', '2D', '3D', '4DX' etc. Default is 'all'"}}, "required": ["location", "timeframe"]}}]}
{"id": "simple_94", "question": [[{"role": "user", "content": "Update my customer information with user id 43523 'name':'John Doe', 'email':'<EMAIL>' in the database."}]], "function": [{"name": "update_user_info", "description": "Update user information in the database.", "parameters": {"type": "dict", "properties": {"user_id": {"type": "integer", "description": "The user ID of the customer."}, "update_info": {"type": "dict", "properties": {"name": {"type": "string", "description": "The customer's updated name."}, "email": {"type": "string", "description": "The customer's updated email."}}, "description": "The new information to update."}, "database": {"type": "string", "description": "The database where the user's information is stored.", "default": "CustomerInfo"}}, "required": ["user_id", "update_info"]}}]}
{"id": "simple_95", "question": [[{"role": "user", "content": "Calculate the area of a triangle with base 5m and height 3m."}]], "function": [{"name": "calc_area_triangle", "description": "Calculate the area of a triangle with the formula area = 0.5 * base * height.", "parameters": {"type": "dict", "properties": {"base": {"type": "integer", "description": "The length of the base of the triangle in meters."}, "height": {"type": "integer", "description": "The perpendicular height of the triangle from the base to the opposite vertex in meters."}}, "required": ["base", "height"]}}]}
{"id": "simple_96", "question": [[{"role": "user", "content": "Find records in database in user table where age is greater than 25 and job is 'engineer'."}]], "function": [{"name": "database.query", "description": "Query the database based on certain conditions.", "parameters": {"type": "dict", "properties": {"table": {"type": "string", "description": "Name of the table to query."}, "conditions": {"type": "array", "items": {"type": "dict", "properties": {"field": {"type": "string", "description": "The field to apply the condition."}, "operation": {"type": "string", "description": "The operation to be performed.", "enum": ["<", ">", "=", ">=", "<="]}, "value": {"type": "string", "description": "The value to be compared."}}, "required": ["field", "operation", "value"]}, "description": "Conditions for the query."}}, "required": ["table", "conditions"]}}]}
{"id": "simple_97", "question": [[{"role": "user", "content": "Calculate the factorial of the number 5"}]], "function": [{"name": "math.factorial", "description": "Calculate the factorial of a given number.", "parameters": {"type": "dict", "properties": {"number": {"type": "integer", "description": "The number to compute factorial."}}, "required": ["number"]}}]}
{"id": "simple_98", "question": [[{"role": "user", "content": "What will be the angle between the hour and minute hands of a clock at 6:30 PM?"}]], "function": [{"name": "calculate_clock_angle", "description": "Calculate the angle between the hour and minute hands of a clock at a given time.", "parameters": {"type": "dict", "properties": {"hours": {"type": "integer", "description": "The hour on the clock face."}, "minutes": {"type": "integer", "description": "The minutes on the clock face."}, "round_to": {"type": "integer", "description": "The number of decimal places to round the result to, default is 2."}}, "required": ["hours", "minutes"]}}]}
{"id": "simple_99", "question": [[{"role": "user", "content": "Plot a sine wave from 0 to 2 pi with a frequency of 5 Hz."}]], "function": [{"name": "plot_sine_wave", "description": "Plot a sine wave for a given frequency in a given range.", "parameters": {"type": "dict", "properties": {"start_range": {"type": "float", "description": "Start of the range in radians. Four decimal places."}, "end_range": {"type": "float", "description": "End of the range in radians. Four decimal places."}, "frequency": {"type": "integer", "description": "Frequency of the sine wave in Hz."}, "amplitude": {"type": "integer", "description": "Amplitude of the sine wave. Default is 1."}, "phase_shift": {"type": "integer", "description": "Phase shift of the sine wave in radians. Default is 0."}}, "required": ["start_range", "end_range", "frequency"]}}]}
{"id": "simple_100", "question": [[{"role": "user", "content": "How much time will it take for the light to reach earth from a star 4 light years away?"}]], "function": [{"name": "light_travel_time", "description": "Calculate the time taken for light to travel from a celestial body to another.", "parameters": {"type": "dict", "properties": {"distance_in_light_years": {"type": "integer", "description": "The distance between the two celestial bodies in light years."}, "speed_of_light": {"type": "integer", "description": "The speed of light in vacuum, in m/s. Default value is ********* m/s."}}, "required": ["distance_in_light_years"]}}]}
{"id": "simple_101", "question": [[{"role": "user", "content": "Calculate the speed of an object in km/h if it traveled 450 meters in 20 seconds."}]], "function": [{"name": "calculate_speed", "description": "Calculate the speed of an object based on the distance travelled and the time taken.", "parameters": {"type": "dict", "properties": {"distance": {"type": "integer", "description": "The distance the object travelled in meters."}, "time": {"type": "integer", "description": "The time it took for the object to travel in seconds."}, "to_unit": {"type": "string", "description": "The unit in which the speed should be calculated, default is m/s."}}, "required": ["distance", "time"]}}]}
{"id": "simple_102", "question": [[{"role": "user", "content": "What's the distance in milesfrom the Earth to the Moon?"}]], "function": [{"name": "calculate_distance", "description": "Calculate the distance between two celestial bodies.", "parameters": {"type": "dict", "properties": {"body1": {"type": "string", "description": "The first celestial body."}, "body2": {"type": "string", "description": "The second celestial body."}, "unit": {"type": "string", "description": "The unit of measurement, default is 'km'."}}, "required": ["body1", "body2"]}}]}
{"id": "simple_103", "question": [[{"role": "user", "content": "Calculate the area under the curve y=3x^2 + 2x - 4, between x = -1 and x = 2."}]], "function": [{"name": "mathematics.calculate_area_under_curve", "description": "Calculate the area under the curve for a given polynomial function within a specified interval.", "parameters": {"type": "dict", "properties": {"polynomial": {"type": "array", "items": {"type": "float"}, "description": "The coefficients of the polynomial, in decreasing order of exponent, where the first element is the coefficient for x^n, the second element is the coefficient for x^(n-1), and so on. The last element is the constant term."}, "limits": {"type": "array", "items": {"type": "float"}, "description": "A list of two numbers specifying the lower and upper limit for the integration interval."}}, "required": ["polynomial", "limits"]}}]}
{"id": "simple_104", "question": [[{"role": "user", "content": "Calculate the area of a triangle with base 6 and height 10."}]], "function": [{"name": "geometry.area_triangle", "description": "Calculate the area of a triangle.", "parameters": {"type": "dict", "properties": {"base": {"type": "integer", "description": "The length of the base of the triangle."}, "height": {"type": "integer", "description": "The height of the triangle from the base."}, "unit": {"type": "string", "description": "The measurement unit for the area. Defaults to square meters."}}, "required": ["base", "height"]}}]}
{"id": "simple_105", "question": [[{"role": "user", "content": "Calculate the power of 3 raised to the power 4."}]], "function": [{"name": "math.power", "description": "Calculate the power of one number raised to another.", "parameters": {"type": "dict", "properties": {"base": {"type": "integer", "description": "The base number."}, "exponent": {"type": "integer", "description": "The exponent."}, "mod": {"type": "integer", "description": "The modulus. Default is 1. Calculates pow(base, exponent) % mod when provided."}}, "required": ["base", "exponent"]}}]}
{"id": "simple_106", "question": [[{"role": "user", "content": "Train a random forest classifier on dataset your_dataset_name with maximum depth of trees as 5, and number of estimators as 100."}]], "function": [{"name": "train_random_forest_classifier", "description": "Train a Random Forest classifier with the specified parameters.", "parameters": {"type": "dict", "properties": {"dataset": {"type": "string", "description": "The dataset to train the classifier on."}, "max_depth": {"type": "integer", "description": "The maximum depth of the trees in the forest."}, "n_estimators": {"type": "integer", "description": "The number of trees in the forest."}}, "required": ["dataset", "max_depth", "n_estimators"]}}]}
{"id": "simple_107", "question": [[{"role": "user", "content": "Calculate the Body Mass Index for a person with a weight of 70 kg and a height of 175 cm."}]], "function": [{"name": "calculate_bmi", "description": "Calculate the Body Mass Index (BMI) for a person based on their weight and height.", "parameters": {"type": "dict", "properties": {"weight": {"type": "integer", "description": "The weight of the person in kilograms."}, "height": {"type": "integer", "description": "The height of the person in centimeters."}, "system": {"type": "string", "description": "The system of units to be used, 'metric' or 'imperial'. Default is 'metric'."}}, "required": ["weight", "height"]}}]}
{"id": "simple_108", "question": [[{"role": "user", "content": "Run a linear regression model with predictor variables 'Age', 'Income' and 'Education' and a target variable 'Purchase_Amount'. Also apply standardization."}]], "function": [{"name": "run_linear_regression", "description": "Build a linear regression model using given predictor variables and a target variable.", "parameters": {"type": "dict", "properties": {"predictors": {"type": "array", "items": {"type": "string"}, "description": "Array containing the names of predictor variables."}, "target": {"type": "string", "description": "The name of target variable."}, "standardize": {"type": "boolean", "description": "Option to apply standardization on the predictors. Defaults to False."}}, "required": ["predictors", "target"]}}]}
{"id": "simple_109", "question": [[{"role": "user", "content": "Generate a random forest model with 100 trees and a depth of 5 on the provided data my_data."}]], "function": [{"name": "random_forest.train", "description": "Train a Random Forest Model on given data", "parameters": {"type": "dict", "properties": {"n_estimators": {"type": "integer", "description": "The number of trees in the forest."}, "max_depth": {"type": "integer", "description": "The maximum depth of the tree."}, "data": {"type": "any", "description": "The training data for the model."}}, "required": ["n_estimators", "max_depth", "data"]}}]}
{"id": "simple_110", "question": [[{"role": "user", "content": "Predict the price of the house in San Francisco with 3 bedrooms, 2 bathrooms and area of 1800 square feet."}]], "function": [{"name": "predict_house_price", "description": "Predict the price of a house in a given area based on number of bedrooms, bathrooms and area.", "parameters": {"type": "dict", "properties": {"bedrooms": {"type": "integer", "description": "The number of bedrooms in the house."}, "bathrooms": {"type": "integer", "description": "The number of bathrooms in the house."}, "area": {"type": "integer", "description": "The area of the house in square feet."}, "location": {"type": "string", "description": "The location of the house in the format of city name."}}, "required": ["bedrooms", "bathrooms", "area", "location"]}}]}
{"id": "simple_111", "question": [[{"role": "user", "content": "Generate a random number from a normal distribution with mean 0 and standard deviation 1."}]], "function": [{"name": "random.normalvariate", "description": "Generates a random number from a normal distribution given the mean and standard deviation.", "parameters": {"type": "dict", "properties": {"mu": {"type": "integer", "description": "Mean of the normal distribution."}, "sigma": {"type": "integer", "description": "Standard deviation of the normal distribution."}}, "required": ["mu", "sigma"]}}]}
{"id": "simple_112", "question": [[{"role": "user", "content": "Calculate the probability of drawing a king from a deck of cards."}]], "function": [{"name": "calculate_probability", "description": "Calculate the probability of an event.", "parameters": {"type": "dict", "properties": {"total_outcomes": {"type": "integer", "description": "Total number of possible outcomes."}, "favorable_outcomes": {"type": "integer", "description": "Number of outcomes considered as 'successful'."}, "round_to": {"type": "integer", "description": "Number of decimal places to round the result to.", "default": 2}}, "required": ["total_outcomes", "favorable_outcomes"]}}]}
{"id": "simple_113", "question": [[{"role": "user", "content": "What's the probability of rolling a six on a six-sided die twice in a row?"}]], "function": [{"name": "probability.dice_roll", "description": "Calculate the probability of rolling a certain number on a six-sided die a certain number of times in a row.", "parameters": {"type": "dict", "properties": {"desired_number": {"type": "integer", "description": "The number you want to roll."}, "number_of_rolls": {"type": "integer", "description": "How many times you want to roll that number in a row."}, "die_sides": {"type": "integer", "description": "The number of sides on the die (optional; default is 6)."}}, "required": ["desired_number", "number_of_rolls"]}}]}
{"id": "simple_114", "question": [[{"role": "user", "content": "Find the probability of getting exactly 5 heads in 10 fair coin tosses."}]], "function": [{"name": "prob_dist.binomial", "description": "Compute the probability of having 'success' outcome from binomial distribution.", "parameters": {"type": "dict", "properties": {"trials": {"type": "integer", "description": "The number of independent experiments."}, "successes": {"type": "integer", "description": "The number of success events."}, "p": {"type": "float", "description": "The probability of success on any given trial, defaults to 0.5"}}, "required": ["trials", "successes"]}}]}
{"id": "simple_115", "question": [[{"role": "user", "content": "Calculate the probability of getting exactly 5 heads in 8 tosses of a fair coin."}]], "function": [{"name": "calculate_binomial_probability", "description": "Calculates the binomial probability given the number of trials, successes and the probability of success on an individual trial.", "parameters": {"type": "dict", "properties": {"number_of_trials": {"type": "integer", "description": "The total number of trials."}, "number_of_successes": {"type": "integer", "description": "The desired number of successful outcomes."}, "probability_of_success": {"type": "float", "description": "The probability of a successful outcome on any given trial.", "default": 0.5}}, "required": ["number_of_trials", "number_of_successes"]}}]}
{"id": "simple_116", "question": [[{"role": "user", "content": "What's the probability of drawing a king from a well shuffled standard deck of 52 cards?"}]], "function": [{"name": "probabilities.calculate_single", "description": "Calculate the probability of an event.", "parameters": {"type": "dict", "properties": {"total_outcomes": {"type": "integer", "description": "The total number of outcomes."}, "event_outcomes": {"type": "integer", "description": "The number of outcomes where the event occurs."}, "round": {"type": "integer", "description": "Round the answer to a specified number of decimal places. Defaults to 2."}}, "required": ["total_outcomes", "event_outcomes"]}}]}
{"id": "simple_117", "question": [[{"role": "user", "content": "What are the odds of pulling a heart suit from a well-shuffled standard deck of 52 cards? Format it as ratio."}]], "function": [{"name": "probability_of_event", "description": "Calculates the probability of an event.", "parameters": {"type": "dict", "properties": {"success_outcomes": {"type": "integer", "description": "The number of successful outcomes."}, "total_outcomes": {"type": "integer", "description": "The total number of possible outcomes."}, "format_as_ratio": {"type": "boolean", "description": "When true, formats the output as a ratio instead of a decimal. Default is false."}}, "required": ["success_outcomes", "total_outcomes"]}}]}
{"id": "simple_118", "question": [[{"role": "user", "content": "Perform a two-sample t-test on my experiment data of Control [10, 15, 12, 14, 11] and Treated [18, 16, 17, 20, 22] group with alpha equals to 0.05"}]], "function": [{"name": "stats.t_test", "description": "Perform a two-sample t-test for two given arrays.", "parameters": {"type": "dict", "properties": {"array_1": {"type": "array", "items": {"type": "integer"}, "description": "First array of data."}, "array_2": {"type": "array", "items": {"type": "integer"}, "description": "Second array of data."}, "alpha": {"type": "float", "description": "Significance level for hypothesis testing."}}, "required": ["array_1", "array_2", "alpha"]}}]}
{"id": "simple_119", "question": [[{"role": "user", "content": "Perform a hypothesis test for two independent samples with scores of Sample1: [22,33,42,12,34] and Sample2: [23,45,44,14,38] at a significance level of 0.05."}]], "function": [{"name": "hypothesis_testing.ttest_ind", "description": "Conducts a hypothesis test for two independent samples.", "parameters": {"type": "dict", "properties": {"sample1": {"type": "array", "items": {"type": "integer"}, "description": "First set of observations (array of numbers)."}, "sample2": {"type": "array", "items": {"type": "integer"}, "description": "Second set of observations (array of numbers)."}, "significance_level": {"type": "float", "description": "Significance level of the test (default: 0.05)"}}, "required": ["sample1", "sample2"]}}]}
{"id": "simple_120", "question": [[{"role": "user", "content": "Run a two sample T-test to compare the average of Group A [3, 4, 5, 6, 4] and Group B [7, 8, 9, 8, 7] assuming equal variance."}]], "function": [{"name": "run_two_sample_ttest", "description": "Runs a two sample t-test for two given data groups.", "parameters": {"type": "dict", "properties": {"group1": {"type": "array", "items": {"type": "integer"}, "description": "First group of data points."}, "group2": {"type": "array", "items": {"type": "integer"}, "description": "Second group of data points."}, "equal_variance": {"type": "boolean", "description": "Assumption about whether the two samples have equal variance.", "default": true}}, "required": ["group1", "group2"]}}]}
{"id": "simple_121", "question": [[{"role": "user", "content": "Calculate the probability of observing 60 heads if I flip a coin 100 times with probability of heads 0.5."}]], "function": [{"name": "calc_binomial_prob", "description": "Calculates the probability of an outcome based on the binomial distribution", "parameters": {"type": "dict", "properties": {"num_trials": {"type": "integer", "description": "Number of independent experiments."}, "num_success": {"type": "integer", "description": "Number of times the event of interest has occurred."}, "prob_success": {"type": "float", "description": "Probability of the event of interest on any single experiment."}}, "required": ["num_trials", "num_success", "prob_success"]}}]}
{"id": "simple_122", "question": [[{"role": "user", "content": "Perform a Chi-Squared test for independence on a 2x2 contingency table [ [10, 20], [30, 40] ]"}]], "function": [{"name": "chi_squared_test", "description": "Performs a Chi-Squared test for independence on a 2x2 contingency table.", "parameters": {"type": "dict", "properties": {"table": {"type": "array", "items": {"type": "array", "items": {"type": "integer"}}, "description": "A 2x2 contingency table presented in array form."}, "alpha": {"type": "float", "description": "Significance level for the Chi-Squared test. Default is 0.05."}}, "required": ["table"]}}]}
{"id": "simple_123", "question": [[{"role": "user", "content": "Perform a two-sample t-test to determine if there is a significant difference between the mean of group1 (e.g., 12.4, 15.6, 11.2, 18.9) and group2 (e.g., 10.5, 9.8, 15.2, 13.8) at the significance level 0.05."}]], "function": [{"name": "hypothesis_testing.two_sample_t_test", "description": "Perform a two-sample t-test to determine if there is a significant difference between the means of two independent samples.", "parameters": {"type": "dict", "properties": {"group1": {"type": "array", "items": {"type": "float"}, "description": "Sample observations from group 1."}, "group2": {"type": "array", "items": {"type": "float"}, "description": "Sample observations from group 2."}, "alpha": {"type": "float", "description": "Significance level for the t-test. Default is 0.05."}}, "required": ["group1", "group2"]}}]}
{"id": "simple_124", "question": [[{"role": "user", "content": "Find the statistical significance between two set of variables, dataset_A with the values 12, 24, 36 and dataset_B with the values 15, 30, 45."}]], "function": [{"name": "t_test", "description": "Perform a statistical t-test to check if the means of two independent datasets are statistically different.", "parameters": {"type": "dict", "properties": {"dataset_A": {"type": "array", "items": {"type": "integer"}, "description": "Dataset A for comparison."}, "dataset_B": {"type": "array", "items": {"type": "integer"}, "description": "Dataset B for comparison."}, "alpha": {"type": "float", "description": "Significance level for the test. Default is 0.05."}}, "required": ["dataset_A", "dataset_B"]}}]}
{"id": "simple_125", "question": [[{"role": "user", "content": "Predict house price in San Francisco based on its area of 2500 square feet, number of rooms as 5 and year of construction is 1990."}]], "function": [{"name": "predict_house_price", "description": "Predict house price based on area, number of rooms and year of construction.", "parameters": {"type": "dict", "properties": {"area": {"type": "integer", "description": "Area of the house in square feet."}, "rooms": {"type": "integer", "description": "Number of rooms in the house."}, "year": {"type": "integer", "description": "Year when the house was constructed."}, "location": {"type": "string", "description": "The location or city of the house."}}, "required": ["area", "rooms", "year", "location"]}}]}
{"id": "simple_126", "question": [[{"role": "user", "content": "What is the coefficient of determination (R-squared) for a model using engine size and fuel economy variables to predict car_price with a dataset in path C:/data/cars.csv?"}]], "function": [{"name": "linear_regression.get_r_squared", "description": "Calculate the coefficient of determination of a regression model.", "parameters": {"type": "dict", "properties": {"dataset_path": {"type": "string", "description": "Path to the CSV dataset file."}, "independent_variables": {"type": "array", "items": {"type": "string"}, "description": "The independent variables to use in the regression model."}, "dependent_variable": {"type": "string", "description": "The dependent variable to predict in the regression model."}}, "required": ["dataset_path", "independent_variables", "dependent_variable"]}}]}
{"id": "simple_127", "question": [[{"role": "user", "content": "Find the Net Present Value (NPV) of an investment, given cash_flows=[200,300,400,500], a discount rate of 10%, and an initial investment of $2000."}]], "function": [{"name": "calculate_NPV", "description": "Calculate the NPV (Net Present Value) of an investment, considering a series of future cash flows, discount rate, and an initial investment.", "parameters": {"type": "dict", "properties": {"cash_flows": {"type": "array", "items": {"type": "integer"}, "description": "Series of future cash flows."}, "discount_rate": {"type": "float", "description": "The discount rate to use."}, "initial_investment": {"type": "integer", "description": "The initial investment. Default is 0 if not specified."}}, "required": ["cash_flows", "discount_rate"]}}]}
{"id": "simple_128", "question": [[{"role": "user", "content": "What's the quarterly dividend per share of a company with 100 million outstanding shares and total dividend payout of 50 million USD?"}]], "function": [{"name": "finance.calculate_quarterly_dividend_per_share", "description": "Calculate quarterly dividend per share for a company given total dividend payout and outstanding shares", "parameters": {"type": "dict", "properties": {"total_payout": {"type": "integer", "description": "The total amount of dividends paid out in USD"}, "outstanding_shares": {"type": "integer", "description": "Total number of outstanding shares"}}, "required": ["total_payout", "outstanding_shares"], "optional": []}}]}
{"id": "simple_129", "question": [[{"role": "user", "content": "Calculate the discounted cash flow of a bond that is giving a coupon payment of $100 annually for next 5 years with discount rate 4%."}]], "function": [{"name": "calculate_discounted_cash_flow", "description": "Calculate the discounted cash flow of a bond for a given annual coupon payment, time frame and discount rate.", "parameters": {"type": "dict", "properties": {"coupon_payment": {"type": "integer", "description": "The annual coupon payment."}, "period": {"type": "integer", "description": "The time frame in years for which coupon payment is made."}, "discount_rate": {"type": "float", "description": "The discount rate."}, "face_value": {"type": "integer", "description": "The face value of the bond, default is 1000."}}, "required": ["coupon_payment", "period", "discount_rate"]}}]}
{"id": "simple_130", "question": [[{"role": "user", "content": "What's the NPV (Net Present Value) of a series of cash flows: [-50000, 10000, 15000, 20000, 25000, 30000] discounted at 8% annually?"}]], "function": [{"name": "finance_calculator.npv", "description": "Calculate the Net Present Value (NPV) for a series of cash flows discounted at a certain interest rate.", "parameters": {"type": "dict", "properties": {"cash_flows": {"type": "array", "items": {"type": "integer"}, "description": "A list of cash flows."}, "discount_rate": {"type": "float", "description": "The annual interest rate used to discount the cash flows."}, "years": {"type": "array", "items": {"type": "integer"}, "description": "A list of years when the cash flow occurs. Default is empty array."}}, "required": ["cash_flows", "discount_rate"]}}]}
{"id": "simple_131", "question": [[{"role": "user", "content": "Calculate the compound interest for an initial principal amount of $10000, with an annual interest rate of 5% and the number of times interest applied per time period is 4 and the time the money is invested for 10 years."}]], "function": [{"name": "calculate_compound_interest", "description": "Calculate compound interest for an initial principal amount.", "parameters": {"type": "dict", "properties": {"principal": {"type": "integer", "description": "The principal amount that the interest is applied to."}, "rate": {"type": "float", "description": "The annual interest rate. Enter as a decimal. E.g, 5% is 0.05"}, "time": {"type": "integer", "description": "The time the money is invested for in years."}, "n": {"type": "integer", "description": "The number of times that interest is compounded per time period. Default is 1."}}, "required": ["principal", "rate", "time"]}}]}
{"id": "simple_132", "question": [[{"role": "user", "content": "Calculate the company's return on equity given its net income of $2,000,000, shareholder's equity of $10,000,000, and dividends paid of $200,000."}]], "function": [{"name": "calculate_return_on_equity", "description": "Calculate a company's return on equity based on its net income, shareholder's equity, and dividends paid.", "parameters": {"type": "dict", "properties": {"net_income": {"type": "integer", "description": "The company's net income."}, "shareholder_equity": {"type": "integer", "description": "The company's total shareholder's equity."}, "dividends_paid": {"type": "integer", "description": "The total dividends paid by the company. Optional. If not given, default to 0."}}, "required": ["net_income", "shareholder_equity"]}}]}
{"id": "simple_133", "question": [[{"role": "user", "content": "Predict the future value of a $5000 investment with an annual interest rate of 5% in 3 years with monthly compounding."}]], "function": [{"name": "finance.predict_future_value", "description": "Calculate the future value of an investment given its present value, interest rate, the number of compounding periods per year, and the time horizon.", "parameters": {"type": "dict", "properties": {"present_value": {"type": "integer", "description": "The present value of the investment."}, "annual_interest_rate": {"type": "float", "description": "The annual interest rate of the investment."}, "compounding_periods_per_year": {"type": "integer", "description": "The number of times that interest is compounded per year. Default is 1 (annually)."}, "time_years": {"type": "integer", "description": "The investment horizon in years."}}, "required": ["present_value", "annual_interest_rate", "time_years"]}}]}
{"id": "simple_134", "question": [[{"role": "user", "content": "Predict the total expected profit of stocks XYZ in 5 years given I have invested $5000 and annual return rate is 7%."}]], "function": [{"name": "investment.predictProfit", "description": "Predict the profit for given investment after specified number of years.", "parameters": {"type": "dict", "properties": {"investment_amount": {"type": "integer", "description": "The amount invested in dollars."}, "annual_return": {"type": "float", "description": "The annual return rate of the investment."}, "years": {"type": "integer", "description": "The time period in years for which the investment is made."}}, "required": ["investment_amount", "annual_return", "years"]}}]}
{"id": "simple_135", "question": [[{"role": "user", "content": "Calculate the return on investment for a stock bought at $20, sold at $25, with a dividend of $2."}]], "function": [{"name": "calculate_return_on_investment", "description": "Calculate the return on investment for a given stock based on its purchase price, sale price, and any dividends received.", "parameters": {"type": "dict", "properties": {"purchase_price": {"type": "integer", "description": "The price the stock was bought at."}, "sale_price": {"type": "integer", "description": "The price the stock was sold at."}, "dividend": {"type": "integer", "description": "Any dividends received from the stock.", "default": 0}}, "required": ["purchase_price", "sale_price"]}}]}
{"id": "simple_136", "question": [[{"role": "user", "content": "Find the compound interest for an investment of $10000 with an annual interest rate of 5% compounded monthly for 5 years."}]], "function": [{"name": "compound_interest", "description": "Calculate compound interest for a certain time period.", "parameters": {"type": "dict", "properties": {"principal": {"type": "integer", "description": "The initial amount of money that was invested or loaned out."}, "annual_rate": {"type": "float", "description": "The interest rate for a year as a percentage."}, "compounding_freq": {"type": "string", "enum": ["monthly", "quarterly", "annually"], "description": "The number of times that interest is compounded per unit period."}, "time_in_years": {"type": "integer", "description": "The time the money is invested for in years."}}, "required": ["principal", "annual_rate", "compounding_freq", "time_in_years"]}}]}
{"id": "simple_137", "question": [[{"role": "user", "content": "Calculate the projected return on a $5000 investment in ABC company's stock, if the expected annual growth rate is 6% and the holding period is 5 years."}]], "function": [{"name": "calculate_stock_return", "description": "Calculate the projected return of a stock investment given the investment amount, the annual growth rate and holding period in years.", "parameters": {"type": "dict", "properties": {"investment_amount": {"type": "integer", "description": "The amount of money to invest."}, "annual_growth_rate": {"type": "float", "description": "The expected annual growth rate of the stock."}, "holding_period": {"type": "integer", "description": "The number of years you intend to hold the stock."}, "dividends": {"type": "boolean", "description": "Optional. True if the calculation should take into account potential dividends. Default is false."}}, "required": ["investment_amount", "annual_growth_rate", "holding_period"]}}]}
{"id": "simple_138", "question": [[{"role": "user", "content": "Calculate the future value of my portfolio if I invest $5000 in stock 'X' with an expected annual return of 5% for 7 years."}]], "function": [{"name": "portfolio_future_value", "description": "Calculate the future value of an investment in a specific stock based on the invested amount, expected annual return and number of years.", "parameters": {"type": "dict", "properties": {"stock": {"type": "string", "description": "The ticker symbol of the stock."}, "invested_amount": {"type": "integer", "description": "The invested amount in USD."}, "expected_annual_return": {"type": "float", "description": "The expected annual return on investment as a decimal. E.g. 5% = 0.05"}, "years": {"type": "integer", "description": "The number of years for which the investment is made."}}, "required": ["stock", "invested_amount", "expected_annual_return", "years"]}}]}
{"id": "simple_139", "question": [[{"role": "user", "content": "What is the estimated return on a mutual fund, given that it has a yearly yield of 5%, an investment amount of $2000 and a time period of 3 years?"}]], "function": [{"name": "estimate_mutual_fund_return", "description": "Calculate the estimated return on a mutual fund given the yearly yield, the investment amount and the time period.", "parameters": {"type": "dict", "properties": {"yearly_yield": {"type": "float", "description": "The yearly yield of the mutual fund as a percentage."}, "investment_amount": {"type": "integer", "description": "The initial investment amount in the mutual fund."}, "years": {"type": "integer", "description": "The time period for which the investment is made in years."}}, "required": ["yearly_yield", "investment_amount", "years"]}}]}
{"id": "simple_140", "question": [[{"role": "user", "content": "Calculate the Compound Annual Growth Rate (CAGR) for an initial investment of $2000, final value of $3000 in a period of 4 years."}]], "function": [{"name": "calculate_cagr", "description": "Calculate the Compound Annual Growth Rate (CAGR) given an initial investment value, a final investment value, and the number of years.", "parameters": {"type": "dict", "properties": {"initial_value": {"type": "integer", "description": "The initial investment value."}, "final_value": {"type": "integer", "description": "The final investment value."}, "period_in_years": {"type": "integer", "description": "The period of the investment in years."}}, "required": ["initial_value", "final_value", "period_in_years"]}}]}
{"id": "simple_141", "question": [[{"role": "user", "content": "Get current Gold price per ounce."}]], "function": [{"name": "get_metal_price", "description": "Retrieve the current price for a specified metal and measure.", "parameters": {"type": "dict", "properties": {"metal": {"type": "string", "description": "The metal whose price needs to be fetched."}, "measure": {"type": "string", "description": "The measure unit for price, like 'ounce' or 'kg'."}}, "required": ["metal", "measure"]}}]}
{"id": "simple_142", "question": [[{"role": "user", "content": "Find the NASDAQ stock price for the company Amazon at closing March.11, 2022."}]], "function": [{"name": "get_stock_price", "description": "Get the closing stock price for a specific company on a specified date.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "Name of the company."}, "date": {"type": "string", "description": "Date of when to get the stock price. Format: yyyy-mm-dd."}, "exchange": {"type": "string", "description": "Name of the stock exchange market where the company's stock is listed. Default is 'NASDAQ'"}}, "required": ["company_name", "date"]}}]}
{"id": "simple_143", "question": [[{"role": "user", "content": "'Get stock price of Apple for the last 5 days in NASDAQ.'"}]], "function": [{"name": "get_stock_price", "description": "Retrieve the stock price for a specific company and time frame.", "parameters": {"type": "dict", "properties": {"company": {"type": "string", "description": "The ticker symbol of the company."}, "days": {"type": "integer", "description": "Number of past days for which the stock price is required."}, "exchange": {"type": "string", "description": "The stock exchange where the company is listed, default is NYSE"}}, "required": ["company", "days"]}}]}
{"id": "simple_144", "question": [[{"role": "user", "content": "Find the market performance of the S&P 500 and the Dow Jones over the past 5 days."}]], "function": [{"name": "market_performance.get_data", "description": "Retrieve the market performance data for specified indexes over a specified time period.", "parameters": {"type": "dict", "properties": {"indexes": {"type": "array", "items": {"type": "string"}, "description": "Array of stock market indexes. Supported indexes are 'S&P 500', 'Dow Jones', 'NASDAQ', 'FTSE 100', 'DAX' etc."}, "days": {"type": "integer", "description": "Number of days in the past for which the performance data is required."}, "detailed": {"type": "boolean", "description": "Whether to return detailed performance data. If set to true, returns high, low, opening, and closing prices. If false, returns only closing prices. Default is false."}}, "required": ["indexes", "days"]}}]}
{"id": "simple_145", "question": [[{"role": "user", "content": "Calculate the compounded interest for an initial principal of $5000, annual interest rate of 5%, and compounding period of 10 years."}]], "function": [{"name": "calculate_compounded_interest", "description": "Calculate the compounded interest for a given principal, interest rate, and period.", "parameters": {"type": "dict", "properties": {"principal": {"type": "integer", "description": "The initial principal."}, "interest_rate": {"type": "float", "description": "The annual interest rate."}, "period": {"type": "integer", "description": "The period in years."}, "compounding_frequency": {"type": "string", "description": "The frequency of compounding per year. Defaults to 'Annually'.", "enum": ["Annually", "Semiannually", "Quarterly", "Monthly", "Daily"]}}, "required": ["principal", "interest_rate", "period"]}}]}
{"id": "simple_146", "question": [[{"role": "user", "content": "What's the price of Amazon stock for the last 3 days?"}]], "function": [{"name": "stock_price", "description": "Get stock price data for a given company over a specified number of days.", "parameters": {"type": "dict", "properties": {"company": {"type": "string", "description": "The company name."}, "days": {"type": "integer", "description": "The number of previous days to retrieve data for."}, "data_type": {"type": "string", "description": "The type of price data to retrieve (e.g., 'Open', 'Close', 'High', 'Low'). Default is 'Close'."}}, "required": ["company", "days"]}}]}
{"id": "simple_147", "question": [[{"role": "user", "content": "Retrieve stock prices of Microsoft and Google for the last 2 weeks."}]], "function": [{"name": "get_stock_prices", "description": "Retrieves stock prices for specified companies and duration.", "parameters": {"type": "dict", "properties": {"companies": {"type": "array", "items": {"type": "string"}, "description": "List of companies to retrieve stock prices for."}, "duration": {"type": "string", "description": "Time duration to retrieve stock prices for. E.g., '1 week', '2 weeks', '1 month', etc."}}, "required": ["companies", "duration"]}}]}
{"id": "simple_148", "question": [[{"role": "user", "content": "Calculate the future value of an investment with an annual rate of return of 8%, an initial investment of $20000, and a time frame of 5 years."}]], "function": [{"name": "finance.calculate_future_value", "description": "Calculate the future value of an investment given an initial investment, annual rate of return, and a time frame.", "parameters": {"type": "dict", "properties": {"initial_investment": {"type": "integer", "description": "The initial investment amount."}, "rate_of_return": {"type": "float", "description": "The annual rate of return."}, "years": {"type": "integer", "description": "The time frame of the investment in years."}, "contribution": {"type": "integer", "description": "Optional: Additional regular contributions. Default is 0."}}, "required": ["initial_investment", "rate_of_return", "years"]}}]}
{"id": "simple_149", "question": [[{"role": "user", "content": "What's the current stock price of Apple and Microsoft?"}]], "function": [{"name": "get_stock_price", "description": "Retrieves the current stock price of the specified companies", "parameters": {"type": "dict", "properties": {"company_names": {"type": "array", "items": {"type": "string"}, "description": "The list of companies for which to retrieve the stock price."}}, "required": ["company_names"]}}]}
{"id": "simple_150", "question": [[{"role": "user", "content": "Calculate the return of investment of a bank's savings account with a deposit of $1000, annual interest rate of 3% for 1 year."}]], "function": [{"name": "calculate_roi", "description": "Calculate the return on investment for a given deposit amount, annual interest rate, and time frame.", "parameters": {"type": "dict", "properties": {"deposit": {"type": "integer", "description": "The initial deposit amount."}, "annual_interest_rate": {"type": "float", "description": "The annual interest rate provided by the bank."}, "years": {"type": "integer", "description": "The period for which the money is invested."}}, "required": ["deposit", "annual_interest_rate", "years"]}}]}
{"id": "simple_151", "question": [[{"role": "user", "content": "Find the highest grossing bank in the U.S for year 2020."}]], "function": [{"name": "highest_grossing_banks", "description": "Retrieve the highest grossing banks in a specified country and year.", "parameters": {"type": "dict", "properties": {"country": {"type": "string", "description": "The country to get the data from."}, "year": {"type": "integer", "description": "The year to get the data from."}, "top_n": {"type": "integer", "description": "Top n banks in terms of grossing. Default is 5"}}, "required": ["country", "year"]}}]}
{"id": "simple_152", "question": [[{"role": "user", "content": "Calculate the balance of a mutual fund given a total investment of $50000 with a 5% annual yield after 3 years."}]], "function": [{"name": "calculate_mutual_fund_balance", "description": "Calculate the final balance of a mutual fund investment based on the total initial investment, annual yield rate and the time period.", "parameters": {"type": "dict", "properties": {"investment_amount": {"type": "integer", "description": "The initial total amount invested in the fund."}, "annual_yield": {"type": "float", "description": "The annual yield rate of the fund."}, "years": {"type": "integer", "description": "The period of time for the fund to mature."}}, "required": ["investment_amount", "annual_yield", "years"]}}]}
{"id": "simple_153", "question": [[{"role": "user", "content": "Calculate the compounded interest on an initial deposit of $5000 at an annual interest rate of 3% for 5 years, compounded quarterly."}]], "function": [{"name": "calculate_compounded_interest", "description": "Calculate the compounded interest for a given initial deposit, interest rate, time and number of times the interest is compounded per unit time.", "parameters": {"type": "dict", "properties": {"principal": {"type": "integer", "description": "The initial amount of money that is being invested or loaned."}, "rate": {"type": "float", "description": "The annual interest rate."}, "time": {"type": "integer", "description": "The number of time periods the money is invested or loaned for."}, "n": {"type": "integer", "description": "The number of times that interest is compounded per unit time."}}, "required": ["principal", "rate", "time", "n"]}}]}
{"id": "simple_154", "question": [[{"role": "user", "content": "Calculate the Future Value of a $5000 investment made today for a term of 10 years at an annual interest rate of 5%"}]], "function": [{"name": "calculate_future_value", "description": "Calculates the future value of an investment based on the present value, interest rate, and time period.", "parameters": {"type": "dict", "properties": {"present_value": {"type": "integer", "description": "The present value or principal amount."}, "annual_interest_rate": {"type": "float", "description": "The annual interest rate in decimal form. Example, 5% is 0.05."}, "years": {"type": "integer", "description": "The time period in years for which the investment is made."}, "compounds_per_year": {"type": "integer", "description": "The number of times the interest is compounded per year. Default is 1 (annual compounding)."}}, "required": ["present_value", "annual_interest_rate", "years"]}}]}
{"id": "simple_155", "question": [[{"role": "user", "content": "Calculate the future value of my investment of $1000 with an annual interest rate of 5% over 2 years."}]], "function": [{"name": "calculate_future_value", "description": "Calculate the future value of an investment given the initial amount, interest rate, and investment duration.", "parameters": {"type": "dict", "properties": {"initial_investment": {"type": "integer", "description": "The initial investment amount."}, "interest_rate": {"type": "float", "description": "The annual interest rate in decimal form."}, "duration": {"type": "integer", "description": "The investment duration in years."}, "compounded": {"type": "integer", "description": "The number of times the interest is compounded per year. Default is 1 (annual compounding)."}}, "required": ["initial_investment", "interest_rate", "duration"]}}]}
{"id": "simple_156", "question": [[{"role": "user", "content": "Look up details of a felony crime record for case number CA123456 in San Diego County"}]], "function": [{"name": "crime_record.get_record", "description": "Retrieve detailed felony crime records using a specific case number and location.", "parameters": {"type": "dict", "properties": {"case_number": {"type": "string", "description": "The case number related to the crime."}, "county": {"type": "string", "description": "The county in which the crime occurred."}, "details": {"type": "boolean", "description": "To get a detailed report, set as true. Defaults to false."}}, "required": ["case_number", "county"]}}]}
{"id": "simple_157", "question": [[{"role": "user", "content": "Find out if an individual John Doe with a birthday 01-01-1980 has any prior felony convictions in California."}]], "function": [{"name": "criminal_history.check_felonies", "description": "This function checks if an individual has any prior felony convictions based on their full name and birth date.", "parameters": {"type": "dict", "properties": {"full_name": {"type": "string", "description": "The full name of the individual."}, "birth_date": {"type": "string", "description": "The birth date of the individual. Must be in MM-DD-YYYY format."}, "state": {"type": "string", "description": "The state to search the criminal record in. Default to 'None', which the function will search across all states."}}, "required": ["full_name", "birth_date"]}}]}
{"id": "simple_158", "question": [[{"role": "user", "content": "Find the information of criminal cases of Mr. X in New York between 2012 and 2015."}]], "function": [{"name": "get_criminal_records", "description": "Retrieve the criminal records of a specific person in a specific area during a certain time period.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The name of the person."}, "location": {"type": "string", "description": "The city and state, e.g. New York, NY"}, "from_year": {"type": "integer", "description": "The start year of the time frame."}, "to_year": {"type": "integer", "description": "The end year of the time frame."}}, "required": ["name", "location", "from_year", "to_year"]}}]}
{"id": "simple_159", "question": [[{"role": "user", "content": "Give me the details of Criminal Law Amendment Act of 2013."}]], "function": [{"name": "get_act_details", "description": "Retrieve the details of a particular legal act based on its name and year of amendment if any.", "parameters": {"type": "dict", "properties": {"act_name": {"type": "string", "description": "The name of the act."}, "amendment_year": {"type": "integer", "description": "Year of amendment if any. If not provided, the latest amendment year will be considered."}}, "required": ["act_name", "amendment_year"]}}]}
{"id": "simple_160", "question": [[{"role": "user", "content": "Who was the victim in the case docket numbered 2022/AL2562 in California?"}]], "function": [{"name": "get_case_info", "description": "Retrieve case details using a specific case docket number and court location.", "parameters": {"type": "dict", "properties": {"docket": {"type": "string", "description": "Docket number for the specific court case."}, "court": {"type": "string", "description": "Court in which the case was heard."}, "info_type": {"type": "string", "description": "Specify the information type needed for the case. i.e., victim, accused, verdict etc."}}, "required": ["docket", "court", "info_type"]}}]}
{"id": "simple_161", "question": [[{"role": "user", "content": "Find out the possible punishments for the crime of theft in California in detail."}]], "function": [{"name": "crime_statute_lookup", "description": "Look up the criminal statutes in a specific jurisdiction to find possible punishments for a specific crime.", "parameters": {"type": "dict", "properties": {"jurisdiction": {"type": "string", "description": "The jurisdiction to search in, usually a state or country."}, "crime": {"type": "string", "description": "The crime to search for."}, "detail_level": {"type": "string", "enum": ["basic", "detailed"], "description": "How detailed of a report to return. Optional, default is 'basic'."}}, "required": ["jurisdiction", "crime"]}}]}
{"id": "simple_162", "question": [[{"role": "user", "content": "Generate a customized law contract between John and Alice for rental agreement in California."}]], "function": [{"name": "generate_law_contract", "description": "Generates a customized law contract given involved parties, contract type and location.", "parameters": {"type": "dict", "properties": {"parties": {"type": "array", "items": {"type": "string"}, "description": "Parties involved in the contract."}, "contract_type": {"type": "string", "description": "Type of the contract."}, "location": {"type": "string", "description": "Location where the contract will be in effect."}}, "required": ["parties", "contract_type", "location"]}}]}
{"id": "simple_163", "question": [[{"role": "user", "content": "Provide me with the property records of my house located at 123 main street, with parcel number 1234567890 in Santa Clara county. Include owners information in the response."}]], "function": [{"name": "property_records.get", "description": "Fetch property records based on location, parcel number and county.", "parameters": {"type": "dict", "properties": {"address": {"type": "string", "description": "Address of the property."}, "parcel_number": {"type": "string", "description": "Parcel number of the property."}, "county": {"type": "string", "description": "County where the property is located."}, "include_owner": {"type": "boolean", "description": "Include owner's name in the property record. Default is false.", "default": false}}, "required": ["address", "parcel_number", "county"]}}]}
{"id": "simple_164", "question": [[{"role": "user", "content": "Provide me the official crime rate of violent crime in San Francisco in 2020."}]], "function": [{"name": "get_crime_rate", "description": "Retrieve the official crime rate of a city.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The name of the city."}, "state": {"type": "string", "description": "The state where the city is located."}, "type": {"type": "string", "description": "Optional. The type of crime. Default is 'violent'"}, "year": {"type": "integer", "description": "Optional. The year for the crime rate data. Default is year 2001."}}, "required": ["city", "state"]}}]}
{"id": "simple_165", "question": [[{"role": "user", "content": "Retrieve cases from 2020 about theft crimes in Los Angeles, California"}]], "function": [{"name": "civil_cases.retrieve", "description": "Retrieve civil cases based on given parameters, including year, crime type, and location.", "parameters": {"type": "dict", "properties": {"year": {"type": "integer", "description": "Year of the cases"}, "crime_type": {"type": "string", "description": "Type of the crime."}, "location": {"type": "string", "description": "Location of the case in the format of city name."}}, "required": ["year", "crime_type", "location"]}}]}
{"id": "simple_166", "question": [[{"role": "user", "content": "Find a lawyer specializing in divorce cases and charge fee less than 400 dollars per hour in Chicago."}]], "function": [{"name": "lawyer.find_nearby", "description": "Locate nearby lawyers based on specific criteria like specialty, fee per hour and city.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city and state, e.g. Chicago, IL."}, "specialty": {"type": "array", "items": {"type": "string", "enum": ["Civil", "Divorce", "Immigration", "Business", "Criminal"]}, "description": "Specialization of the lawyer."}, "fee": {"type": "integer", "description": "Hourly fee charged by lawyer"}}, "required": ["city", "specialty", "fee"]}}]}
{"id": "simple_167", "question": [[{"role": "user", "content": "Retrieve the details of a Supreme Court case titled 'Roe v. Wade'.Include dissent information."}]], "function": [{"name": "law.civil.get_case_details", "description": "Retrieve the details of a Supreme Court case given its title.", "parameters": {"type": "dict", "properties": {"case_title": {"type": "string", "description": "Title of the Supreme Court case."}, "include_dissent": {"type": "boolean", "description": "If true, the output will include details of the dissenting opinion."}}, "required": ["case_title", "include_dissent"]}}]}
{"id": "simple_168", "question": [[{"role": "user", "content": "Search for ongoing lawsuits related to the company 'Google' filed after January 1, 2021 in California."}]], "function": [{"name": "lawsuit_search", "description": "Search for lawsuits related to a specific company within a specific date range and location.", "parameters": {"type": "dict", "properties": {"company": {"type": "string", "description": "The company related to the lawsuit."}, "start_date": {"type": "string", "description": "Start of the date range for when the lawsuit was filed in the format of MM-DD-YYY."}, "location": {"type": "string", "description": "Location where the lawsuit was filed in the format of full state name."}, "status": {"type": "string", "enum": ["ongoing", "settled", "dismissed"], "description": "The status of the lawsuit. Default is 'ongoing'."}}, "required": ["company", "start_date", "location"]}}]}
{"id": "simple_169", "question": [[{"role": "user", "content": "Find the details of the court case identified by docket number 123456 in Texas. Don't return full text"}]], "function": [{"name": "court_case.search", "description": "Retrieves details about a court case using its docket number and location.", "parameters": {"type": "dict", "properties": {"docket_number": {"type": "string", "description": "The docket number for the case."}, "location": {"type": "string", "description": "The location where the case is registered, in the format: state, e.g., Texas"}, "full_text": {"type": "boolean", "default": "false", "description": "Option to return the full text of the case ruling."}}, "required": ["docket_number", "location"]}}]}
{"id": "simple_170", "question": [[{"role": "user", "content": "Find a historical law case about fraud from 2010 to 2015."}]], "function": [{"name": "law_case_search.find_historical", "description": "Search for a historical law case based on specific criteria like the subject and year.", "parameters": {"type": "dict", "properties": {"subject": {"type": "string", "description": "The subject matter of the case, e.g., 'fraud'"}, "from_year": {"type": "integer", "description": "The start year for the range of the case. The case should happen after this year."}, "to_year": {"type": "integer", "description": "The end year for the range of the case. The case should happen before this year."}}, "required": ["subject", "from_year", "to_year"]}}]}
{"id": "simple_171", "question": [[{"role": "user", "content": "Fetch details of a law case with number 43403 in New York court for year 2018."}]], "function": [{"name": "fetch_law_case_details", "description": "Fetch details of a specific law case based on case number, year and court.", "parameters": {"type": "dict", "properties": {"case_number": {"type": "integer", "description": "The specific number of the law case."}, "court": {"type": "string", "description": "The city name where the court takes place"}, "year": {"type": "integer", "description": "The year in which the law case took place."}}, "required": ["case_number", "court", "year"]}}]}
{"id": "simple_172", "question": [[{"role": "user", "content": "How to obtain the detailed case information of the 'R vs Adams' legal case?"}]], "function": [{"name": "legal_case.fetch", "description": "Fetch detailed legal case information from database.", "parameters": {"type": "dict", "properties": {"case_id": {"type": "string", "description": "The ID of the legal case."}, "details": {"type": "boolean", "description": "True if need the detail info. "}}, "required": ["case_id", "details"]}}]}
{"id": "simple_173", "question": [[{"role": "user", "content": "Find state law cases related to land disputes in the past 5 years from 2015 to 2021 in New York."}]], "function": [{"name": "law_case_search", "description": "Search and retrieve law cases based on the topic, timeline, and location.", "parameters": {"type": "dict", "properties": {"topic": {"type": "string", "description": "The subject matter of the case."}, "year_range": {"type": "array", "items": {"type": "integer"}, "description": "The start and end year for searching cases."}, "location": {"type": "string", "description": "The location where the case is being heard."}, "judicial_system": {"type": "string", "description": "The specific judicial system in which to search (e.g. 'federal', 'state').", "default": "all"}}, "required": ["topic", "year_range", "location"]}}]}
{"id": "simple_174", "question": [[{"role": "user", "content": "Get me the top 10 landmark cases in constitutional law in China."}]], "function": [{"name": "get_top_cases", "description": "Retrieve a list of the most influential or landmark cases in a specific field of law.", "parameters": {"type": "dict", "properties": {"field_of_law": {"type": "string", "description": "The specific field of law e.g., constitutional law, criminal law, etc."}, "top_number": {"type": "integer", "description": "The number of top cases to retrieve."}, "country": {"type": "string", "description": "The country where the law cases should be retrieved from. Default is United States of America."}}, "required": ["field_of_law", "top_number"]}}]}
{"id": "simple_175", "question": [[{"role": "user", "content": "How many months of experience a Lawyer John Doe has on handling Bankruptcy cases."}]], "function": [{"name": "lawyer.get_experience", "description": "Retrieve months of experience of a Lawyer on handling certain type of law cases.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The full name of the Lawyer."}, "law_type": {"type": "string", "description": "The type of law case. eg. Bankruptcy"}}, "required": ["name", "law_type"]}}]}
{"id": "simple_176", "question": [[{"role": "user", "content": "Find details of patent lawsuits involving the company 'Apple Inc.' from the year 2010."}]], "function": [{"name": "lawsuit_details.find", "description": "Find details of lawsuits involving a specific company from a given year.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "Name of the company."}, "year": {"type": "integer", "description": "Year of the lawsuit."}, "case_type": {"type": "string", "description": "Type of the lawsuit, e.g., 'IPR', 'Patent', 'Commercial', etc. Default is 'all'."}}, "required": ["company_name", "year"]}}]}
{"id": "simple_177", "question": [[{"role": "user", "content": "Find all Patent lawsuit cases of Facebook in 2018."}]], "function": [{"name": "get_lawsuit_cases", "description": "Retrieve all lawsuit cases related to a specific company during a particular year.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "The name of the company."}, "year": {"type": "integer", "description": "The specific year to search for lawsuit cases."}, "status": {"type": "string", "enum": ["open", "closed", "all"], "description": "The status of the lawsuit cases to retrieve. If not specified, defaults to 'all'."}}, "required": ["company_name", "year"]}}]}
{"id": "simple_178", "question": [[{"role": "user", "content": "Find details about lawsuit case numbered 'LAX2019080202' in the Los Angeles court."}]], "function": [{"name": "get_lawsuit_details", "description": "Retrieve the detailed information about a lawsuit based on its case number and the court location.", "parameters": {"type": "dict", "properties": {"case_number": {"type": "string", "description": "The case number of the lawsuit."}, "court_location": {"type": "string", "description": "The location of the court where the case is filed."}, "additional_details": {"type": "array", "items": {"type": "string", "enum": ["attorneys", "plaintiffs", "defendants", "charges", "court_updates"]}, "description": "Optional. Array containing additional details to be fetched. Default is all."}}, "required": ["case_number", "court_location"]}}]}
{"id": "simple_179", "question": [[{"role": "user", "content": "Find the latest court case between Apple and Samsung occured in USA."}]], "function": [{"name": "find_latest_court_case", "description": "Find the latest court case between two companies.", "parameters": {"type": "dict", "properties": {"company1": {"type": "string", "description": "The name of the first company."}, "company2": {"type": "string", "description": "The name of the second company."}, "country": {"type": "string", "description": "The country in which the court case is located.", "default": "USA"}}, "required": ["company1", "company2"]}}]}
{"id": "simple_180", "question": [[{"role": "user", "content": "Find the lawsuits filed against the company Google in California in the year 2020."}]], "function": [{"name": "lawsuits_search", "description": "Search for lawsuits against a specific company within a specific time and location.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "The name of the company."}, "location": {"type": "string", "description": "The location where the lawsuit was filed."}, "year": {"type": "integer", "description": "The year when the lawsuit was filed."}, "case_type": {"type": "string", "description": "The type of the case. Options include: 'civil', 'criminal', 'small_claims', etc. Default is 'all'."}}, "required": ["company_name", "location", "year"]}}]}
{"id": "simple_181", "question": [[{"role": "user", "content": "Get details of a lawsuit with case number '123456-ABC' filed in Los Angeles court with verdict"}]], "function": [{"name": "get_lawsuit_details", "description": "Retrieve details of a lawsuit based on its case number and court location.", "parameters": {"type": "dict", "properties": {"case_number": {"type": "string", "description": "Case number of the lawsuit."}, "court_location": {"type": "string", "description": "The location of the court where the lawsuit was filed."}, "with_verdict": {"type": "boolean", "description": "Flag to include verdict details if available. Default is False"}}, "required": ["case_number", "court_location"]}}]}
{"id": "simple_182", "question": [[{"role": "user", "content": "Retrieve all the lawsuit details for case number XYZ123."}]], "function": [{"name": "lawsuit_info", "description": "Retrieves details of a lawsuit given a case number", "parameters": {"type": "dict", "properties": {"case_number": {"type": "string", "description": "The unique identifier of the lawsuit case"}, "year": {"type": "integer", "description": "The year in which the lawsuit case was initiated. Default is 2023 if not specified.", "optional": true, "default": 2023}, "location": {"type": "string", "description": "The location or court jurisdiction where the case was filed. Default is 'all'.", "optional": true}}, "required": ["case_number"]}}]}
{"id": "simple_183", "question": [[{"role": "user", "content": "Search for current lawsuits filed against Apple in Santa Clara County."}]], "function": [{"name": "lawsuit_search", "description": "Retrieve all lawsuits involving a particular entity from specified jurisdiction.", "parameters": {"type": "dict", "properties": {"entity": {"type": "string", "description": "The entity involved in lawsuits."}, "county": {"type": "string", "description": "The jurisdiction for the lawsuit search for example Alameda county."}, "state": {"type": "string", "description": "The state for the lawsuit search. Default is California."}}, "required": ["entity", "county"]}}]}
{"id": "simple_184", "question": [[{"role": "user", "content": "I need the details of the lawsuit case with case ID of 1234 and verify if it's already closed."}]], "function": [{"name": "lawsuit.check_case", "description": "Verify the details of a lawsuit case and check its status using case ID.", "parameters": {"type": "dict", "properties": {"case_id": {"type": "integer", "description": "The identification number of the lawsuit case."}, "closed_status": {"type": "boolean", "description": "The status of the lawsuit case to be verified."}}, "required": ["case_id", "closed_status"]}}]}
{"id": "simple_185", "question": [[{"role": "user", "content": "What will be the weather in New York in the next 72 hours including the precipitation?"}]], "function": [{"name": "detailed_weather_forecast", "description": "Retrieve a detailed weather forecast for a specific location and duration including optional precipitation details.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city name that you want to get the weather for."}, "duration": {"type": "integer", "description": "Duration in hours for the detailed forecast."}, "include_precipitation": {"type": "boolean", "description": "Whether to include precipitation data in the forecast. Default is false."}}, "required": ["location", "duration"]}}]}
{"id": "simple_186", "question": [[{"role": "user", "content": "What is the temperature in celsius and humidity level of Tokyo, Japan right now?"}]], "function": [{"name": "current_weather_condition", "description": "Get the current weather conditions of a specific city including temperature and humidity.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city that you want to get the current weather conditions for."}, "country": {"type": "string", "description": "The country of the city you specified."}, "measurement": {"type": "string", "description": "You can specify which unit to display the temperature in, 'c' for Celsius, 'f' for Fahrenheit. Default is 'c'."}}, "required": ["city", "country"]}}]}
{"id": "simple_187", "question": [[{"role": "user", "content": "What's the current temperature and humidity in Seattle, Washington?"}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current temperature and humidity for a specific location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city name to get the weather for."}, "include_temperature": {"type": "boolean", "description": "Whether to include the temperature in the result. Default is true."}, "include_humidity": {"type": "boolean", "description": "Whether to include the humidity in the result. Default is true."}}, "required": ["location"]}}]}
{"id": "simple_188", "question": [[{"role": "user", "content": "What is the humidity level in Miami, Florida in the upcoming 7 days?"}]], "function": [{"name": "weather.humidity_forecast", "description": "Retrieve a humidity forecast for a specific location and time frame.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the humidity for."}, "days": {"type": "integer", "description": "Number of days for the forecast."}, "min_humidity": {"type": "integer", "description": "Minimum level of humidity (in percentage) to filter the result. Default is 0."}}, "required": ["location", "days"]}}]}
{"id": "simple_189", "question": [[{"role": "user", "content": "Get weather information for New York, USA for the next 3 days with details."}]], "function": [{"name": "weather_forecast_detailed", "description": "Retrieve a detailed weather forecast for a specific city like Boston and time frame.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the weather for."}, "days": {"type": "integer", "description": "Number of days for the forecast."}, "details": {"type": "boolean", "description": "Provide detailed weather information or not.", "default": false}}, "required": ["location", "days"]}}]}
{"id": "simple_190", "question": [[{"role": "user", "content": "What's the elevation and area of Yellowstone National Park?"}]], "function": [{"name": "park_information", "description": "Retrieve the basic information such as elevation and area of a national park.", "parameters": {"type": "dict", "properties": {"park_name": {"type": "string", "description": "The name of the national park."}, "information": {"type": "array", "items": {"type": "string", "enum": ["Elevation", "Area", "Location", "Established Year"]}, "description": "The type of information you want about the park."}}, "required": ["park_name", "information"]}}]}
{"id": "simple_191", "question": [[{"role": "user", "content": "Find me the 5 tallest mountains within 50km of Denver, Colorado."}]], "function": [{"name": "locate_tallest_mountains", "description": "Find the tallest mountains within a specified radius of a location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city from which to calculate distance."}, "radius": {"type": "integer", "description": "The radius within which to find mountains, measured in kilometers."}, "amount": {"type": "integer", "description": "The number of mountains to find, listed from tallest to smallest."}}, "required": ["location", "radius", "amount"]}}]}
{"id": "simple_192", "question": [[{"role": "user", "content": "Calculate the slope gradient in degree between two points on a landscape with coordinates (40.7128, -74.0060) and (34.0522, -118.2437)."}]], "function": [{"name": "calculate_slope_gradient", "description": "Calculate the slope gradient between two geographical coordinates.", "parameters": {"type": "dict", "properties": {"point1": {"type": "array", "items": {"type": "float"}, "description": "The geographic coordinates for the first point [Latitude, Longitude]."}, "point2": {"type": "array", "items": {"type": "float"}, "description": "The geographic coordinates for the second point [Latitude, Longitude]."}, "unit": {"type": "string", "enum": ["degree", "percent", "ratio"], "description": "The unit for the slope gradient. Default is 'degree'."}}, "required": ["point1", "point2"]}}]}
{"id": "simple_193", "question": [[{"role": "user", "content": "Find the best local nurseries in Toronto with a good variety of annual plants."}]], "function": [{"name": "local_nursery.find", "description": "Locate local nurseries based on location and plant types availability.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city or locality where the nursery needs to be located."}, "plant_types": {"type": "array", "items": {"type": "string", "enum": ["Annual", "Perennial", "Shrub", "Tree", "Herbs", "Fruits"]}, "description": "Type of plants the nursery should provide."}}, "required": ["location", "plant_types"]}}]}
{"id": "simple_194", "question": [[{"role": "user", "content": "What are the top three plants suitable for a hill slope in terms of erosion prevention?"}]], "function": [{"name": "get_plants_for_slope", "description": "Retrieve the list of plants suitable for slope based on erosion control ability.", "parameters": {"type": "dict", "properties": {"slope_type": {"type": "string", "description": "The type of slope like steep, moderate etc."}, "num_results": {"type": "integer", "description": "The number of top results needed. Default is 5."}}, "required": ["slope_type", "num_results"]}}]}
{"id": "simple_195", "question": [[{"role": "user", "content": "Calculate the carbon footprint of my lifestyle, assuming I drive 20 miles a day, consume 3 meat meals a week, and produce 500 lbs of trash in a year."}]], "function": [{"name": "calculate_carbon_footprint", "description": "Calculate the estimated carbon footprint of a lifestyle based on factors such as daily driving distance, weekly meat consumption, and yearly trash production.", "parameters": {"type": "dict", "properties": {"daily_miles": {"type": "integer", "description": "The daily driving distance in miles."}, "meat_meals_per_week": {"type": "integer", "description": "The number of meat-based meals consumed per week."}, "annual_trash_weight": {"type": "integer", "description": "The yearly weight of trash production in pounds."}, "flights_per_year": {"type": "integer", "description": "The number of flights taken per year. Default is 0."}}, "required": ["daily_miles", "meat_meals_per_week", "annual_trash_weight"]}}]}
{"id": "simple_196", "question": [[{"role": "user", "content": "What is the air quality index in London 2022/08/16?"}]], "function": [{"name": "air_quality", "description": "Retrieve the air quality index for a specific location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the air quality index for."}, "date": {"type": "string", "description": "The date (month-day-year) you want to get the air quality index for."}}, "required": ["location", "date"]}}]}
{"id": "simple_197", "question": [[{"role": "user", "content": "Find the air quality index in San Diego at 12pm."}]], "function": [{"name": "get_air_quality_index", "description": "Retrieve the air quality index at a specified location and time.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location to get the air quality index for."}, "time": {"type": "string", "description": "The specific time to check the air quality. Default is the current time."}}, "required": ["location", "time"]}}]}
{"id": "simple_198", "question": [[{"role": "user", "content": "Calculate the required water daily intake for a person with weight 70 kg."}]], "function": [{"name": "calculate_daily_water_intake", "description": "Calculate the recommended daily water intake for a person based on their weight.", "parameters": {"type": "dict", "properties": {"weight": {"type": "integer", "description": "The weight of the person in kilograms."}, "activity_level": {"type": "string", "description": "The level of physical activity of the person. Default is 'moderate'."}, "climate": {"type": "string", "description": "The climate of the area where the person lives. Default is 'temperate'."}}, "required": ["weight"]}}]}
{"id": "simple_199", "question": [[{"role": "user", "content": "Find air quality index in San Jose for next three days."}]], "function": [{"name": "environmental_data.air_quality_index", "description": "Retrieves Air Quality Index (AQI) for specified location over a number of days.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "Name of the city or town to retrieve air quality index for."}, "days": {"type": "integer", "description": "Number of days for which to retrieve data. If not provided, default to today."}}, "required": ["location"]}}]}
{"id": "simple_200", "question": [[{"role": "user", "content": "How much CO2 is produced annually by a gas-fueled car that travels 12,000 miles per year, with fuel efficiency of 25 MPG ?"}]], "function": [{"name": "calculate_emissions", "description": "Calculates the annual carbon dioxide emissions produced by a vehicle based on the distance traveled, the fuel type and the fuel efficiency of the vehicle.", "parameters": {"type": "dict", "properties": {"distance": {"type": "integer", "description": "The distance travelled in miles."}, "fuel_type": {"type": "string", "description": "Type of fuel used by the vehicle."}, "fuel_efficiency": {"type": "float", "description": "The vehicle's fuel efficiency in miles per gallon."}, "efficiency_reduction": {"type": "integer", "description": "The percentage decrease in fuel efficiency per year (optional). Default is 0"}}, "required": ["distance", "fuel_type", "fuel_efficiency"]}}]}
{"id": "simple_201", "question": [[{"role": "user", "content": "Estimate the population of pandas in the wild in China."}]], "function": [{"name": "estimate_population", "description": "Estimate the population of a particular species in a given country.", "parameters": {"type": "dict", "properties": {"species": {"type": "string", "description": "The species for which population needs to be estimated."}, "country": {"type": "string", "description": "The country where the species lives."}, "year": {"type": "integer", "description": "The year for which population estimate is sought. Default is the current year."}}, "required": ["species", "country"]}}]}
{"id": "simple_202", "question": [[{"role": "user", "content": "How many greenhouse gas emissions would I save if I switched to renewable energy sources for 3 months in California?"}]], "function": [{"name": "calculate_emission_savings", "description": "Calculate potential greenhouse gas emissions saved by switching to renewable energy sources.", "parameters": {"type": "dict", "properties": {"energy_type": {"type": "string", "description": "Type of the renewable energy source."}, "usage_duration": {"type": "integer", "description": "Usage duration in months."}, "region": {"type": "string", "description": "The region where you use energy. Default is 'Texas'."}}, "required": ["energy_type", "usage_duration"]}}]}
{"id": "simple_203", "question": [[{"role": "user", "content": "Can you find me the latest information about air quality index and pollution data for Chicago?"}]], "function": [{"name": "get_air_quality", "description": "Retrieve real-time air quality and pollution data for a specific location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the air quality data for."}, "detail": {"type": "boolean", "description": "If true, additional data like PM2.5, PM10, ozone levels, and pollution sources will be retrieved. Default is false."}}, "required": ["location"]}}]}
{"id": "simple_204", "question": [[{"role": "user", "content": "Find restaurants near me within 10 miles that offer Chinese cuisine in Seattle."}]], "function": [{"name": "restaurant.find_nearby", "description": "Locate nearby restaurants based on specific criteria like cuisine type.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Seattle, WA"}, "cuisine": {"type": "string", "description": "Preferred type of cuisine in restaurant."}, "max_distance": {"type": "integer", "description": "Maximum distance (in miles) within which to search for restaurants. Default is 5."}}, "required": ["location", "cuisine"]}}]}
{"id": "simple_205", "question": [[{"role": "user", "content": "Find out the current traffic situation from Boston driving to New York."}]], "function": [{"name": "get_traffic_info", "description": "Retrieve current traffic conditions for a specified route.", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "The starting point of the route."}, "end_location": {"type": "string", "description": "The destination of the route."}, "mode": {"type": "string", "enum": ["driving", "walking", "bicycling", "transit"], "description": "Preferred method of transportation, default to 'driving'."}}, "required": ["start_location", "end_location"]}}]}
{"id": "simple_206", "question": [[{"role": "user", "content": "Find the nearest park with a tennis court in London."}]], "function": [{"name": "parks.find_nearby", "description": "Locate nearby parks based on specific criteria like tennis court availability.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. London, UK"}, "amenities": {"type": "array", "items": {"type": "string", "enum": ["Tennis Court", "Picnic Area", "Playground", "Running Track"]}, "description": "Preferred amenities in park. Default is ['Running Track']"}}, "required": ["location"]}}]}
{"id": "simple_207", "question": [[{"role": "user", "content": "Get the shortest driving distance between New York, USA and Miami, USA."}]], "function": [{"name": "calculate_shortest_distance", "description": "Calculate the shortest driving distance between two locations.", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "The starting location for the drive."}, "end_location": {"type": "string", "description": "The destination location for the drive."}, "route_preference": {"type": "string", "enum": ["Shortest", "Scenic"], "description": "The preferred type of route."}}, "required": ["start_location", "end_location", "route_preference"]}}]}
{"id": "simple_208", "question": [[{"role": "user", "content": "Get me the directions from New York to Los Angeles avoiding highways and toll roads."}]], "function": [{"name": "map_service.get_directions", "description": "Retrieve directions from a starting location to an ending location, including options for route preferences.", "parameters": {"type": "dict", "properties": {"start": {"type": "string", "description": "Starting location for the route."}, "end": {"type": "string", "description": "Ending location for the route."}, "avoid": {"type": "array", "items": {"type": "string", "enum": ["tolls", "highways", "ferries"]}, "description": "Route features to avoid. Default is ['highways', 'ferries']"}}, "required": ["start", "end"]}}]}
{"id": "simple_209", "question": [[{"role": "user", "content": "Locate the nearest public library in Boston, Massachusetts with English fiction section and free Wi-Fi."}]], "function": [{"name": "public_library.find_nearby", "description": "Locate nearby public libraries based on specific criteria like English fiction availability and Wi-Fi.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Boston, MA"}, "facilities": {"type": "array", "items": {"type": "string", "enum": ["Wi-Fi", "Reading Room", "Fiction", "Children Section", "Cafe"]}, "description": "Facilities and sections in public library."}}, "required": ["location", "facilities"]}}]}
{"id": "simple_210", "question": [[{"role": "user", "content": "Get 5 latest news on Bitcoin in US"}]], "function": [{"name": "get_news", "description": "Fetches the latest news on a specific topic.", "parameters": {"type": "dict", "properties": {"topic": {"type": "string", "description": "The subject for the news topic."}, "quantity": {"type": "integer", "description": "Number of articles to fetch."}, "region": {"type": "string", "description": "The geographical region for the news. Default is 'US'."}}, "required": ["topic", "quantity"]}}]}
{"id": "simple_211", "question": [[{"role": "user", "content": "Send an email to John <NAME_EMAIL> with the subject 'Meeting' and body 'Let's meet at 10 AM tomorrow'."}]], "function": [{"name": "send_email", "description": "Send an email to the specified email address.", "parameters": {"type": "dict", "properties": {"to": {"type": "string", "description": "The email address to send to."}, "subject": {"type": "string", "description": "The subject of the email."}, "body": {"type": "string", "description": "The body content of the email."}, "cc": {"type": "string", "description": "The email address to carbon copy. Default is empty if not specified."}, "bcc": {"type": "string", "description": "The email address to blind carbon copy. Default is empty if not specified."}}, "required": ["to", "subject", "body"]}}]}
{"id": "simple_212", "question": [[{"role": "user", "content": "Give me detail information about stocks of Apple Inc."}]], "function": [{"name": "get_stock_info", "description": "Retrieves information about a specific stock based on company's name.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "The name of the company."}, "detail_level": {"type": "string", "description": "Level of detail for stock information. Can be 'summary' or 'detailed'."}, "market": {"type": "string", "description": "The stock market of interest. Default is 'NASDAQ'"}}, "required": ["company_name", "detail_level"]}}]}
{"id": "simple_213", "question": [[{"role": "user", "content": "Book a direct flight from San Francisco to London for 2022-04-27 afternoon"}]], "function": [{"name": "flight.book", "description": "Book a direct flight for a specific date and time from departure location to destination location.", "parameters": {"type": "dict", "properties": {"departure_location": {"type": "string", "description": "The location you are departing from."}, "destination_location": {"type": "string", "description": "The location you are flying to."}, "date": {"type": "string", "description": "The date of the flight. Accepts standard date format e.g., 2022-04-28."}, "time": {"type": "string", "description": "Preferred time of flight. Default is 'morning'."}, "direct_flight": {"type": "boolean", "description": "If set to true, only direct flights will be searched. Default is false."}}, "required": ["departure_location", "destination_location", "date"]}}]}
{"id": "simple_214", "question": [[{"role": "user", "content": "Search for upcoming month rock concerts in New York."}]], "function": [{"name": "event_finder.find_upcoming", "description": "Find upcoming events of a specific genre in a given location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state where the search will take place, e.g. New York, NY."}, "genre": {"type": "string", "description": "The genre of events."}, "days_ahead": {"type": "integer", "description": "The number of days from now to include in the search.", "default": 7}}, "required": ["location", "genre"]}}]}
{"id": "simple_215", "question": [[{"role": "user", "content": "Give me a brief on movie 'Interstellar'"}]], "function": [{"name": "movie_details.brief", "description": "This function retrieves a brief about a specified movie.", "parameters": {"type": "dict", "properties": {"title": {"type": "string", "description": "Title of the movie"}, "extra_info": {"type": "boolean", "description": "Option to get additional information like Director, Cast, Awards etc.", "default": "false"}}, "required": ["title"]}}]}
{"id": "simple_216", "question": [[{"role": "user", "content": "Analyze the sentiment of a customer review 'I love the food here! It's always fresh and delicious.'."}]], "function": [{"name": "sentiment_analysis", "description": "Perform sentiment analysis on a given piece of text.", "parameters": {"type": "dict", "properties": {"text": {"type": "string", "description": "The text on which to perform sentiment analysis."}, "language": {"type": "string", "description": "The language in which the text is written."}}, "required": ["text", "language"]}}]}
{"id": "simple_217", "question": [[{"role": "user", "content": "Analyze my fMRI data in ~/data/myfMRI.nii from a multi-band sequence, that is smoothed at 6mm with an isotropic voxel size of 2mm."}]], "function": [{"name": "fMRI.analyze", "description": "This function takes in fMRI data to output analyzed data.", "parameters": {"type": "dict", "properties": {"data_source": {"type": "string", "description": "The path where the data is stored."}, "sequence_type": {"type": "string", "description": "Type of fMRI sequence"}, "smooth": {"type": "integer", "description": "Spatial smoothing FWHM. In mm."}, "voxel_size": {"type": "integer", "description": "Size of isotropic voxels in mm.", "default": 3}}, "required": ["data_source", "sequence_type", "smooth"]}}]}
{"id": "simple_218", "question": [[{"role": "user", "content": "Given patient with id 546382, retrieve their brain MRI report with the status 'concluded'."}]], "function": [{"name": "patient.get_mri_report", "description": "Fetch the brain MRI report of the patient for a given status.", "parameters": {"type": "dict", "properties": {"patient_id": {"type": "string", "description": "The patient identifier."}, "mri_type": {"type": "string", "description": "Type of the MRI. Default to be 'brain'.", "enum": ["brain", "spinal", "chest", "abdominal"]}, "status": {"type": "string", "description": "Status of the report, could be 'in progress', 'concluded' or 'draft'.", "enum": ["in progress", "concluded", "draft"]}}, "required": ["patient_id", "status"]}}]}
{"id": "simple_219", "question": [[{"role": "user", "content": "What are the coordinates of the neuron in a rat's all part of the brain that produces GABA neurotransmitters?"}]], "function": [{"name": "get_neuron_coordinates", "description": "Retrieve the coordinates of the specified neuron in the rat's brain.", "parameters": {"type": "dict", "properties": {"neuron_type": {"type": "string", "description": "Type of neuron to find. For instance, GABA, Glutamate, etc."}, "brain_region": {"type": "string", "description": "The region of the brain to consider.", "default": "All"}}, "required": ["neuron_type", "brain_region"]}}]}
{"id": "simple_220", "question": [[{"role": "user", "content": "Calculate the neuronal activity based on synaptic input rate of 200 and weight 0.5 and decay rate of 0.1."}]], "function": [{"name": "calculate_neuronal_activity", "description": "Calculate the neuronal activity (rate of firing) based on a given input synaptic rate, weight of inputs, and decay rate. Higher input or weight increases firing rate and higher decay rate decreases it.", "parameters": {"type": "dict", "properties": {"input_synaptic_rate": {"type": "integer", "description": "The synaptic input rate, usually represented as number of inputs per second."}, "weight": {"type": "float", "description": "The weight of the input, denoting its influence on the neuron's state. Default is 1.0."}, "decay_rate": {"type": "float", "description": "The rate at which the neuron's potential decays in the absence of inputs."}}, "required": ["input_synaptic_rate", "decay_rate"]}}]}
{"id": "simple_221", "question": [[{"role": "user", "content": "What will be the population growth in London over the next five years?"}]], "function": [{"name": "population_growth_estimate", "description": "Estimate the future population growth of a specific location over a specified time period.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to estimate the population growth for."}, "years": {"type": "integer", "description": "Number of years into the future for the estimate."}, "rate": {"type": "float", "description": "Expected annual growth rate in percentage. Default is 1.2."}}, "required": ["location", "years"]}}]}
{"id": "simple_222", "question": [[{"role": "user", "content": "Can you calculate my Body Mass Index (BMI) given my weight is 70 kg and height is 180 cm?"}]], "function": [{"name": "calculate_bmi", "description": "Calculate the Body Mass Index based on given weight and height.", "parameters": {"type": "dict", "properties": {"weight": {"type": "integer", "description": "The weight of a person in kilograms."}, "height": {"type": "integer", "description": "The height of a person in centimeters."}, "unit": {"type": "string", "description": "Optional. The measurement system to be used for the result. The default is 'metric'."}}, "required": ["weight", "height"]}}]}
{"id": "simple_223", "question": [[{"role": "user", "content": "Find social behaviors and patterns in a group size of 50 with extroverted members being 15 and introverted members being 35."}]], "function": [{"name": "group_dynamics.pattern", "description": "Examine the social dynamics and interactions within a group based on the personality traits and group size.", "parameters": {"type": "dict", "properties": {"total": {"type": "integer", "description": "The total group size."}, "extroverts": {"type": "integer", "description": "The number of extroverted members in the group."}, "introverts": {"type": "integer", "description": "The number of introverted members in the group."}}, "required": ["total", "extroverts", "introverts"]}}]}
{"id": "simple_224", "question": [[{"role": "user", "content": "Find the most followed person on twitter who tweets about psychology related to behaviour and group dynamics."}]], "function": [{"name": "social_media_analytics.most_followed", "description": "Find the most followed Twitter user related to certain topics.", "parameters": {"type": "dict", "properties": {"topic": {"type": "string", "description": "The main topic of interest."}, "sub_topics": {"type": "array", "items": {"type": "string"}, "description": "Sub-topics related to main topic. Default is empty."}, "region": {"type": "string", "description": "Region of interest for twitter search. Default is 'all'."}}, "required": ["topic"]}}]}
{"id": "simple_225", "question": [[{"role": "user", "content": "What is the percentage of population preferring digital reading over physical books?"}]], "function": [{"name": "psych_research.get_preference", "description": "Gathers research data on public preference between two options, based on societal category.", "parameters": {"type": "dict", "properties": {"category": {"type": "string", "description": "The societal category the preference data is about. E.g. reading, transportation, food"}, "option_one": {"type": "string", "description": "The first option people could prefer."}, "option_two": {"type": "string", "description": "The second option people could prefer."}, "demographic": {"type": "string", "description": "Specific demographic of society to narrow down the research.", "default": "all"}}, "required": ["category", "option_one", "option_two"]}}]}
{"id": "simple_226", "question": [[{"role": "user", "content": "Find the compatibility score in percentage of Aries with Gemini."}]], "function": [{"name": "get_zodiac_compatibility", "description": "Retrieve the compatibility score between two Zodiac signs.", "parameters": {"type": "dict", "properties": {"sign1": {"type": "string", "description": "The first Zodiac sign."}, "sign2": {"type": "string", "description": "The second Zodiac sign."}, "scale": {"type": "string", "enum": ["percentage", "0-10 scale"], "description": "The scale on which compatibility should be shown. Default is 'percentage'."}}, "required": ["sign1", "sign2"]}}]}
{"id": "simple_227", "question": [[{"role": "user", "content": "Get me strength and weakness traits for ENFJ personality type."}]], "function": [{"name": "get_personality_traits", "description": "Retrieve the personality traits for a specific personality type, including their strengths and weaknesses.", "parameters": {"type": "dict", "properties": {"type": {"type": "string", "description": "The personality type."}, "traits": {"type": "array", "items": {"type": "string", "enum": ["strengths", "weaknesses"]}, "description": "List of traits to be retrieved, default is ['strengths']."}}, "required": ["type"]}}]}
{"id": "simple_228", "question": [[{"role": "user", "content": "Find three personality traits of people who like jogging."}]], "function": [{"name": "get_personality_traits", "description": "Retrieve the common personality traits of people based on their hobbies or activities.", "parameters": {"type": "dict", "properties": {"hobby": {"type": "string", "description": "The hobby or activity of interest."}, "trait_count": {"type": "integer", "description": "The number of top traits to return, default is 5"}}, "required": ["hobby"]}}]}
{"id": "simple_229", "question": [[{"role": "user", "content": "What's my Big Five Personality trait scores given that I am efficient, organized, easy going and compassionate?"}]], "function": [{"name": "get_bigfive_scores", "description": "Retrieve Big Five Personality trait scores based on individual's behavioural characteristics.", "parameters": {"type": "dict", "properties": {"characteristics": {"type": "array", "items": {"type": "string"}, "description": "List of user's behavioural characteristics."}, "scale": {"type": "string", "enum": ["high", "medium", "low"], "description": "The scoring scale of traits (default is medium)."}}, "required": ["characteristics"]}}]}
{"id": "simple_230", "question": [[{"role": "user", "content": "Who was the King of France in 1510?"}]], "function": [{"name": "historic_leader_search", "description": "Retrieve information about a historical leader given a location and date.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The country or region in question."}, "date": {"type": "integer", "description": "The year being queried."}, "title": {"type": "string", "description": "The official title of the position. Default is 'King'."}}, "required": ["location", "date"]}}]}
{"id": "simple_231", "question": [[{"role": "user", "content": "Provide key war events in German history from 1871 to 1945."}]], "function": [{"name": "history.get_key_events", "description": "Retrieve key historical events within a specific period for a certain country.", "parameters": {"type": "dict", "properties": {"country": {"type": "string", "description": "The name of the country for which history is queried."}, "start_year": {"type": "integer", "description": "Start year of the period for which history is queried."}, "end_year": {"type": "integer", "description": "End year of the period for which history is queried."}, "event_type": {"type": "array", "items": {"type": "string", "enum": ["War", "Revolutions", "Diplomacy", "Economy"]}, "description": "Types of event. Default to 'all', which all types will be considered."}}, "required": ["country", "start_year", "end_year"]}}]}
{"id": "simple_232", "question": [[{"role": "user", "content": "What was the full name king of England in 1800?"}]], "function": [{"name": "monarch.getMonarchOfYear", "description": "Retrieve the monarch of a specific location during a specified year.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location (e.g., country) whose monarch needs to be found."}, "year": {"type": "integer", "description": "The year to search the monarch."}, "fullName": {"type": "boolean", "default": false, "description": "If true, returns the full name and title of the monarch."}}, "required": ["location", "year"]}}]}
{"id": "simple_233", "question": [[{"role": "user", "content": "When did the Treaty of Tordesillas take place? Put it in the format of YYYY."}]], "function": [{"name": "european_history.get_event_date", "description": "Retrieve the date of a specific event in European history.", "parameters": {"type": "dict", "properties": {"event_name": {"type": "string", "description": "The name of the event."}, "format": {"type": "string", "description": "Optional format of the returned date. Default is 'MM-DD-YYYY'."}}, "required": ["event_name"]}}]}
{"id": "simple_234", "question": [[{"role": "user", "content": "Find important Wars in European history during the 19th century."}]], "function": [{"name": "history_eu.fetch_events", "description": "Fetches significant historical events within a specific time period in European history.", "parameters": {"type": "dict", "properties": {"century": {"type": "integer", "description": "The century you are interested in."}, "region": {"type": "string", "description": "The region of Europe you are interested in.", "enum": ["Northern", "Southern", "Eastern", "Western"]}, "category": {"type": "string", "description": "Category of the historical events. Default is 'Culture'.", "enum": ["Wars", "Culture", "Politics", "Scientific", "Others"]}}, "required": ["century", "region"]}}]}
{"id": "simple_235", "question": [[{"role": "user", "content": "When was the signing of the Treaty of Lisbon?"}]], "function": [{"name": "get_event_date", "description": "Retrieve the date of a historical event.", "parameters": {"type": "dict", "properties": {"event": {"type": "string", "description": "The name of the historical event."}, "location": {"type": "string", "description": "Location where the event took place. Default to global if not specified."}}, "required": ["event"]}}]}
{"id": "simple_236", "question": [[{"role": "user", "content": "Get start date on the American Civil War."}]], "function": [{"name": "us_history.get_event_info", "description": "Retrieve detailed information about a significant event in U.S. history.", "parameters": {"type": "dict", "properties": {"event_name": {"type": "string", "description": "The name of the event."}, "specific_info": {"type": "string", "description": "Specific aspect of information related to event.", "enum": ["Start Date", "End Date", "Participants", "Result", "Notable Figures", "Importance in History"]}}, "required": ["event_name", "specific_info"]}}]}
{"id": "simple_237", "question": [[{"role": "user", "content": "Get historical GDP data for United States from 1960 to 2000."}]], "function": [{"name": "get_historical_GDP", "description": "Retrieve historical GDP data for a specific country and time range.", "parameters": {"type": "dict", "properties": {"country": {"type": "string", "description": "The country for which the historical GDP data is required."}, "start_year": {"type": "integer", "description": "Starting year of the period for which GDP data is required."}, "end_year": {"type": "integer", "description": "Ending year of the period for which GDP data is required."}}, "required": ["country", "start_year", "end_year"]}}]}
{"id": "simple_238", "question": [[{"role": "user", "content": "Who was the president of the United States during the American Civil War?"}]], "function": [{"name": "us_history.get_president", "description": "Retrieve the U.S. president during a specific event in American history.", "parameters": {"type": "dict", "properties": {"event": {"type": "string", "description": "The event in U.S. history."}, "year": {"type": "integer", "description": "The specific year of the event."}}, "required": ["event", "year"]}}]}
{"id": "simple_239", "question": [[{"role": "user", "content": "Who was the full name of the president of the United States in 1861?"}]], "function": [{"name": "US_president.in_year", "description": "Retrieve the name of the U.S. president in a given year.", "parameters": {"type": "dict", "properties": {"year": {"type": "integer", "description": "The year in question."}, "full_name": {"type": "boolean", "default": true, "description": "Option to return full name with middle initial, if applicable."}}, "required": ["year"]}}]}
{"id": "simple_240", "question": [[{"role": "user", "content": "Who was the President of the United States in 1940?"}]], "function": [{"name": "history_api.get_president_by_year", "description": "Get the name of the U.S. President for a specified year.", "parameters": {"type": "dict", "properties": {"year": {"type": "integer", "description": "The year you want to know the U.S. president of."}, "full_term_only": {"type": "boolean", "description": "Flag to determine if we should only return presidents that served a full term for the specified year.", "default": false}}, "required": ["year"]}}]}
{"id": "simple_241", "question": [[{"role": "user", "content": "Who was the U.S. president during the Civil War?"}]], "function": [{"name": "US_President_During_Event", "description": "Returns the U.S. president during a specified historical event.", "parameters": {"type": "dict", "properties": {"event": {"type": "string", "description": "The historical event."}, "country": {"type": "string", "description": "The country the president leads (optional parameter, defaults to 'USA' if not specified)."}}, "required": ["event"]}}]}
{"id": "simple_242", "question": [[{"role": "user", "content": "Who is the scientist that first proposed the theory of evolution?"}]], "function": [{"name": "get_scientist_for_discovery", "description": "Retrieve the scientist's name who is credited for a specific scientific discovery or theory.", "parameters": {"type": "dict", "properties": {"discovery": {"type": "string", "description": "The scientific discovery or theory."}}, "required": ["discovery"]}}]}
{"id": "simple_243", "question": [[{"role": "user", "content": "Who discovered the neutron? Give me detail information."}]], "function": [{"name": "get_discoverer", "description": "Get the person or team who made a particular scientific discovery", "parameters": {"type": "dict", "properties": {"discovery": {"type": "string", "description": "The discovery for which the discoverer's information is needed."}, "detail": {"type": "boolean", "description": "Optional flag to get additional details about the discoverer, such as birth date and nationality. Defaults to false."}}, "required": ["discovery", "detail"]}}]}
{"id": "simple_244", "question": [[{"role": "user", "content": "What year was the law of universal gravitation published by Isaac Newton?"}]], "function": [{"name": "publication_year.find", "description": "Fetches the year a particular scientific work was published.", "parameters": {"type": "dict", "properties": {"author": {"type": "string", "description": "Name of the author of the work."}, "work_title": {"type": "string", "description": "Title of the scientific work."}, "location": {"type": "string", "description": "Place of the publication, if known. Default to 'all'."}}, "required": ["author", "work_title"]}}]}
{"id": "simple_245", "question": [[{"role": "user", "content": "Who discovered radium?"}]], "function": [{"name": "discoverer.get", "description": "Retrieve the name of the discoverer of an element based on its name.", "parameters": {"type": "dict", "properties": {"element_name": {"type": "string", "description": "The name of the element."}, "year": {"type": "integer", "description": "Optional parameter that refers to the year of discovery. It could be helpful in case an element was discovered more than once. Default to 0, which means not use it."}, "first": {"type": "boolean", "default": true, "description": "Optional parameter indicating if the first discoverer's name should be retrieved."}}, "required": ["element_name"]}}]}
{"id": "simple_246", "question": [[{"role": "user", "content": "Who discovered Gravity and what was the method used?"}]], "function": [{"name": "science_history.get_discovery_details", "description": "Retrieve the details of a scientific discovery based on the discovery name.", "parameters": {"type": "dict", "properties": {"discovery": {"type": "string", "description": "The name of the discovery, e.g. Gravity"}, "method_used": {"type": "string", "description": "The method used for the discovery, default value is 'default' which gives the most accepted method."}}, "required": ["discovery"]}}]}
{"id": "simple_247", "question": [[{"role": "user", "content": "What was Albert Einstein's contribution to science on March 17, 1915?"}]], "function": [{"name": "historical_contrib.get_contrib", "description": "Retrieve historical contribution made by a scientist on a specific date.", "parameters": {"type": "dict", "properties": {"scientist": {"type": "string", "description": "The scientist whose contributions need to be searched."}, "date": {"type": "string", "description": "The date when the contribution was made in yyyy-mm-dd format."}, "category": {"type": "string", "description": "The field of the contribution, such as 'Physics' or 'Chemistry'. Default is 'all'."}}, "required": ["scientist", "date"]}}]}
{"id": "simple_248", "question": [[{"role": "user", "content": "Who invented the theory of relativity and in which year?"}]], "function": [{"name": "science_history.get_invention", "description": "Retrieve the inventor and year of invention based on the invention's name.", "parameters": {"type": "dict", "properties": {"invention_name": {"type": "string", "description": "The name of the invention."}, "want_year": {"type": "boolean", "default": false, "description": "Return the year of invention if set to true."}}, "required": ["invention_name", "want_year"]}}]}
{"id": "simple_249", "question": [[{"role": "user", "content": "Tell me more about Christianity and its history till the 14th century"}]], "function": [{"name": "religion.history_info", "description": "Provides comprehensive historical details about a specified religion till a specified century.", "parameters": {"type": "dict", "properties": {"religion": {"type": "string", "description": "The name of the religion for which historical details are needed."}, "till_century": {"type": "integer", "description": "The century till which historical details are needed."}, "include_people": {"type": "boolean", "description": "To include influential people related to the religion during that time period, default is False"}}, "required": ["religion", "till_century"]}}]}
{"id": "simple_250", "question": [[{"role": "user", "content": "What's the time difference between San Francisco and Sydney?"}]], "function": [{"name": "get_time_difference", "description": "Get the time difference between two places.", "parameters": {"type": "dict", "properties": {"place1": {"type": "string", "description": "The first place for time difference."}, "place2": {"type": "string", "description": "The second place for time difference."}}, "required": ["place1", "place2"]}}]}
{"id": "simple_251", "question": [[{"role": "user", "content": "What is the earliest reference of Jesus Christ in history from historical record?"}]], "function": [{"name": "get_earliest_reference", "description": "Retrieve the earliest historical reference of a person.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The name of the person."}, "source": {"type": "string", "enum": ["scriptures", "historical records"], "description": "Source to fetch the reference. Default is 'scriptures'"}}, "required": ["name"]}}]}
{"id": "simple_252", "question": [[{"role": "user", "content": "Find ten major historical events related to Christianity in the 16th century sort by importance."}]], "function": [{"name": "get_religion_history", "description": "Retrieves significant religious events, including the details of the event, its historical context, and its impacts.", "parameters": {"type": "dict", "properties": {"religion": {"type": "string", "description": "Name of the religion to be queried."}, "century": {"type": "integer", "description": "The century in which the event(s) took place."}, "sort_by": {"type": "string", "enum": ["importance", "chronological"], "default": "chronological", "description": "Order of sorting the events. Default is chronological."}, "count": {"type": "integer", "default": 5, "description": "Number of events to return. Default is 5."}}, "required": ["religion", "century"]}}]}
{"id": "simple_253", "question": [[{"role": "user", "content": "Retrieve the full historyof Buddhism"}]], "function": [{"name": "retrieve_religion_info", "description": "Retrieve the history and main beliefs of a religion.", "parameters": {"type": "dict", "properties": {"religion_name": {"type": "string", "description": "The name of the religion."}, "detail_level": {"type": "string", "description": "Level of detail for the returned information, either 'summary' or 'full'.", "default": "summary"}}, "required": ["religion_name", "detail_level"]}}]}
{"id": "simple_254", "question": [[{"role": "user", "content": "Retrieve the historic dates and facts related to Christianity between year 300 and 400."}]], "function": [{"name": "get_religion_history", "description": "Retrieves historic events and facts related to a specified religion for a given period.", "parameters": {"type": "dict", "properties": {"religion": {"type": "string", "description": "The name of the religion."}, "start_year": {"type": "integer", "description": "The starting year of the period."}, "end_year": {"type": "integer", "description": "The end year of the period."}, "event_type": {"type": "string", "enum": ["all", "crusade", "schism", "reform"], "description": "Optional parameter specifying the type of event. Default is 'all'."}}, "required": ["religion", "start_year", "end_year"]}}]}
{"id": "simple_255", "question": [[{"role": "user", "content": "Get the biography and main contributions of Pope Innocent III."}]], "function": [{"name": "religious_history.get_papal_biography", "description": "Retrieve the biography and main religious and historical contributions of a Pope based on his papal name.", "parameters": {"type": "dict", "properties": {"papal_name": {"type": "string", "description": "The papal name of the Pope."}, "include_contributions": {"type": "boolean", "default": false, "description": "Include main contributions of the Pope in the response if true."}}, "required": ["papal_name", "include_contributions"]}}]}
{"id": "simple_256", "question": [[{"role": "user", "content": "Generate an image of a circle with a radius of 50 pixels and color 'Red'."}]], "function": [{"name": "generate_circle_image", "description": "Generates a circle image based on the given radius and color", "parameters": {"type": "dict", "properties": {"radius": {"type": "integer", "description": "The radius of the circle in pixels."}, "color": {"type": "string", "description": "The color of the circle."}, "background": {"type": "string", "description": "Optional: The color of the background, default is white."}}, "required": ["radius", "color"]}}]}
{"id": "simple_257", "question": [[{"role": "user", "content": "Can you help me identify the basic RGB value of Sea Green color?"}]], "function": [{"name": "identify_color_rgb", "description": "This function identifies the RGB values of a named color.", "parameters": {"type": "dict", "properties": {"color_name": {"type": "string", "description": "Name of the color."}, "standard": {"type": "string", "description": "The color standard (e.g. basic, pantone). Default is 'basic'"}}, "required": ["color_name"]}}]}
{"id": "simple_258", "question": [[{"role": "user", "content": "Mix yellow and blue colors and adjust the lightness level to 60 percent."}]], "function": [{"name": "mix_paint_color", "description": "Combine two primary paint colors and adjust the resulting color's lightness level.", "parameters": {"type": "dict", "properties": {"color1": {"type": "string", "description": "The first primary color to be mixed."}, "color2": {"type": "string", "description": "The second primary color to be mixed."}, "lightness": {"type": "integer", "description": "The desired lightness level of the resulting color in percentage. The default level is set to 50."}}, "required": ["color1", "color2"]}}]}
{"id": "simple_259", "question": [[{"role": "user", "content": "Calculate the total quantity of paint needed to cover a wall of 30 feet by 12 feet using a specific brand that covers 400 square feet per gallon."}]], "function": [{"name": "calculate_paint_needed", "description": "Calculate the amount of paint needed to cover a surface area based on the coverage rate of a specific paint brand.", "parameters": {"type": "dict", "properties": {"coverage_rate": {"type": "integer", "description": "The area in square feet that one gallon of paint can cover."}, "length": {"type": "integer", "description": "Length of the wall to be painted in feet."}, "height": {"type": "integer", "description": "Height of the wall to be painted in feet."}}, "required": ["coverage_rate", "length", "height"]}}]}
{"id": "simple_260", "question": [[{"role": "user", "content": "Calculate how many gallons of paint is required to paint a wall with width of 20ft and height of 12ft, assuming 1 gallon covers approximately 350 sq.ft. Don't include window area of 15 sq.ft."}]], "function": [{"name": "paint_requirement.calculate", "description": "Calculate the amount of paint required to paint a given area. Account for coverage efficiency of the paint and exclusions (like windows).", "parameters": {"type": "dict", "properties": {"area": {"type": "dict", "properties": {"width": {"type": "integer", "description": "The width of the area to be painted in feet."}, "height": {"type": "integer", "description": "The height of the area to be painted in feet."}}, "description": "The area to be painted."}, "paint_coverage": {"type": "integer", "description": "Coverage area per gallon of the paint in square feet.", "default": 350}, "exclusion": {"type": "dict", "properties": {"type": {"type": "string", "description": "The type of the exclusion e.g window, door etc."}, "area": {"type": "integer", "description": "The area of the exclusion in square feet."}}, "description": "Area not to be painted. Default to not use any exclusion if not specified."}}, "required": ["area", "paint_coverage"]}}]}
{"id": "simple_261", "question": [[{"role": "user", "content": "Draw a rectangle with a width of 20 units and height of 10 units in red."}]], "function": [{"name": "draw_rectangle", "description": "Draw a rectangle given its width and height.", "parameters": {"type": "dict", "properties": {"width": {"type": "integer", "description": "The width of the rectangle."}, "height": {"type": "integer", "description": "The height of the rectangle."}, "color": {"type": "string", "description": "The color of the rectangle. Default is 'black'."}}, "required": ["width", "height"]}}]}
{"id": "simple_262", "question": [[{"role": "user", "content": "Change my painting's medium to oil and change size to 12x18 with red dominant color."}]], "function": [{"name": "modify_painting", "description": "Modify an existing painting's attributes such as size, medium, and color.", "parameters": {"type": "dict", "properties": {"size": {"type": "string", "description": "The size of the painting in inches, width by height."}, "medium": {"type": "string", "description": "The medium of the painting, such as oil, acrylic, etc."}, "dominant_color": {"type": "string", "description": "The dominant color of the painting. Default to 'black'."}}, "required": ["size", "medium"]}}]}
{"id": "simple_263", "question": [[{"role": "user", "content": "Find me the most recent art sculpture by James Plensa with detailed description."}]], "function": [{"name": "get_sculpture_info", "description": "Retrieves the most recent artwork by a specified artist with its detailed description.", "parameters": {"type": "dict", "properties": {"artist_name": {"type": "string", "description": "The name of the artist."}, "detail": {"type": "boolean", "description": "If True, it provides detailed description of the sculpture. Defaults to False."}}, "required": ["artist_name"]}}]}
{"id": "simple_264", "question": [[{"role": "user", "content": "Find the size of the sculpture with title 'David' by Michelangelo."}]], "function": [{"name": "sculpture.get_details", "description": "Retrieve details of a sculpture based on the artist and the title of the sculpture.", "parameters": {"type": "dict", "properties": {"artist": {"type": "string", "description": "The artist who made the sculpture."}, "title": {"type": "string", "description": "The title of the sculpture."}, "detail": {"type": "string", "description": "The specific detail wanted about the sculpture. Default is 'general information'."}}, "required": ["artist", "title"]}}]}
{"id": "simple_265", "question": [[{"role": "user", "content": "Find me sculptures near Chicago that were made in the 19th century."}]], "function": [{"name": "sculpture_search", "description": "Find sculptures based on location and a specific time frame.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city where the sculptures are located."}, "time_frame": {"type": "string", "description": "The time frame during which the sculptures were made."}, "material": {"type": "string", "description": "Optional material of the sculptures. Default is 'all'"}}, "required": ["location", "time_frame"]}}]}
{"id": "simple_266", "question": [[{"role": "user", "content": "What is the value of the sculpture 'The Thinker' by Rodin?"}]], "function": [{"name": "get_sculpture_value", "description": "Retrieve the current market value of a particular sculpture by a specific artist.", "parameters": {"type": "dict", "properties": {"sculpture": {"type": "string", "description": "The name of the sculpture."}, "artist": {"type": "string", "description": "The name of the artist who created the sculpture."}}, "required": ["sculpture", "artist"]}}]}
{"id": "simple_267", "question": [[{"role": "user", "content": "Find the top rated modern sculpture exhibition happening in New York in the upcoming month."}]], "function": [{"name": "find_exhibition", "description": "Locate the most popular exhibitions based on criteria like location, time, art form, and user ratings.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state where the exhibition is held, e.g., New York City, NY."}, "art_form": {"type": "string", "description": "The form of art the exhibition is displaying e.g., sculpture."}, "month": {"type": "string", "description": "The month of exhibition. Default value will return upcoming events if not specified."}, "user_ratings": {"type": "string", "enum": ["low", "average", "high"], "description": "Select exhibitions with user rating threshold. Default is 'low'"}}, "required": ["location", "art_form"]}}]}
{"id": "simple_268", "question": [[{"role": "user", "content": "Find me the sculptures of Michelangelo with material Marble in Rome, Italy."}]], "function": [{"name": "sculpture_locator.find_by_artist", "description": "Locate the sculptures of specific artist by material and location", "parameters": {"type": "dict", "properties": {"artist": {"type": "string", "description": "Name of the Artist of the sculpture"}, "material": {"type": "string", "description": "Material of the sculpture."}, "location": {"type": "string", "description": "The location where you want to find the sculpture. Default is 'all' if not specified."}}, "required": ["artist", "material"]}}]}
{"id": "simple_269", "question": [[{"role": "user", "content": "Calculate the compound interest of an investment of $10,000 at an interest rate of 5% compounded yearly for 10 years."}]], "function": [{"name": "calculate_compound_interest", "description": "Calculates the compound interest of an investment over a given time period.", "parameters": {"type": "dict", "properties": {"principle": {"type": "integer", "description": "The initial amount of the investment."}, "interest_rate": {"type": "float", "description": "The yearly interest rate of the investment."}, "time": {"type": "integer", "description": "The time, in years, the money is invested or borrowed for."}, "compounds_per_year": {"type": "integer", "description": "The number of times the interest is compounded per year. Default is 1 (interest is compounded yearly)."}}, "required": ["principle", "interest_rate", "time"]}}]}
{"id": "simple_270", "question": [[{"role": "user", "content": "Can you give me the height and width of Empire State building in feet?"}]], "function": [{"name": "building.get_dimensions", "description": "Retrieve the dimensions of a specific building based on its name.", "parameters": {"type": "dict", "properties": {"building_name": {"type": "string", "description": "The name of the building."}, "unit": {"type": "string", "description": "The unit in which you want the dimensions. Default is meter.", "enum": ["meter", "feet"]}}, "required": ["building_name", "unit"]}}]}
{"id": "simple_271", "question": [[{"role": "user", "content": "What is the structural dynamic analysis of the building with building Id B1004 for 2nd, 3rd and 4th floors?"}]], "function": [{"name": "analyze_structure", "description": "Analyze a structure of a building based on its Id and floor numbers.", "parameters": {"type": "dict", "properties": {"building_id": {"type": "string", "description": "The unique identification number of the building."}, "floors": {"type": "array", "items": {"type": "integer"}, "description": "Floor numbers to be analyzed."}, "mode": {"type": "string", "description": "Mode of analysis, e.g. 'static' or 'dynamic'. Default is 'static'."}}, "required": ["building_id", "floors"]}}]}
{"id": "simple_272", "question": [[{"role": "user", "content": "Calculate the area and circumference of a circle with a radius of 5 units."}]], "function": [{"name": "calculate_circle_dimensions", "description": "Calculate the area and circumference of a circle based on the radius.", "parameters": {"type": "dict", "properties": {"radius": {"type": "integer", "description": "The radius of the circle."}}, "required": ["radius"]}}]}
{"id": "simple_273", "question": [[{"role": "user", "content": "Find out the open hours for the Louvre Museum in Paris."}]], "function": [{"name": "museum.get_hours", "description": "Retrieve the open hours for a museum based on its name and location.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The name of the museum."}, "location": {"type": "string", "description": "The city where the museum is located."}, "day": {"type": "string", "description": "Optional: Day of the week for specific open hours. Default 'Monday'."}}, "required": ["name", "location"]}}]}
{"id": "simple_274", "question": [[{"role": "user", "content": "Find information about the opening hours of the Metropolitan Museum of Art."}]], "function": [{"name": "museum_info", "description": "Retrieve information about the opening hours of a museum based on its name.", "parameters": {"type": "dict", "properties": {"museum_name": {"type": "string", "description": "The name of the museum."}, "info_type": {"type": "string", "description": "The type of information needed about the museum.", "default": "opening_hours"}}, "required": ["museum_name"]}}]}
{"id": "simple_275", "question": [[{"role": "user", "content": "Get the list of top 5 popular artworks at the Metropolitan Museum of Art. Please sort by popularity."}]], "function": [{"name": "metropolitan_museum.get_top_artworks", "description": "Fetches the list of popular artworks at the Metropolitan Museum of Art. Results can be sorted based on popularity.", "parameters": {"type": "dict", "properties": {"number": {"type": "integer", "description": "The number of artworks to fetch"}, "sort_by": {"type": "string", "description": "The criteria to sort the results on. Default is 'popularity'.", "enum": ["popularity", "chronological", "alphabetical"]}}, "required": ["number"]}}]}
{"id": "simple_276", "question": [[{"role": "user", "content": "Get the working hours of Louvre Museum in Paris."}]], "function": [{"name": "museum_working_hours.get", "description": "Get the working hours of a museum in a specific location.", "parameters": {"type": "dict", "properties": {"museum": {"type": "string", "description": "The name of the museum."}, "location": {"type": "string", "description": "The location of the museum."}, "day": {"type": "string", "description": "Specific day of the week. Default is 'Monday'"}}, "required": ["museum", "location"]}}]}
{"id": "simple_277", "question": [[{"role": "user", "content": "Find the working hours and ticket price of The British Museum for this weekend, Jun.20,2023."}]], "function": [{"name": "museum_info", "description": "Get information about a museum including its opening hours and ticket prices for a specific date range.", "parameters": {"type": "dict", "properties": {"museum": {"type": "string", "description": "The name of the museum."}, "date": {"type": "string", "description": "The specific date for which information is needed, in the format of YYYY-MM-DD such as '2022-12-01'."}, "information": {"type": "array", "items": {"type": "string", "enum": ["opening_hours", "ticket_price", "address"]}, "description": "The type of information needed from the museum. This is optional and defaults to 'all' if not specified.", "default": "all"}}, "required": ["museum", "date"]}}]}
{"id": "simple_278", "question": [[{"role": "user", "content": "Find me the average price and ratings of piano from Yamaha."}]], "function": [{"name": "get_instrument_details", "description": "Retrieve the average price and ratings of an instrument from a particular manufacturer.", "parameters": {"type": "dict", "properties": {"instrument": {"type": "string", "description": "The name of the instrument."}, "manufacturer": {"type": "string", "description": "The manufacturer of the instrument."}, "features": {"type": "array", "items": {"type": "string", "enum": ["price", "rating"]}, "description": "The features to retrieve about the instrument. Default is 'price'"}}, "required": ["instrument", "manufacturer"]}}]}
{"id": "simple_279", "question": [[{"role": "user", "content": "What's the retail price of a Fender American Professional II Stratocaster in Rosewood Finish?"}]], "function": [{"name": "instrument_price.get", "description": "Retrieve the current retail price of a specific musical instrument.", "parameters": {"type": "dict", "properties": {"brand": {"type": "string", "description": "The brand of the instrument."}, "model": {"type": "string", "description": "The specific model of the instrument."}, "finish": {"type": "string", "description": "The color or type of finish on the instrument."}}, "required": ["brand", "model", "finish"]}}]}
{"id": "simple_280", "question": [[{"role": "user", "content": "Find an acoustic instrument within my budget of $1000."}]], "function": [{"name": "find_instrument", "description": "Search for a musical instrument within specified budget and of specific type.", "parameters": {"type": "dict", "properties": {"budget": {"type": "integer", "description": "Your budget for the instrument."}, "type": {"type": "string", "description": "Type of the instrument"}, "make": {"type": "string", "description": "Maker of the instrument. Default to not use if not specified."}}, "required": ["budget", "type"]}}]}
{"id": "simple_281", "question": [[{"role": "user", "content": "Find the details about the musical instrument 'Violin' from 'Stradivarius' maker, made in the year 1721."}]], "function": [{"name": "get_instrument_info", "description": "Retrieve the details about a specific musical instrument based on its name, maker, and manufacturing year.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The name of the instrument."}, "maker": {"type": "string", "description": "The name of the maker who created the instrument."}, "year": {"type": "integer", "description": "The year the instrument was made."}}, "required": ["name", "maker", "year"]}}]}
{"id": "simple_282", "question": [[{"role": "user", "content": "Find a Yamaha flute with the specifications of open hole, C foot, and silver headjoint available for sale."}]], "function": [{"name": "find_flute", "description": "Locate a flute for sale based on specific requirements.", "parameters": {"type": "dict", "properties": {"brand": {"type": "string", "description": "The brand of the flute. Example, 'Yamaha'"}, "specs": {"type": "array", "items": {"type": "string", "enum": ["open hole", "C foot", "silver headjoint"]}, "description": "The specifications of the flute desired."}}, "required": ["brand", "specs"]}}]}
{"id": "simple_283", "question": [[{"role": "user", "content": "Find the price of a used Gibson Les Paul guitar in excellent condition in the Chicago area."}]], "function": [{"name": "guitar_price.find", "description": "Retrieve the price of a specific used guitar model based on its condition and location.", "parameters": {"type": "dict", "properties": {"model": {"type": "string", "description": "The model of the guitar."}, "condition": {"type": "string", "enum": ["Poor", "Good", "Excellent"], "description": "The condition of the guitar."}, "location": {"type": "string", "description": "The location where the guitar is being sold."}}, "required": ["model", "condition", "location"]}}]}
{"id": "simple_284", "question": [[{"role": "user", "content": "Get information about the pop concerts in New York for next month."}]], "function": [{"name": "concert_info.get", "description": "Retrieve information about concerts based on specific genre, location and date.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city where the concert will take place."}, "date": {"type": "string", "description": "Time frame to get the concert for."}, "genre": {"type": "string", "description": "Genre of the concert.", "enum": ["Pop", "Rock", "Country", "Classical", "Electronic", "Hip-Hop"]}}, "required": ["location", "date", "genre"]}}]}
{"id": "simple_285", "question": [[{"role": "user", "content": "Find me a Rock concert in Chicago with ticket availability under $100."}]], "function": [{"name": "find_concert", "description": "Locate a concert in a specified location within a certain budget.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city where you are looking for a concert. In the format City, State."}, "price": {"type": "integer", "description": "Maximum ticket price."}, "genre": {"type": "string", "description": "Music genre of the concert. Default to 'Jazz'. ", "enum": ["Rock", "Pop", "Country", "Jazz", "Classical"]}}, "required": ["location", "price"]}}]}
{"id": "simple_286", "question": [[{"role": "user", "content": "Get concert details for the artist Beyonce performing in San Diego next month (April 2022)."}]], "function": [{"name": "concert.get_details", "description": "Fetch the details for a particular concert based on the artist and location.", "parameters": {"type": "dict", "properties": {"artist": {"type": "string", "description": "Name of the artist/band who's performing."}, "location": {"type": "string", "description": "City where the concert is taking place."}, "date": {"type": "string", "description": "Date of the concert in 'mm-yyyy' format. Default is the current month if not specified."}}, "required": ["artist", "location"]}}]}
{"id": "simple_287", "question": [[{"role": "user", "content": "Find me a classical concert this weekend in Los Angeles with cheap tickets."}]], "function": [{"name": "concert.search", "description": "Locate a concert based on specific criteria like genre, location, and date.", "parameters": {"type": "dict", "properties": {"genre": {"type": "string", "description": "Genre of the concert."}, "location": {"type": "string", "description": "City of the concert."}, "date": {"type": "string", "description": "Date of the concert, e.g. this weekend, today, tomorrow.", "enum": ["this weekend", "next weekend", "this month", "next month", "today", "tomorrow", "the day after"]}, "price_range": {"type": "string", "enum": ["free", "cheap", "moderate", "expensive"], "description": "Expected price range of the concert tickets. Default is 'free'."}}, "required": ["genre", "location", "date"]}}]}
{"id": "simple_288", "question": [[{"role": "user", "content": "Get me two tickets for next Eminem concert in New York City."}]], "function": [{"name": "concert_booking.book_ticket", "description": "Book concert tickets for a specific artist in a specified city.", "parameters": {"type": "dict", "properties": {"artist": {"type": "string", "description": "The artist you want to book tickets for."}, "city": {"type": "string", "description": "The city where the concert is."}, "num_tickets": {"type": "integer", "description": "Number of tickets required. Default is 1."}}, "required": ["artist", "city"]}}]}
{"id": "simple_289", "question": [[{"role": "user", "content": "Find concerts near me in Seattle that plays jazz music."}]], "function": [{"name": "concert.find_nearby", "description": "Locate nearby concerts based on specific criteria like genre.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Seattle, WA"}, "genre": {"type": "string", "description": "Genre of music to be played at the concert."}}, "required": ["location", "genre"]}}]}
{"id": "simple_290", "question": [[{"role": "user", "content": "What's the timing and location for The Weeknd's concert happening in December?"}]], "function": [{"name": "concert.find_details", "description": "Finds details of a concert event.", "parameters": {"type": "dict", "properties": {"artist": {"type": "string", "description": "Name of the artist performing."}, "month": {"type": "string", "description": "Month in which the concert is happening."}, "year": {"type": "integer", "description": "Year of the concert.", "default": 2022}}, "required": ["artist", "month"]}}]}
{"id": "simple_291", "question": [[{"role": "user", "content": "Generate a melody in C major scale, starting with the note C4, 16 measures long, at 120 beats per minute."}]], "function": [{"name": "music_generator.generate_melody", "description": "Generate a melody based on certain musical parameters.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The key of the melody. E.g., 'C' for C major."}, "start_note": {"type": "string", "description": "The first note of the melody, specified in scientific pitch notation. E.g., 'C4'."}, "length": {"type": "integer", "description": "The number of measures in the melody."}, "tempo": {"type": "integer", "description": "The tempo of the melody, in beats per minute. Optional parameter. If not specified, defaults to 120."}}, "required": ["key", "start_note", "length"]}}]}
{"id": "simple_292", "question": [[{"role": "user", "content": "Compose a simple piano melody with a progression of C, F and G for 4 measures."}]], "function": [{"name": "compose_melody", "description": "Compose a melody using the specified chord progression for a certain number of measures on specified instrument.", "parameters": {"type": "dict", "properties": {"progression": {"type": "array", "items": {"type": "string"}, "description": "The progression of chords."}, "measures": {"type": "integer", "description": "The number of measures of the melody."}, "instrument": {"type": "string", "description": "The instrument for the composition. Default is 'Piano'."}}, "required": ["progression", "measures"]}}]}
{"id": "simple_293", "question": [[{"role": "user", "content": "Create a mix track using notes of C major scale and duration of each note being quarter of a second with a duration of 3 minutes."}]], "function": [{"name": "music_composer.create_mix", "description": "Create a mix of a song based on a particular music scale and duration", "parameters": {"type": "dict", "properties": {"scale": {"type": "string", "description": "The musical scale to be used. E.g: C Major, A Minor, etc."}, "note_duration": {"type": "string", "description": "Duration of each note. Options: 'whole', 'half', 'quarter', 'eighth', 'sixteenth'.", "enum": ["whole", "half", "quarter", "eighth", "sixteenth"]}, "track_length": {"type": "integer", "description": "Length of the mix track in seconds."}}, "required": ["scale", "note_duration", "track_length"]}}]}
{"id": "simple_294", "question": [[{"role": "user", "content": "Generate a major chord progression in C key with four chords."}]], "function": [{"name": "music_generation.create_chord_progression", "description": "Create a chord progression in a specific key and number of chords.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The key for the chord progression."}, "chords": {"type": "integer", "description": "Number of chords in the progression."}, "progression_type": {"type": "string", "description": "The type of the chord progression. Optional parameter. Default is 'major'."}}, "required": ["key", "chords"]}}]}
{"id": "simple_295", "question": [[{"role": "user", "content": "Find the lyrics to the song 'Bohemian Rhapsody' by Queen."}]], "function": [{"name": "get_song_lyrics", "description": "Retrieve the lyrics of a song based on the artist's name and song title.", "parameters": {"type": "dict", "properties": {"song_title": {"type": "string", "description": "The title of the song."}, "artist_name": {"type": "string", "description": "The name of the artist who performed the song."}, "lang": {"type": "string", "description": "The language of the lyrics. Default is English.", "enum": ["English", "French", "Spanish", "German", "Italian"]}}, "required": ["song_title", "artist_name"]}}]}
{"id": "simple_296", "question": [[{"role": "user", "content": "Generate a major C scale progression with tempo 80 BPM and duration 4 beats."}]], "function": [{"name": "music_generator.generate_scale_progression", "description": "Generate a music scale progression in a specific key with a given tempo and duration.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The key in which to generate the scale progression."}, "tempo": {"type": "integer", "description": "The tempo of the scale progression in BPM."}, "duration": {"type": "integer", "description": "The duration of each note in beats."}, "scale_type": {"type": "string", "default": "major", "description": "The type of scale to generate. Defaults to 'major'."}}, "required": ["key", "tempo", "duration"]}}]}
{"id": "simple_297", "question": [[{"role": "user", "content": "music.theory.chordProgression(progression=['I', 'V', 'vi', 'IV'])"}]], "function": [{"name": "music.theory.chordProgression", "description": "Identifies a potential key signature for the given chord progression.", "parameters": {"type": "dict", "properties": {"progression": {"type": "array", "items": {"type": "string"}, "description": "The chord progression in Roman numerals. Eg: ['I', 'V', 'vi', 'IV']."}, "returnAllPossibleKeys": {"type": "boolean", "description": "Flag indicating if the function should return all possible key signatures that fit the chord progression. If false, the function will return the first valid key it finds. Default is false."}, "assumeMajor": {"type": "boolean", "description": "Assumption if the key signature is Major. If true, the function will assume the key signature to be major and otherwise minor. Default is true."}}, "required": ["progression"]}}]}
{"id": "simple_298", "question": [[{"role": "user", "content": "What key signature does C# major have?"}]], "function": [{"name": "music_theory.key_signature", "description": "Return the key signature of a major or minor scale.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The root of the scale, e.g., 'C', 'F#', 'Ab'."}, "scale_type": {"type": "string", "enum": ["major", "minor"], "description": "Type of the scale, either 'major' or 'minor'. Default is 'major'."}}, "required": ["key"]}}]}
{"id": "simple_299", "question": [[{"role": "user", "content": "What is the musical scale associated with C sharp major?"}]], "function": [{"name": "musical_scale", "description": "Get the musical scale of a specific key in music theory.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The musical key for which the scale will be found."}, "scale_type": {"type": "string", "default": "major", "description": "The type of musical scale."}}, "required": ["key"]}}]}
{"id": "simple_300", "question": [[{"role": "user", "content": "Calculate the duration between two notes of 440Hz and 880Hz frequency based on harmonic rhythm."}]], "function": [{"name": "music.calculate_note_duration", "description": "Calculate the duration between two notes based on their frequencies and harmonic rhythm.", "parameters": {"type": "dict", "properties": {"first_note_frequency": {"type": "integer", "description": "The frequency of the first note in Hz."}, "second_note_frequency": {"type": "integer", "description": "The frequency of the second note in Hz."}, "tempo": {"type": "integer", "description": "The tempo of the music in beats per minute. Defaults to 120 beats per minute."}}, "required": ["first_note_frequency", "second_note_frequency"]}}]}
{"id": "simple_301", "question": [[{"role": "user", "content": "What is the third major chord in C major scale?"}]], "function": [{"name": "get_third_chord", "description": "Calculate the third major chord in a given key.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The key of the scale."}, "type": {"type": "string", "description": "Type of the scale, either major or minor. Default is 'major'."}}, "required": ["key"]}}]}
{"id": "simple_302", "question": [[{"role": "user", "content": "Calculate the batting average for a baseball player who has 180 hits and 600 at-bats. Round to 3 decimals."}]], "function": [{"name": "calculate_batting_average", "description": "Calculate the batting average for a baseball player based on their number of hits and at-bats.", "parameters": {"type": "dict", "properties": {"hits": {"type": "integer", "description": "The number of hits."}, "at_bats": {"type": "integer", "description": "The number of at-bats."}, "decimal_places": {"type": "integer", "description": "The number of decimal places to return in the batting average. Default is 3."}}, "required": ["hits", "at_bats"]}}]}
{"id": "simple_303", "question": [[{"role": "user", "content": "Get the player stats of Cristiano Ronaldo in the 2019-2020 season"}]], "function": [{"name": "soccer_stat.get_player_stats", "description": "Retrieve soccer player statistics for a given season.", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "Name of the player."}, "season": {"type": "string", "description": "Soccer season, usually specified by two years."}, "league": {"type": "string", "description": "Optional - the soccer league, defaults to all leagues if not specified."}}, "required": ["player_name", "season"]}}]}
{"id": "simple_304", "question": [[{"role": "user", "content": "Get point and rebound stats for player 'LeBron James' from last basketball game"}]], "function": [{"name": "player_stats.getLastGame", "description": "Get last game statistics for a specific player in basketball", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The name of the basketball player."}, "team": {"type": "string", "description": "The team that player currently plays for."}, "metrics": {"type": "array", "items": {"type": "string", "enum": ["Points", "Rebounds", "Assists", "Blocks"]}, "description": "Specific metrics to retrieve. If no value is specified, all available metrics will be returned by default."}}, "required": ["player_name", "team"]}}]}
{"id": "simple_305", "question": [[{"role": "user", "content": "Calculate the overall goal and assist of soccer player Messi in La Liga 2020-2021 season"}]], "function": [{"name": "sports_stats.get_performance", "description": "Compute the performance score of a soccer player given his game stats for a specific tournament in a season.", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "Name of the player."}, "tournament": {"type": "string", "description": "Name of the soccer tournament."}, "season": {"type": "string", "description": "Specific season in format 'YYYY-YYYY'."}, "performance_indicator": {"type": "array", "items": {"type": "string", "enum": ["Goals Scored", "Assists Made", "Saves Made", "Cards Received"]}, "description": "Array of performance indicators. Use as much as possible. Default to use all if not specified."}}, "required": ["player_name", "tournament", "season"]}}]}
{"id": "simple_306", "question": [[{"role": "user", "content": "Find average batting score of a cricketer, Virat Kohli for past 10 matches"}]], "function": [{"name": "average_batting_score", "description": "Get the average batting score of a cricketer for specified past matches.", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "Name of the cricket player."}, "matches": {"type": "integer", "description": "Number of past matches to consider for average calculation."}, "match_format": {"type": "string", "description": "Format of the cricket matches considered (e.g., 'T20', 'ODI', 'Test'). Default is 'T20'."}}, "required": ["player_name", "matches"]}}]}
{"id": "simple_307", "question": [[{"role": "user", "content": "Who won the basketball game between Lakers and Clippers on Jan 28, 2021?"}]], "function": [{"name": "game_result.get_winner", "description": "Get the winner of a specific basketball game.", "parameters": {"type": "dict", "properties": {"teams": {"type": "array", "items": {"type": "string"}, "description": "List of two teams who played the game."}, "date": {"type": "string", "description": "The date of the game, formatted as YYYY-MM-DD."}, "venue": {"type": "string", "optional": true, "description": "Optional: The venue of the game. Default is 'home'."}}, "required": ["teams", "date"]}}]}
{"id": "simple_308", "question": [[{"role": "user", "content": "What are the next five matches for Manchester United and who are they playing against in the English Premier League?"}]], "function": [{"name": "sports.match_schedule", "description": "Retrieve the match schedule for a specific sports team.", "parameters": {"type": "dict", "properties": {"team_name": {"type": "string", "description": "The name of the sports team."}, "num_matches": {"type": "integer", "description": "The number of upcoming matches you want to get."}, "league": {"type": "string", "description": "The sports league of the team. This is an optional parameter. Default is 'English Premier League'."}}, "required": ["team_name", "num_matches"]}}]}
{"id": "simple_309", "question": [[{"role": "user", "content": "Find me the record of Tom Brady in the 2020 NFL season."}]], "function": [{"name": "nfl_data.player_record", "description": "Retrieve the record of an NFL player in a specified season.", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The name of the NFL player."}, "season_year": {"type": "integer", "description": "The year of the NFL season."}, "team": {"type": "string", "description": "The NFL team that the player played for in that season. Default is all teams if not specified."}}, "required": ["player_name", "season_year"]}}]}
{"id": "simple_310", "question": [[{"role": "user", "content": "What are the career stats of basketball player LeBron James?"}]], "function": [{"name": "get_career_stats", "description": "Retrieve the career statistics of a basketball player based on the player's name.", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The name of the basketball player."}, "team": {"type": "string", "description": "The team that the player currently plays for or has played for (Optional). Default to use all teams if not specified."}}, "required": ["player_name"]}}]}
{"id": "simple_311", "question": [[{"role": "user", "content": "Find me the detailed profile of basketball player Lebron James"}]], "function": [{"name": "sports_db.find_athlete", "description": "Find the profile information of a sports athlete based on their full name.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The full name of the athlete."}, "team": {"type": "string", "description": "The team the athlete belongs to. Default to all teams if not specified."}, "sport": {"type": "string", "description": "The sport that athlete plays.", "enum": ["Basketball", "Baseball", "Football", "Soccer"]}}, "required": ["name", "sport"]}}]}
{"id": "simple_312", "question": [[{"role": "user", "content": "What are the statistics of Ronaldo's matches in 2021?"}]], "function": [{"name": "player_statistic", "description": "Retrieves detailed player's statistics for a specific year.", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The player's name."}, "year": {"type": "integer", "description": "Year for which the statistics will be displayed."}, "team_name": {"type": "string", "description": "The name of the team(optional). Default to not use it if not specified."}}, "required": ["player_name", "year"]}}]}
{"id": "simple_313", "question": [[{"role": "user", "content": "What's the total worth in euro of Messi according to latest data?"}]], "function": [{"name": "celebrity_net_worth.get", "description": "Get the total net worth of a sports celebrity based on most recent data.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The full name of the sports celebrity."}, "currency": {"type": "string", "description": "The currency in which the net worth will be returned. Default is 'USD'."}}, "required": ["name", "currency"]}}]}
{"id": "simple_314", "question": [[{"role": "user", "content": "Find all the major achievements of the footballer Lionel Messi."}]], "function": [{"name": "sports_celebrity.get_major_achievements", "description": "Returns a list of major achievements of a particular sports celebrity.", "parameters": {"type": "dict", "properties": {"celebrity_name": {"type": "string", "description": "Name of the sports celebrity."}, "sports": {"type": "string", "description": "Type of sports the celebrity involved in. Default is Football."}, "team": {"type": "string", "description": "Optional. Team where celebrity currently plays. Default is 'all'"}}, "required": ["celebrity_name"]}}]}
{"id": "simple_315", "question": [[{"role": "user", "content": "Get the NBA team's ranking with the best defence in the 2021 season."}]], "function": [{"name": "get_defense_ranking", "description": "Retrieve the defence ranking of NBA teams in a specified season.", "parameters": {"type": "dict", "properties": {"season": {"type": "integer", "description": "The NBA season to get defence ranking from."}, "top": {"type": "integer", "default": 1, "description": "Number of top teams in defence ranking to fetch."}}, "required": ["season"]}}]}
{"id": "simple_316", "question": [[{"role": "user", "content": "Find the current world rank of a Tennis player, Serena Williams."}]], "function": [{"name": "get_sport_ranking", "description": "Retrieve the current world ranking of a sportsperson based on the sport and player's name.", "parameters": {"type": "dict", "properties": {"sport": {"type": "string", "description": "Name of the sport."}, "player_name": {"type": "string", "description": "Name of the player."}, "gender": {"type": "string", "description": "Gender of the player. This is optional. The possible values are male or female.", "default": "all"}}, "required": ["sport", "player_name"]}}]}
{"id": "simple_317", "question": [[{"role": "user", "content": "Find the ranking of LA Lakers in the NBA 2021 regular season."}]], "function": [{"name": "get_team_rank", "description": "Get the team ranking in a sports league based on season and type.", "parameters": {"type": "dict", "properties": {"team_name": {"type": "string", "description": "The name of the sports team."}, "league": {"type": "string", "description": "The name of the league in which the team competes."}, "season": {"type": "string", "description": "The season for which the team's ranking is sought."}, "type": {"type": "string", "description": "Type of the season: regular or playoff.", "enum": ["regular", "playoff"]}}, "required": ["team_name", "league", "season", "type"]}}]}
{"id": "simple_318", "question": [[{"role": "user", "content": "What is the FIFA ranking of Germany's men soccer team for the year 2021?"}]], "function": [{"name": "get_team_ranking", "description": "Retrieve the FIFA ranking of a specific soccer team for a certain year.", "parameters": {"type": "dict", "properties": {"team_name": {"type": "string", "description": "The name of the soccer team."}, "year": {"type": "integer", "description": "The year for which the ranking is to be retrieved."}, "gender": {"type": "string", "description": "The gender of the team. It can be either 'men' or 'women'. Default is 'men'."}}, "required": ["team_name", "year"]}}]}
{"id": "simple_319", "question": [[{"role": "user", "content": "What is the ranking of Manchester United in Premier League?"}]], "function": [{"name": "sports_ranking", "description": "Fetch the ranking of a specific sports team in a specific league", "parameters": {"type": "dict", "properties": {"team": {"type": "string", "description": "The name of the team."}, "league": {"type": "string", "description": "The name of the league."}, "season": {"type": "integer", "description": "Optional parameter to specify the season, default is the current season '2023' if not specified."}}, "required": ["team", "league"]}}]}
{"id": "simple_320", "question": [[{"role": "user", "content": "Fetch the basketball league standings, where Golden State Warriors stand in current 2022-2023 season with details"}]], "function": [{"name": "sports_ranking.get_team_position", "description": "Retrieve a team's position and stats in the basketball league for a given season.", "parameters": {"type": "dict", "properties": {"team": {"type": "string", "description": "The name of the team."}, "season": {"type": "string", "description": "The season for which data should be fetched."}, "detailed": {"type": "boolean", "description": "Flag to retrieve detailed stats or just the position.", "default": false}}, "required": ["team", "season"]}}]}
{"id": "simple_321", "question": [[{"role": "user", "content": "What's the ranking of Barcelona in the 2021 La Liga season?"}]], "function": [{"name": "sports_ranking", "description": "Get the ranking of a team in a given sports league and season.", "parameters": {"type": "dict", "properties": {"team": {"type": "string", "description": "The name of the team."}, "league": {"type": "string", "description": "The name of the sports league."}, "season": {"type": "string", "description": "The season for which ranking needs to be obtained."}}, "required": ["team", "league", "season"]}}]}
{"id": "simple_322", "question": [[{"role": "user", "content": "Get the current ranking for Liverpool Football Club in the Premier League."}]], "function": [{"name": "sports_ranking.get_current", "description": "Retrieve the current ranking of a specific team in a particular league.", "parameters": {"type": "dict", "properties": {"team": {"type": "string", "description": "The name of the team whose ranking is sought."}, "league": {"type": "string", "description": "The league in which the team participates."}, "season": {"type": "string", "description": "The season for which the ranking is sought. Defaults to the current season '2023-2024' if not provided."}}, "required": ["team", "league"]}}]}
{"id": "simple_323", "question": [[{"role": "user", "content": "Who is ranked as the top player in woman tennis?"}]], "function": [{"name": "sports_ranking.get_top_player", "description": "Get the top player in a specific sport.", "parameters": {"type": "dict", "properties": {"sport": {"type": "string", "description": "The type of sport."}, "gender": {"type": "string", "description": "The gender of the sport category. Optional.", "default": "men"}}, "required": ["sport"]}}]}
{"id": "simple_324", "question": [[{"role": "user", "content": "Find the score of last game for Los Angeles Lakers including its opponent name."}]], "function": [{"name": "team_score.get_latest", "description": "Retrieve the score of the most recent game for a specified sports team.", "parameters": {"type": "dict", "properties": {"team": {"type": "string", "description": "Name of the sports team."}, "include_opponent": {"type": "boolean", "description": "Include the name of the opponent team in the return.", "default": false}}, "required": ["team"]}}]}
{"id": "simple_325", "question": [[{"role": "user", "content": "Who won the last match between Chicago Bulls and Los Angeles Lakers?"}]], "function": [{"name": "sports.match_results", "description": "Returns the results of a given match between two teams.", "parameters": {"type": "dict", "properties": {"team1": {"type": "string", "description": "The name of the first team."}, "team2": {"type": "string", "description": "The name of the second team."}, "season": {"type": "string", "description": "The season when the match happened. Default is the current season."}}, "required": ["team1", "team2"]}}]}
{"id": "simple_326", "question": [[{"role": "user", "content": "Get the latest game score and statistics for Los Angeles Lakers in NBA."}]], "function": [{"name": "get_team_score", "description": "Retrieves the latest game score, individual player stats, and team stats for a specified sports team.", "parameters": {"type": "dict", "properties": {"team_name": {"type": "string", "description": "The name of the sports team."}, "league": {"type": "string", "description": "The league that the team is part of."}, "include_player_stats": {"type": "boolean", "default": false, "description": "Indicates if individual player statistics should be included in the result. Default is false."}}, "required": ["team_name", "league"]}}]}
{"id": "simple_327", "question": [[{"role": "user", "content": "Give me the schedule of Manchester United for the next 6 games in Premier League."}]], "function": [{"name": "sports_team.get_schedule", "description": "Fetches the schedule of the specified sports team for the specified number of games in the given league.", "parameters": {"type": "dict", "properties": {"team_name": {"type": "string", "description": "The name of the sports team."}, "num_of_games": {"type": "integer", "description": "Number of games for which to fetch the schedule."}, "league": {"type": "string", "description": "The name of the sports league. If not provided, the function will fetch the schedule for all games, regardless of the league."}, "location": {"type": "string", "description": "Optional. The city or venue where games are to be held. If not provided, default that all venues will be considered."}}, "required": ["team_name", "num_of_games", "league"]}}]}
{"id": "simple_328", "question": [[{"role": "user", "content": "Find the rating and player count of the board game 'Ticket to Ride'."}]], "function": [{"name": "boardgame.get_info", "description": "Retrieve detailed information of a board game.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "Name of the board game."}, "parameters": {"type": "array", "items": {"type": "string", "enum": ["player count", "playing time", "age", "mechanics", "rating"]}, "description": "Game characteristics interested."}, "language": {"type": "string", "description": "The preferred language for the game information, default is English"}}, "required": ["name", "parameters"]}}]}
{"id": "simple_329", "question": [[{"role": "user", "content": "Calculate the odds of rolling a 7 with two dice in the board game Monopoly."}]], "function": [{"name": "monopoly_odds_calculator", "description": "Calculates the probability of rolling a certain sum with two dice, commonly used in board game like Monopoly.", "parameters": {"type": "dict", "properties": {"number": {"type": "integer", "description": "The number for which the odds are calculated."}, "dice_number": {"type": "integer", "description": "The number of dice involved in the roll."}, "dice_faces": {"type": "integer", "description": "The number of faces on a single die. Default is 6 for standard six-faced die."}}, "required": ["number", "dice_number"]}}]}
{"id": "simple_330", "question": [[{"role": "user", "content": "What's the average review rating and the age range for the board game 'Catan'?"}]], "function": [{"name": "board_game_info", "description": "Get the information about a board game from a database. ", "parameters": {"type": "dict", "properties": {"game_name": {"type": "string", "description": "Name of the board game."}, "info_required": {"type": "array", "items": {"type": "string", "enum": ["average_review_rating", "age_range", "number_of_players", "playing_time", "genre"]}, "description": "Array of information requested for the game."}}, "required": ["game_name", "info_required"]}}]}
{"id": "simple_331", "question": [[{"role": "user", "content": "Find the top chess players in New York with a rating above 2300."}]], "function": [{"name": "board_game.chess.get_top_players", "description": "Find top chess players in a location based on rating.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city you want to find the players from."}, "minimum_rating": {"type": "integer", "description": "Minimum rating to filter the players."}, "number_of_players": {"type": "integer", "default": 10, "description": "Number of players you want to retrieve, default value is 10"}}, "required": ["location", "minimum_rating"]}}]}
{"id": "simple_332", "question": [[{"role": "user", "content": "What's the chess classical rating of Magnus Carlsen?"}]], "function": [{"name": "chess.rating", "description": "Fetches the current chess rating of a given player", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The full name of the chess player."}, "variant": {"type": "string", "description": "The variant of chess for which rating is requested (e.g., 'classical', 'blitz', 'bullet'). Default is 'classical'."}}, "required": ["player_name"]}}]}
{"id": "simple_333", "question": [[{"role": "user", "content": "Find the high and low temperatures, humidity, and precipitation for London, United Kingdom for the next 3 days."}]], "function": [{"name": "detailed_weather_forecast", "description": "Retrieve a detailed weather forecast for a specific location and time frame, including high/low temperatures, humidity, and precipitation.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the weather for."}, "days": {"type": "integer", "description": "Number of days for the forecast."}, "details": {"type": "array", "items": {"type": "string", "enum": ["high_low_temperature", "humidity", "precipitation"]}, "description": "Specific weather details required in the forecast."}}, "required": ["location", "days", "details"]}}]}
{"id": "simple_334", "question": [[{"role": "user", "content": "Check who is the winner in a game of blackjack given player having A and 10, dealer having 10 and 9. The Ace is considered 1."}]], "function": [{"name": "blackjack.check_winner", "description": "Checks and determines the winner in a game of blackjack.", "parameters": {"type": "dict", "properties": {"player_cards": {"type": "array", "items": {"type": "string"}, "description": "Cards held by the player."}, "dealer_cards": {"type": "array", "items": {"type": "string"}, "description": "Cards held by the dealer."}, "ace_value": {"type": "integer", "description": "The value considered for the ace card, can be either 1 or 11.", "default": 11}}, "required": ["player_cards", "dealer_cards"]}}]}
{"id": "simple_335", "question": [[{"role": "user", "content": "Find a Card of rank 'Queen' and suit 'Hearts' in the deck."}]], "function": [{"name": "find_card_in_deck", "description": "Locate a particular card in a deck based on rank and suit.", "parameters": {"type": "dict", "properties": {"rank": {"type": "string", "description": "Rank of the card (e.g. Ace, Two, King)."}, "suit": {"type": "string", "description": "Suit of the card (e.g. Hearts, Spades, Diamonds, Clubs)."}, "deck": {"type": "array", "items": {"type": "dict", "properties": {"rank": {"type": "string"}, "suit": {"type": "string"}}}, "description": "Deck of cards. If not provided, the deck will be a standard 52 card deck"}}, "required": ["rank", "suit"]}}]}
{"id": "simple_336", "question": [[{"role": "user", "content": "Shuffle a deck of cards, and draw 3 cards from the top."}]], "function": [{"name": "cards.shuffle_and_draw", "description": "Shuffle a standard deck of 52 cards and draw a specified number of cards from the top.", "parameters": {"type": "dict", "properties": {"num_cards": {"type": "integer", "description": "Number of cards to be drawn. The default is 1 if no value is provided."}}, "required": ["num_cards"]}}]}
{"id": "simple_337", "question": [[{"role": "user", "content": "In a texas holdem game, Who won in the poker game with players Alex, Sam, Robert and Steve given the cards Alex':['A of spades', 'K of spades'], 'Sam': ['2 of diamonds', '3 of clubs'], 'Robert': ['Q of hearts', '10 of hearts'], 'Steve': ['4 of spades', '5 of spades']?"}]], "function": [{"name": "poker_game_winner", "description": "Identify the winner in a poker game based on the cards.", "parameters": {"type": "dict", "properties": {"players": {"type": "array", "items": {"type": "string"}, "description": "Names of the players in a list."}, "cards": {"type": "dict", "description": "An object containing the player name as key and the cards as values in a list."}, "type": {"type": "string", "description": "Type of poker game. Defaults to 'Texas Holdem'"}}, "required": ["players", "cards"]}}]}
{"id": "simple_338", "question": [[{"role": "user", "content": "What is the probability of drawing a heart card from a deck of 52 cards?"}]], "function": [{"name": "card_game_probability.calculate", "description": "Calculate the probability of drawing a certain card or suit from a deck of cards.", "parameters": {"type": "dict", "properties": {"total_cards": {"type": "integer", "description": "Total number of cards in the deck."}, "desired_cards": {"type": "integer", "description": "Number of cards in the deck that satisfy the conditions."}, "cards_drawn": {"type": "integer", "default": 1, "description": "Number of cards drawn from the deck."}}, "required": ["total_cards", "desired_cards"]}}]}
{"id": "simple_339", "question": [[{"role": "user", "content": "What is the probability of getting a full house in poker?"}]], "function": [{"name": "poker_probability.full_house", "description": "Calculate the probability of getting a full house in a poker game.", "parameters": {"type": "dict", "properties": {"deck_size": {"type": "integer", "description": "The size of the deck. Default is 52."}, "hand_size": {"type": "integer", "description": "The size of the hand. Default is 5."}}, "required": ["deck_size", "hand_size"]}}]}
{"id": "simple_340", "question": [[{"role": "user", "content": "Determine the winner in a Poker game with John having a Hand of 8\u2665, 10\u2665, J\u2665, Q\u2665, K\u2665 and Mike having 9\u2660, J\u2660, 10\u2660, Q\u2660, K\u2660."}]], "function": [{"name": "card_games.poker_determine_winner", "description": "Determines the winner in a game of Poker based on the cards in each players' hands.", "parameters": {"type": "dict", "properties": {"player1": {"type": "string", "description": "The first player's name."}, "hand1": {"type": "array", "items": {"type": "string"}, "description": "The list of cards (as strings) in first player's hand. E.g ['10\u2660', 'J\u2660']"}, "player2": {"type": "string", "description": "The second player's name."}, "hand2": {"type": "array", "items": {"type": "string"}, "description": "The list of cards (as strings) in second player's hand. E.g ['9\u2665', '10\u2665']"}}, "required": ["player1", "hand1", "player2", "hand2"]}}]}
{"id": "simple_341", "question": [[{"role": "user", "content": "What are the odds of drawing a heart card from a deck without joker?"}]], "function": [{"name": "deck_of_cards.odds", "description": "Compute the probability of drawing a specific suit from a given deck of cards.", "parameters": {"type": "dict", "properties": {"suit": {"type": "string", "description": "The card suit. Valid values include: 'spades', 'clubs', 'hearts', 'diamonds'."}, "deck_type": {"type": "string", "description": "Type of deck, normal deck includes joker, and without_joker deck excludes joker.", "default": "normal"}}, "required": ["suit", "deck_type"]}}]}
{"id": "simple_342", "question": [[{"role": "user", "content": "Find all multi-player games released in 2019 with an ESRB rating of 'Everyone'"}]], "function": [{"name": "game_list.get_games", "description": "Get a list of video games based on release year, multiplayer functionality and ESRB rating", "parameters": {"type": "dict", "properties": {"release_year": {"type": "integer", "description": "The year the game was released."}, "multiplayer": {"type": "boolean", "description": "Whether the game has multiplayer functionality."}, "ESRB_rating": {"type": "string", "description": "The ESRB rating of the game."}}, "required": ["release_year", "multiplayer", "ESRB_rating"]}}]}
{"id": "simple_343", "question": [[{"role": "user", "content": "Fetch player statistics of 'Zelda' on Switch for user 'Sam'."}]], "function": [{"name": "game_stats.fetch_player_statistics", "description": "Fetch player statistics for a specific video game for a given user.", "parameters": {"type": "dict", "properties": {"game": {"type": "string", "description": "The name of the video game."}, "username": {"type": "string", "description": "The username of the player."}, "platform": {"type": "string", "description": "The platform user is playing on.", "default": "PC"}}, "required": ["game", "username"]}}]}
{"id": "simple_344", "question": [[{"role": "user", "content": "What's the power rating for the Weapon 'Guardian Sword+' in the game 'Legend of Zelda: Breath of the Wild'?"}]], "function": [{"name": "get_game_item_stats", "description": "Retrieve the statistics of a specific item in a specific video game.", "parameters": {"type": "dict", "properties": {"game": {"type": "string", "description": "The game to retrieve information from."}, "item": {"type": "string", "description": "The name of the item in the game."}, "stat": {"type": "string", "description": "Specific statistic required."}}, "required": ["game", "item", "stat"]}}]}
{"id": "simple_345", "question": [[{"role": "user", "content": "Find the value of a vintage Super Mario Bros. game from 1985 like new."}]], "function": [{"name": "game_valuation", "description": "Get the current market value of a vintage video game.", "parameters": {"type": "dict", "properties": {"game_name": {"type": "string", "description": "The name of the game."}, "release_year": {"type": "integer", "description": "The year the game was released."}, "condition": {"type": "string", "enum": ["New", "Like New", "Used", "Fair", "Poor"], "description": "The condition of the game. Default is 'Used'."}}, "required": ["game_name", "release_year"]}}]}
{"id": "simple_346", "question": [[{"role": "user", "content": "Get all collectable items from the game 'Animal Crossing: New Horizons' during the Spring season."}]], "function": [{"name": "get_collectables_in_season", "description": "Retrieve a list of collectable items in a specific game during a specified season.", "parameters": {"type": "dict", "properties": {"game_name": {"type": "string", "description": "Name of the game."}, "season": {"type": "string", "description": "The season for which to retrieve the collectable items."}, "item_type": {"type": "string", "description": "The type of item to search for. Default is 'all'. Possible values: 'all', 'bug', 'fish', 'sea creatures', etc."}}, "required": ["game_name", "season"]}}]}
{"id": "simple_347", "question": [[{"role": "user", "content": "Get me the details of the last game played by Liverpool F.C. Include its statistics."}]], "function": [{"name": "soccer.get_last_match", "description": "Retrieve the details of the last match played by a specified soccer club.", "parameters": {"type": "dict", "properties": {"team_name": {"type": "string", "description": "The name of the soccer club."}, "include_stats": {"type": "boolean", "description": "If true, include match statistics like possession, shots on target etc. Default is false."}}, "required": ["team_name"]}}]}
{"id": "simple_348", "question": [[{"role": "user", "content": "Create a new player profile for the game with name 'StarPlayer' and character class 'Mage', set the starting level to 5."}]], "function": [{"name": "create_player_profile", "description": "Create a new player profile with character name, class and starting level.", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The desired name of the player."}, "_class": {"type": "string", "description": "The character class for the player"}, "starting_level": {"type": "integer", "description": "The starting level for the player", "default": 1}}, "required": ["player_name", "_class"]}}]}
{"id": "simple_349", "question": [[{"role": "user", "content": "Find the highest score achieved by any player in the online game 'Overwatch' on PC globally."}]], "function": [{"name": "game_score.highest", "description": "Retrieve the highest score achieved by any player in a specific game.", "parameters": {"type": "dict", "properties": {"game": {"type": "string", "description": "The name of the online game."}, "platform": {"type": "string", "description": "The platform where the game is played, e.g. PC, Xbox, Playstation"}, "region": {"type": "string", "description": "The geographic region of the player. Defaults to 'Global'"}}, "required": ["game", "platform"]}}]}
{"id": "simple_350", "question": [[{"role": "user", "content": "Get the highest scoring player of game 'Valorant' in 2022 season."}]], "function": [{"name": "get_highest_scoring_player", "description": "Retrieve the highest scoring player in a specific game and season.", "parameters": {"type": "dict", "properties": {"game": {"type": "string", "description": "The game in which you want to find the highest scoring player."}, "season": {"type": "string", "description": "The season during which the high score was achieved."}}, "required": ["game", "season"]}}]}
{"id": "simple_351", "question": [[{"role": "user", "content": "Find me a multiplayer game with rating above 4.5 and compatible with Windows 10."}]], "function": [{"name": "multiplayer_game_finder", "description": "Locate multiplayer games that match specific criteria such as rating, platform compatibility, genre, etc.", "parameters": {"type": "dict", "properties": {"platform": {"type": "string", "description": "The platform you want the game to be compatible with, e.g. Windows 10, PS5."}, "rating": {"type": "float", "description": "Desired minimum game rating on a 5.0 scale."}, "genre": {"type": "string", "description": "Desired game genre, e.g. Action, Adventure, Racing. Default is 'Action'.", "enum": ["Action", "Adventure", "Racing", "Strategy", "Simulation"]}}, "required": ["platform", "rating"]}}]}
{"id": "simple_352", "question": [[{"role": "user", "content": "Get the average user score for the game 'The Legend of Zelda: Breath of the Wild' from GameSpot."}]], "function": [{"name": "gamespot.getAverageUserScore", "description": "Retrieve the average user score of a game from GameSpot.", "parameters": {"type": "dict", "properties": {"game_name": {"type": "string", "description": "The name of the game."}, "platform": {"type": "string", "description": "The platform the game was released on (e.g., Nintendo Switch, PS5, etc.)", "default": "all platforms"}}, "required": ["game_name", "platform"]}}]}
{"id": "simple_353", "question": [[{"role": "user", "content": "What are some gluten-free recipes for dinner?"}]], "function": [{"name": "find_recipes", "description": "Find recipes based on dietary restrictions, meal type, and preferred ingredients.", "parameters": {"type": "dict", "properties": {"diet": {"type": "string", "description": "The dietary restrictions, e.g., 'vegan', 'gluten-free'."}, "meal_type": {"type": "string", "description": "The type of meal, e.g., 'dinner', 'breakfast'."}, "ingredients": {"type": "array", "items": {"type": "string"}, "description": "The preferred ingredients. If left blank, it will default to return general recipes."}}, "required": ["diet", "meal_type"]}}]}
{"id": "simple_354", "question": [[{"role": "user", "content": "Find a vegan soup recipe that takes under 30 minutes to make."}]], "function": [{"name": "get_vegan_recipe", "description": "Retrieve a vegan soup recipe based on the provided cooking time.", "parameters": {"type": "dict", "properties": {"dish_type": {"type": "string", "description": "The type of dish, e.g. soup, dessert, etc.", "enum": ["soup", "main dish", "dessert", "salad"]}, "cooking_time": {"type": "integer", "description": "The maximum cooking time for the recipe in minutes."}, "ingredient_preference": {"type": "array", "items": {"type": "string"}, "description": "Preferred ingredients to be included in the recipe, if any. Default to not use it if not provided."}}, "required": ["dish_type", "cooking_time"]}}]}
{"id": "simple_355", "question": [[{"role": "user", "content": "How many calories in the Beef Lasagna Recipe from Foodnetwork.com?"}]], "function": [{"name": "recipe_info.get_calories", "description": "Retrieve the amount of calories from a specific recipe in a food website.", "parameters": {"type": "dict", "properties": {"website": {"type": "string", "description": "The food website that has the recipe."}, "recipe": {"type": "string", "description": "Name of the recipe."}, "optional_meal_time": {"type": "string", "description": "Specific meal time of the day for the recipe (optional, could be 'Breakfast', 'Lunch', 'Dinner'). Default is all if not specified."}}, "required": ["website", "recipe"]}}]}
{"id": "simple_356", "question": [[{"role": "user", "content": "Find me a recipe that serves 2 people, is vegan, and takes under 30 minutes to prepare."}]], "function": [{"name": "recipe_finder.find", "description": "Find a recipe based on dietary preferences, number of servings, and preparation time.", "parameters": {"type": "dict", "properties": {"servings": {"type": "integer", "description": "The number of people that the recipe should serve."}, "diet": {"type": "string", "description": "Any dietary restrictions like 'vegan', 'vegetarian', 'gluten-free' etc."}, "prep_time": {"type": "integer", "description": "The maximum amount of time (in minutes) the preparation should take. Default is 60 minutes."}}, "required": ["servings", "diet"]}}]}
{"id": "simple_357", "question": [[{"role": "user", "content": "Get the recipe for vegan chocolate cake including the steps for preparation."}]], "function": [{"name": "get_recipe", "description": "Fetch the recipe for a specific dish along with preparation steps.", "parameters": {"type": "dict", "properties": {"dish_name": {"type": "string", "description": "Name of the dish whose recipe needs to be fetched."}, "diet_preference": {"type": "string", "description": "Preferred dietary consideration like vegan, vegetarian, gluten-free etc. Default is none.", "default": "none"}}, "required": ["dish_name"]}}]}
{"id": "simple_358", "question": [[{"role": "user", "content": "Find a gluten-free cookie recipe that takes less than 30 minutes to prepare."}]], "function": [{"name": "recipe_search", "description": "Search for a cooking recipe based on specific dietary needs and time constraint.", "parameters": {"type": "dict", "properties": {"diet": {"type": "array", "items": {"type": "string", "enum": ["Gluten Free", "Dairy Free", "Vegan", "Vegetarian"]}, "description": "Specific dietary need."}, "time_limit": {"type": "integer", "description": "The maximum time to prepare the recipe in minutes. Default is 60 minutes."}, "dish": {"type": "string", "description": "The name of the dish to search for. Default is not use if not specified."}}, "required": ["dish", "diet"]}}]}
{"id": "simple_359", "question": [[{"role": "user", "content": "Give me a recipe for a vegetarian pasta with cheese for 2 servings."}]], "function": [{"name": "recipe_search", "description": "Search for a recipe given dietary restriction, ingredients, and number of servings.", "parameters": {"type": "dict", "properties": {"dietary_restriction": {"type": "string", "description": "The dietary restriction, e.g., 'Vegetarian'."}, "ingredients": {"type": "array", "items": {"type": "string"}, "description": "The list of ingredients."}, "servings": {"type": "integer", "description": "The number of servings the recipe should make"}}, "required": ["dietary_restriction", "ingredients", "servings"]}}]}
{"id": "simple_360", "question": [[{"role": "user", "content": "Find a recipe for pasta carbonara which contains only less than 500 calories."}]], "function": [{"name": "find_recipe", "description": "Locate a recipe based on name and its calorie content", "parameters": {"type": "dict", "properties": {"recipeName": {"type": "string", "description": "The recipe's name."}, "maxCalories": {"type": "integer", "description": "The maximum calorie content of the recipe.", "default": 1000}}, "required": ["recipeName"]}}]}
{"id": "simple_361", "question": [[{"role": "user", "content": "Find Italian restaurants near New York city that serves gluten-free options."}]], "function": [{"name": "restaurant_finder", "description": "Locate restaurants based on certain criteria such as cuisine, city, and dietary preferences.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "City where you are looking for the restaurant."}, "cuisine": {"type": "string", "description": "Type of cuisine you are interested in."}, "diet": {"type": "string", "description": "Dietary preferences. e.g. 'Vegetarian', 'Gluten-free', etc. Default 'Vegetarian'."}}, "required": ["city", "cuisine"]}}]}
{"id": "simple_362", "question": [[{"role": "user", "content": "What are the top five sushi restaurants with high reviews i.e. above 4/5 in Tokyo?"}]], "function": [{"name": "get_best_sushi_places", "description": "Returns the best sushi places given the city, review_rate and top number.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city in which to look for the sushi places."}, "top": {"type": "integer", "description": "The number of top sushi places to be returned."}, "review_rate": {"type": "float", "description": "The review rating to filter the sushi places. Places with review ratings above this value will be returned. Default 0.00."}}, "required": ["city", "top"]}}]}
{"id": "simple_363", "question": [[{"role": "user", "content": "Find the closest sushi restaurant with a patio in Boston."}]], "function": [{"name": "restaurant_search.find_closest", "description": "Locate the closest sushi restaurant based on certain criteria, such as the presence of a patio.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city, for instance Boston, MA"}, "cuisine": {"type": "string", "description": "Type of food like Sushi."}, "amenities": {"type": "array", "items": {"type": "string", "enum": ["Patio", "Wi-Fi", "Happy Hour", "Wheelchair Accessible"]}, "description": "Preferred amenities in the restaurant. Default 'Wi-Fi'."}}, "required": ["location", "cuisine"]}}]}
{"id": "simple_364", "question": [[{"role": "user", "content": "Can I find an Italian restaurant with Gluten-free options near Brooklyn?"}]], "function": [{"name": "find_restaurant", "description": "Locate nearby restaurants based on user defined criteria", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location where user wants to search for a restaurant."}, "type": {"type": "string", "description": "The type of the cuisine/restaurant."}, "diet_option": {"type": "string", "description": "Special dietary preferences."}}, "required": ["location", "type", "diet_option"]}}]}
{"id": "simple_365", "question": [[{"role": "user", "content": "How many ounces in 2 pounds of butter?"}]], "function": [{"name": "cooking_conversion.convert", "description": "Convert cooking measurements from one unit to another.", "parameters": {"type": "dict", "properties": {"quantity": {"type": "integer", "description": "The quantity to be converted."}, "from_unit": {"type": "string", "description": "The unit to convert from."}, "to_unit": {"type": "string", "description": "The unit to convert to."}, "item": {"type": "string", "description": "The item to be converted."}}, "required": ["quantity", "from_unit", "to_unit", "item"]}}]}
{"id": "simple_366", "question": [[{"role": "user", "content": "How many teaspoons are in 2 tablespoons for measurement in my recipe?"}]], "function": [{"name": "recipe.unit_conversion", "description": "Convert a value from one kitchen unit to another for cooking purposes.", "parameters": {"type": "dict", "properties": {"value": {"type": "integer", "description": "The value to be converted."}, "from_unit": {"type": "string", "description": "The unit to convert from. Supports 'teaspoon', 'tablespoon', 'cup', etc."}, "to_unit": {"type": "string", "description": "The unit to convert to. Supports 'teaspoon', 'tablespoon', 'cup', etc."}, "precision": {"type": "integer", "description": "The precision to round the output to, in case of a non-integer result. Optional, default is 1."}}, "required": ["value", "from_unit", "to_unit"]}}]}
{"id": "simple_367", "question": [[{"role": "user", "content": "Find me a vegan recipe for brownies which prep time is under 30 minutes."}]], "function": [{"name": "find_recipe", "description": "Find a recipe based on the dietary restrictions, recipe type, and time constraints.", "parameters": {"type": "dict", "properties": {"dietary_restrictions": {"type": "string", "description": "Dietary restrictions e.g. vegan, vegetarian, gluten free, dairy free."}, "recipe_type": {"type": "string", "description": "Type of the recipe. E.g. dessert, main course, breakfast."}, "time": {"type": "integer", "description": "Time limit in minutes to prep the meal."}}, "required": ["dietary_restrictions", "recipe_type", "time"]}}]}
{"id": "simple_368", "question": [[{"role": "user", "content": "How much time will it take to cook a roast chicken of 1.5 kg?"}]], "function": [{"name": "calculate_cooking_time", "description": "Calculate the cooking time for a roast chicken.", "parameters": {"type": "dict", "properties": {"weight_kg": {"type": "float", "description": "The weight of the chicken in kilograms."}, "cooking_method": {"type": "string", "description": "The method of cooking, defaults to 'roast'."}, "temp_celsius": {"type": "integer", "description": "The cooking temperature in degrees celsius, defaults to 180."}}, "required": ["weight_kg"]}}]}
{"id": "simple_369", "question": [[{"role": "user", "content": "Find a grocery store near me with organic fruits and vegetables in Houston."}]], "function": [{"name": "grocery_store.find_nearby", "description": "Locate nearby grocery stores based on specific criteria like organic fruits and vegetables.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Houston, TX"}, "categories": {"type": "array", "items": {"type": "string", "enum": ["Organic", "Vegetables", "Fruits", "Dairy", "Seafood", "Bakery"]}, "description": "Categories of items to be found in the grocery store. Default is all if not specified."}}, "required": ["location"]}}]}
{"id": "simple_370", "question": [[{"role": "user", "content": "Order three bottles of olive oil and a five pound bag of rice from Safeway in Palo Alto."}]], "function": [{"name": "safeway.order", "description": "Order specified items from a Safeway location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location of the Safeway store, e.g. Palo Alto, CA."}, "items": {"type": "array", "items": {"type": "string"}, "description": "List of items to order."}, "quantity": {"type": "array", "items": {"type": "integer"}, "description": "Quantity of each item in the order list."}}, "required": ["location", "items", "quantity"]}}]}
{"id": "simple_371", "question": [[{"role": "user", "content": "Check the price of tomatoes and lettuce at the Whole Foods in Los Angeles."}]], "function": [{"name": "whole_foods.check_price", "description": "Check the price of items at a specific Whole Foods location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "Location of the Whole Foods store."}, "items": {"type": "array", "items": {"type": "string"}, "description": "List of items for which the price needs to be checked."}}, "required": ["location", "items"]}}]}
{"id": "simple_372", "question": [[{"role": "user", "content": "Find the top five organic bananas brands on the basis of rating from Whole Foods store."}]], "function": [{"name": "whole_foods.find_top_brands", "description": "Get top brands based on a specific product from Whole Foods", "parameters": {"type": "dict", "properties": {"product": {"type": "string", "description": "The product for which the top brands should be fetched."}, "number": {"type": "integer", "description": "Number of top brands to be fetched. Default is 5"}, "organic": {"type": "boolean", "description": "If the product should be organic. Default is false"}}, "required": ["product"]}}]}
{"id": "simple_373", "question": [[{"role": "user", "content": "I want to buy apples, rice, and 12 pack of bottled water from a Walmart near San Jose. Show me the product information and stock availability."}]], "function": [{"name": "walmart.purchase", "description": "Retrieve information of items from Walmart including stock availability.", "parameters": {"type": "dict", "properties": {"loc": {"type": "string", "description": "Location of the nearest Walmart."}, "product_list": {"type": "array", "items": {"type": "string"}, "description": "Items to be purchased listed in an array."}, "pack_size": {"type": "array", "items": {"type": "integer"}, "description": "Size of the product pack if applicable. The size of the array should be equal to product_list. Default is not use it if not specified."}}, "required": ["loc", "product_list"]}}]}
{"id": "simple_374", "question": [[{"role": "user", "content": "Check the amount of protein, calories and carbs in an avocado from Walmart."}]], "function": [{"name": "grocery_info.nutritional_info", "description": "Retrieve nutritional information for a given food item from a particular store", "parameters": {"type": "dict", "properties": {"store": {"type": "string", "description": "The store where the item is available"}, "food": {"type": "string", "description": "Food item for which information is needed."}, "information": {"type": "array", "items": {"type": "string", "enum": ["Protein", "Calories", "Carbohydrates", "Fat", "Fiber"]}, "description": "Nutritional details required."}}, "required": ["store", "food", "information"]}}]}
{"id": "simple_375", "question": [[{"role": "user", "content": "Check the total price for three pumpkins and two dozen eggs at Walmart."}]], "function": [{"name": "walmart.check_price", "description": "Calculate total price for given items and their quantities at Walmart.", "parameters": {"type": "dict", "properties": {"items": {"type": "array", "items": {"type": "string"}, "description": "List of items to be priced."}, "quantities": {"type": "array", "items": {"type": "integer"}, "description": "Quantity of each item corresponding to the items list."}, "store_location": {"type": "string", "description": "The store location for specific pricing (optional). Default to all if not specified."}}, "required": ["items", "quantities"]}}]}
{"id": "simple_376", "question": [[{"role": "user", "content": "What time is it currently in London, UK in 24 hour format?"}]], "function": [{"name": "time_zone_converter", "description": "Retrieve the current time of a specific city.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city you want to know the current time for."}, "country": {"type": "string", "description": "The country where the city is located."}, "display_format": {"type": "string", "description": "The time display format: '12h' or '24h'. Default is '24h'."}}, "required": ["city", "country"]}}]}
{"id": "simple_377", "question": [[{"role": "user", "content": "What is the current time in Sydney, Australia?"}]], "function": [{"name": "get_current_time", "description": "Retrieve the current time for a specified city and country.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city for which the current time is to be retrieved."}, "country": {"type": "string", "description": "The country where the city is located."}, "format": {"type": "string", "description": "The format in which the time is to be displayed, optional (defaults to 'HH:MM:SS')."}}, "required": ["city", "country"]}}]}
{"id": "simple_378", "question": [[{"role": "user", "content": "Convert time 3pm from New York time zone to London time zone."}]], "function": [{"name": "timezone.convert", "description": "Convert time from one time zone to another.", "parameters": {"type": "dict", "properties": {"time": {"type": "string", "description": "The local time you want to convert, e.g. 3pm"}, "from_timezone": {"type": "string", "description": "The time zone you want to convert from."}, "to_timezone": {"type": "string", "description": "The time zone you want to convert to."}}, "required": ["time", "from_timezone", "to_timezone"]}}]}
{"id": "simple_379", "question": [[{"role": "user", "content": "What's the current time in Sydney, Australia?"}]], "function": [{"name": "get_current_time", "description": "Retrieve the current time in a specific time zone.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The name of the city."}, "country": {"type": "string", "description": "The name of the country."}, "timezone": {"type": "string", "description": "The optional timezone to get current time. Default "}}, "required": ["location", "country"]}}]}
{"id": "simple_380", "question": [[{"role": "user", "content": "Book a single room at a pet friendly hotel near Manhattan, New York for 3 nights starting from March 10th, 2023."}]], "function": [{"name": "hotel_booking", "description": "Books a hotel room given the location, room type, stay duration and any additional preferences.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city where you want to book the hotel."}, "room_type": {"type": "string", "description": "Type of the room required. Options: 'single', 'double', 'deluxe', etc."}, "duration": {"type": "integer", "description": "The number of nights you want to book the hotel for."}, "start_date": {"type": "string", "description": "The date when your stay begins."}, "preferences": {"type": "array", "items": {"type": "string", "enum": ["pet_friendly", "gym", "swimming_pool", "free_breakfast", "parking"]}, "description": "Optional preferences of stay at the hotel. Default to use all if not specified."}}, "required": ["location", "room_type", "duration", "start_date"]}}]}
{"id": "simple_381", "question": [[{"role": "user", "content": "Check if any Hilton Hotel is available for two adults in Paris from 2023 April 4th to April 8th?"}]], "function": [{"name": "hilton_hotel.check_availability", "description": "Check hotel availability for a specific location and time frame.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city where you want to check hotel availability."}, "check_in_date": {"type": "string", "description": "The check-in date in the format YYYY-MM-DD."}, "check_out_date": {"type": "string", "description": "The check-out date in the format YYYY-MM-DD."}, "no_of_adults": {"type": "integer", "description": "The number of adults for the hotel booking."}, "hotel_chain": {"type": "string", "description": "The hotel chain where you want to book the hotel.", "default": "Hilton"}}, "required": ["location", "check_in_date", "check_out_date", "no_of_adults"]}}]}
{"id": "simple_382", "question": [[{"role": "user", "content": "Book a single room for two nights at the Hilton Hotel in Chicago, starting from 10th December 2022."}]], "function": [{"name": "book_hotel", "description": "Book a room of specified type for a particular number of nights at a specific hotel, starting from a specified date.", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "location": {"type": "string", "description": "The city in which the hotel is located."}, "room_type": {"type": "string", "description": "The type of room to be booked."}, "start_date": {"type": "string", "description": "The start date for the booking."}, "nights": {"type": "integer", "description": "The number of nights for which the booking is to be made."}}, "required": ["hotel_name", "location", "room_type", "start_date", "nights"]}}]}
{"id": "simple_383", "question": [[{"role": "user", "content": "I would like to book a single room for two nights at The Plaza hotel."}]], "function": [{"name": "book_room", "description": "Book a room in a specified hotel.", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "room_type": {"type": "string", "description": "The type of room to be booked."}, "num_nights": {"type": "integer", "description": "The number of nights to book the room for."}}, "required": ["hotel_name", "room_type", "num_nights"]}}]}
{"id": "simple_384", "question": [[{"role": "user", "content": "Book a hotel room for two adults and one child in Paris, France from July 10, 2022 to July 20, 2022."}]], "function": [{"name": "hotel_booking.book", "description": "Book a hotel room given the city, date, and the number of adults and children.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city where the hotel is located."}, "from_date": {"type": "string", "description": "The start date of the booking. The format is MM-DD-YYYY."}, "to_date": {"type": "string", "description": "The end date of the booking. The format is MM-DD-YYYY."}, "adults": {"type": "integer", "description": "The number of adults for the booking."}, "children": {"type": "integer", "description": "The number of children for the booking."}, "room_type": {"type": "string", "description": "The type of the room, default is 'Standard'. Options are 'Standard', 'Deluxe', 'Suite'.", "default": "Standard"}}, "required": ["city", "from_date", "to_date", "adults", "children"]}}]}
{"id": "simple_385", "question": [[{"role": "user", "content": "Book a hotel room with king size bed in Los Angeles for 2 nights starting from 15th October,2023."}]], "function": [{"name": "hotel_bookings.book_room", "description": "Book a hotel room based on specific criteria like location, room type, and check-in and check-out dates.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state where you want to book the hotel, e.g. Los Angeles, CA"}, "room_type": {"type": "string", "description": "Preferred type of room in the hotel, e.g. king size, queen size, deluxe, suite etc."}, "check_in_date": {"type": "string", "description": "Check-in date for the hotel. Format - DD-MM-YYYY."}, "no_of_nights": {"type": "integer", "description": "Number of nights for the stay."}, "no_of_rooms": {"type": "integer", "description": "Number of rooms to book. Default is 1.", "default": 1}}, "required": ["location", "room_type", "check_in_date", "no_of_nights"]}}]}
{"id": "simple_386", "question": [[{"role": "user", "content": "Book a luxury room in Hotel Paradise, Las Vegas, with a city view for 3 days starting from May 12, 2022."}]], "function": [{"name": "book_hotel", "description": "Book a room in a specific hotel with particular preferences", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "location": {"type": "string", "description": "The location of the hotel."}, "room_type": {"type": "string", "description": "The type of room preferred."}, "start_date": {"type": "string", "description": "The starting date of the stay in format MM-DD-YYYY."}, "stay_duration": {"type": "integer", "description": "The duration of the stay in days."}, "view": {"type": "string", "description": "The preferred view from the room, can be ignored if no preference. If none provided, assumes no preference.", "default": "No preference"}}, "required": ["hotel_name", "location", "room_type", "start_date", "stay_duration"]}}]}
{"id": "simple_387", "question": [[{"role": "user", "content": "Book a hotel room at the Plaza Hotel in New York for 3 nights starting from 1st June 2022"}]], "function": [{"name": "hotel_booking", "description": "Books a hotel room for a specific date range.", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "location": {"type": "string", "description": "The city and state, e.g. New York, NY."}, "start_date": {"type": "string", "description": "The start date of the reservation. Use format 'YYYY-MM-DD'."}, "end_date": {"type": "string", "description": "The end date of the reservation. Use format 'YYYY-MM-DD'."}, "rooms": {"type": "integer", "default": 1, "description": "The number of rooms to reserve."}}, "required": ["hotel_name", "location", "start_date", "end_date"]}}]}
{"id": "simple_388", "question": [[{"role": "user", "content": "How many Canadian dollars can I get for 500 US dollars?"}]], "function": [{"name": "currency_exchange.convert", "description": "Convert an amount from a base currency to a target currency based on the current exchange rate.", "parameters": {"type": "dict", "properties": {"base_currency": {"type": "string", "description": "The currency to convert from."}, "target_currency": {"type": "string", "description": "The currency to convert to."}, "amount": {"type": "integer", "description": "The amount in base currency to convert"}}, "required": ["base_currency", "target_currency", "amount"]}}]}
{"id": "simple_389", "question": [[{"role": "user", "content": "Calculate the current cost in British Pounds if I need to convert 200 US dollars."}]], "function": [{"name": "currency_converter", "description": "Calculates the current cost in target currency given the amount in base currency and exchange rate", "parameters": {"type": "dict", "properties": {"base_currency": {"type": "string", "description": "The currency to convert from."}, "target_currency": {"type": "string", "description": "The currency to convert to."}, "amount": {"type": "float", "description": "The amount in base currency"}}, "required": ["base_currency", "target_currency", "amount"]}}]}
{"id": "simple_390", "question": [[{"role": "user", "content": "Convert 150 Euros to Canadian dollars."}]], "function": [{"name": "currency_conversion.convert", "description": "Convert a value from one currency to another.", "parameters": {"type": "dict", "properties": {"amount": {"type": "integer", "description": "The amount to be converted."}, "from_currency": {"type": "string", "description": "The currency to convert from."}, "to_currency": {"type": "string", "description": "The currency to convert to."}}, "required": ["amount", "from_currency", "to_currency"]}}]}
{"id": "simple_391", "question": [[{"role": "user", "content": "Get the exchange rate from British pounds to Japanese yen with the fee 0.02 included."}]], "function": [{"name": "get_exchange_rate_with_fee", "description": "Retrieve the exchange rate between two currencies including transaction fee.", "parameters": {"type": "dict", "properties": {"base_currency": {"type": "string", "description": "The base currency."}, "target_currency": {"type": "string", "description": "The target currency."}, "fee": {"type": "float", "description": "The transaction fee in percentage. Default is 0%."}}, "required": ["base_currency", "target_currency", "fee"]}}]}
{"id": "simple_392", "question": [[{"role": "user", "content": "Get me the latest exchange rate from British Pounds to Japanese Yen."}]], "function": [{"name": "latest_exchange_rate", "description": "Retrieve the latest exchange rate between two specified currencies.", "parameters": {"type": "dict", "properties": {"source_currency": {"type": "string", "description": "The currency you are converting from."}, "target_currency": {"type": "string", "description": "The currency you are converting to."}, "amount": {"type": "float", "description": "The amount to be converted. If omitted, default to exchange rate of 1 unit source currency"}}, "required": ["source_currency", "target_currency"]}}]}
{"id": "simple_393", "question": [[{"role": "user", "content": "How much will 20000 Japanese Yen be in United States Dollar?"}]], "function": [{"name": "convert_currency", "description": "Converts an amount from a particular currency to another currency.", "parameters": {"type": "dict", "properties": {"base_currency": {"type": "string", "description": "The base currency in which the original amount is present."}, "target_currency": {"type": "string", "description": "The currency to which you want to convert."}, "amount": {"type": "integer", "description": "The amount you want to convert."}}, "required": ["base_currency", "target_currency", "amount"]}}]}
{"id": "simple_394", "question": [[{"role": "user", "content": "Get me the travel distance and duration from the Eiffel Tower to the Louvre Museum"}]], "function": [{"name": "maps.get_distance_duration", "description": "Retrieve the travel distance and estimated travel time from one location to another via car", "parameters": {"type": "dict", "properties": {"start_location": {"type": "string", "description": "Starting point of the journey"}, "end_location": {"type": "string", "description": "Ending point of the journey"}, "traffic": {"type": "boolean", "description": "If true, considers current traffic. Default is false."}}, "required": ["start_location", "end_location"]}}]}
{"id": "simple_395", "question": [[{"role": "user", "content": "Find the nearest parking lot within 2 miles of Central Park in New York."}]], "function": [{"name": "parking_lot.find_nearest", "description": "Locate the nearest parking lot based on a specific location and radius.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The reference location e.g. Central Park, NY"}, "radius": {"type": "integer", "description": "The maximum distance from the location in miles. Default is 5 miles"}, "type": {"type": "string", "description": "The type of parking lot. Default is 'public'."}}, "required": ["location", "radius"]}}]}
{"id": "simple_396", "question": [[{"role": "user", "content": "Find a hospital within 5 km radius around Denver, Colorado with pediatrics department."}]], "function": [{"name": "hospital.locate", "description": "Locate nearby hospitals based on location and radius. Options to include specific departments are available.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. Denver, CO"}, "radius": {"type": "integer", "description": "The radius within which you want to find the hospital in kms."}, "department": {"type": "string", "description": "Specific department within the hospital. Default is 'General Medicine'.", "enum": ["General Medicine", "Emergency", "Pediatrics", "Cardiology", "Orthopedics"]}}, "required": ["location", "radius"]}}]}
{"id": "simple_397", "question": [[{"role": "user", "content": "Find the distance between New York and Boston, accounting for terrain."}]], "function": [{"name": "distance_calculator.calculate", "description": "Calculate the distance between two locations, considering terrain.", "parameters": {"type": "dict", "properties": {"origin": {"type": "string", "description": "Starting location of the distance measurement."}, "destination": {"type": "string", "description": "Destination location of the distance measurement."}, "consider_terrain": {"type": "boolean", "description": "Whether to account for terrain in distance calculation, defaults to false."}}, "required": ["origin", "destination"]}}]}
{"id": "simple_398", "question": [[{"role": "user", "content": "What are the opening hours of the Metropolitan Museum of Art on Saturday?"}]], "function": [{"name": "get_museum_hours", "description": "Retrieve opening hours of a specified museum for the specified day.", "parameters": {"type": "dict", "properties": {"museum_name": {"type": "string", "description": "The name of the museum."}, "day": {"type": "string", "description": "Day of the week.", "enum": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]}}, "required": ["museum_name", "day"]}}]}
{"id": "simple_399", "question": [[{"role": "user", "content": "Find me the best Italian restaurants in New York City with average customer ratings of more than 4 and accepts credit cards."}]], "function": [{"name": "restaurant_search", "description": "Locates top rated restaurants based on specific criteria such as type of cuisine, ratings, and facilities.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. New York City, NY"}, "cuisine": {"type": "string", "description": "Preferred type of cuisine e.g., Italian, Indian, American, etc."}, "rating": {"type": "integer", "description": "Minimum average customer rating out of 5"}, "accepts_credit_cards": {"type": "boolean", "description": "If the restaurant should accept credit cards."}}, "required": ["location", "cuisine", "rating", "accepts_credit_cards"]}}]}