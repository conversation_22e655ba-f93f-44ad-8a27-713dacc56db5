{"id": "parallel_0", "ground_truth": [{"spotify.play": {"artist": ["Taylor Swift"], "duration": [20]}}, {"spotify.play": {"artist": ["Maroon 5"], "duration": [15]}}]}
{"id": "parallel_1", "ground_truth": [{"calculate_em_force": {"b_field": [5], "area": [2], "d_time": [4]}}, {"calculate_em_force": {"b_field": [5], "area": [2], "d_time": [10]}}]}
{"id": "parallel_2", "ground_truth": [{"calculate_resistance": {"length": [5], "area": [0.01], "resistivity": ["copper", ""]}}, {"calculate_resistance": {"length": [5], "area": [0.01], "resistivity": ["aluminum"]}}]}
{"id": "parallel_3", "ground_truth": [{"protein_info.get_sequence_and_3D": {"protein_name": ["human HbA1c", "HbA1c"], "model_3d": [true, ""]}}, {"protein_info.get_sequence_and_3D": {"protein_name": ["normal hemoglobin"], "model_3d": [true, ""]}}, {"protein_info.get_sequence_and_3D": {"protein_name": ["rat hemoglobin"], "model_3d": [true, ""]}}]}
{"id": "parallel_4", "ground_truth": [{"calculate_bmi": {"height": [6.0], "weight": [80]}}, {"calculate_bmi": {"height": [5.6], "weight": [60]}}]}
{"id": "parallel_5", "ground_truth": [{"streaming_services.shows_list_and_ratings": {"streaming_service": ["Netflix"], "show_list": [["Friends"]], "sort_by_rating": [true]}}, {"streaming_services.shows_list_and_ratings": {"streaming_service": ["Hulu"], "show_list": [["The Office", "Stranger Things"], ["Stranger Things", "The Office"]], "sort_by_rating": [true]}}]}
{"id": "parallel_6", "ground_truth": [{"calculate_sales_tax": {"purchase_amount": [30.45], "city": ["Chicago", "CHI"], "state": ["IL", "Illinois"]}}, {"calculate_sales_tax": {"purchase_amount": [52.33], "city": ["Sacramento"], "state": ["CA", "California"]}}, {"calculate_sales_tax": {"purchase_amount": [11.23], "city": ["Portland"], "state": ["OR", "Oregon"]}}]}
{"id": "parallel_7", "ground_truth": [{"math.factorial": {"number": [5]}}, {"math.factorial": {"number": [10]}}, {"math.factorial": {"number": [15]}}]}
{"id": "parallel_8", "ground_truth": [{"database_us_census.get_population": {"area": ["New York City", "NY", "New York City, NY", "NYC"], "type": ["city"], "year": ["", 2000]}}, {"database_us_census.get_population": {"area": ["Los Angeles", "Los Angeles, CA", "CA", "Los Angeles, CA"], "type": ["city"], "year": ["", 2000]}}, {"database_us_census.get_population": {"area": ["Alaska"], "type": ["state"], "year": ["", 2000]}}, {"database_us_census.get_population": {"area": ["USA", "United States", "United States of America"], "type": ["country"], "year": ["", 2000]}}]}
{"id": "parallel_9", "ground_truth": [{"find_movie_showing": {"location": ["San Diego", "San Diego, CA", "CA"], "movie": [["Tenet"]], "time": [["5 pm"], ["17:00"]]}}, {"find_movie_showing": {"location": ["San Diego", "San Diego, CA", "CA"], "movie": [["No Time To Die"]], "time": [["7:30 pm"], ["19:30"]]}}]}
{"id": "parallel_10", "ground_truth": [{"math.pythagoras": {"a": [3], "b": [4]}}, {"math.pythagoras": {"a": [5], "b": [12]}}]}
{"id": "parallel_11", "ground_truth": [{"ml.predict_house_price": {"location": ["New York", "New York, NY", "NYC"], "size": [3000]}}, {"ml.predict_house_price": {"location": ["Los Angeles", "Los Angeles, CA", "LA"], "size": [4000]}}]}
{"id": "parallel_12", "ground_truth": [{"model.DecisionTreeClassifier": {"criterion": ["gini"], "max_depth": [5], "random_state": [1]}}, {"model.DecisionTreeClassifier": {"criterion": ["entropy"], "max_depth": [10], "random_state": [1]}}]}
{"id": "parallel_13", "ground_truth": [{"confidence_interval.calculate": {"sample_std_dev": [10], "sample_size": [50], "sample_mean": [25], "confidence_level": [0.95]}}, {"confidence_interval.calculate": {"sample_std_dev": [10], "sample_size": [150], "sample_mean": [25], "confidence_level": [0.95]}}]}
{"id": "parallel_14", "ground_truth": [{"calculate_present_value": {"payment_per_year": [1000], "interest_rate": [0.05], "years": [20]}}, {"calculate_present_value": {"payment_per_year": [1000], "interest_rate": [0.05], "years": [30]}}, {"calculate_present_value": {"payment_per_year": [1000], "interest_rate": [0.05], "years": [10]}}]}
{"id": "parallel_15", "ground_truth": [{"calculate_capital_gains_tax": {"short_term_gain": [15000], "long_term_gain": [25000], "state": ["CA", "California"]}}, {"calculate_capital_gains_tax": {"short_term_gain": [20000], "long_term_gain": [50000], "state": ["FL", "Florida"]}}]}
{"id": "parallel_16", "ground_truth": [{"calculate_return_on_investment": {"initial_investment": [2000], "gain_loss": [500]}}, {"calculate_return_on_investment": {"initial_investment": [5000], "gain_loss": [-1000]}}]}
{"id": "parallel_17", "ground_truth": [{"get_stock_data": {"symbol": ["AAPL"], "data_points": [["price", "volume"], ["volume", "price"]]}}, {"get_stock_data": {"symbol": ["GOOG", "GOOGL"], "data_points": [["price", "volume"], ["volume", "price"]]}}, {"get_stock_data": {"symbol": ["MSFT"], "data_points": [["price", "volume"], ["volume", "price"]]}}]}
{"id": "parallel_18", "ground_truth": [{"financials.calculate_future_value": {"present_value": [1000], "annual_interest_rate": [0.05], "number_of_years": [1]}}, {"financials.calculate_future_value": {"present_value": [1000], "annual_interest_rate": [0.05], "number_of_years": [5]}}, {"financials.calculate_future_value": {"present_value": [1000], "annual_interest_rate": [0.05], "number_of_years": [10]}}]}
{"id": "parallel_19", "ground_truth": [{"calculate_mortgage_payment": {"loan_amount": [400000], "interest_rate": [0.04], "loan_term": [15]}}, {"calculate_mortgage_payment": {"loan_amount": [400000], "interest_rate": [0.04], "loan_term": [20]}}, {"calculate_mortgage_payment": {"loan_amount": [400000], "interest_rate": [0.04], "loan_term": [30]}}]}
{"id": "parallel_20", "ground_truth": [{"loan_eligibility_check": {"financial_institution": ["HSBC"], "loan_amount": [500000], "annual_income": [100000]}}, {"loan_eligibility_check": {"financial_institution": ["Wells Fargo"], "loan_amount": [700000], "annual_income": [120000]}}]}
{"id": "parallel_21", "ground_truth": [{"law_crimes.search": {"crime": ["money laundering"], "location": ["San Francisco", "SF"], "year": [2019]}}, {"law_crimes.search": {"crime": ["money laundering"], "location": ["Texas", "TX"], "year": [2018]}}]}
{"id": "parallel_22", "ground_truth": [{"court_info.get_case_status": {"case_number": ["XY1234"], "court": ["Los Angeles County Court", "Los Angeles", "Los Angeles, CA", "LA"], "details": ["status", ""]}}, {"court_info.get_case_status": {"case_number": ["GH5678"], "court": ["Orange County Court", "Orange County", "OC"], "details": ["status", ""]}}, {"court_info.get_case_status": {"case_number": ["XY1234"], "court": ["Los Angeles County Court", "Los Angeles", "Los Angeles, CA", "LA"], "details": ["trial_date"]}}, {"court_info.get_case_status": {"case_number": ["GH5678"], "court": ["Orange County Court", "Orange County", "OC"], "details": ["trial_date"]}}]}
{"id": "parallel_23", "ground_truth": [{"alimony_calculator.ca.calculate": {"payor_income": [10000], "recipient_income": [3000], "duration": [10]}}, {"alimony_calculator.ca.calculate": {"payor_income": [10000], "recipient_income": [3000], "duration": [20]}}]}
{"id": "parallel_24", "ground_truth": [{"law_case.get_details": {"case_number": ["28473"], "include_history": [true], "include_litigants": [true]}}, {"law_case.get_details": {"case_number": ["64725"], "include_history": [true], "include_litigants": [true]}}]}
{"id": "parallel_25", "ground_truth": [{"lawsuit.lookup": {"company_name": ["Dara Inc"], "year": [2019]}}, {"lawsuit.lookup": {"company_name": ["Dara Inc"], "year": [2018]}}]}
{"id": "parallel_26", "ground_truth": [{"court_case.find": {"location": ["New York District", "NY District", "New York", "New York, NY", "NY"], "case_number": [["67813"]], "case_type": ["Civil", ""]}}, {"court_case.find": {"location": ["New York District", "NY District", "New York", "New York, NY", "NY"], "case_number": [["71249"]], "case_type": ["Criminal"]}}, {"court_case.find": {"location": ["New York District", "NY District", "New York", "New York, NY", "NY"], "case_number": [["67813"]], "case_type": ["Criminal"]}}, {"court_case.find": {"location": ["New York District", "NY District", "New York", "New York, NY", "NY"], "case_number": [["71249"]], "case_type": ["Civil", ""]}}]}
{"id": "parallel_27", "ground_truth": [{"nature_reserve.find_nearby": {"location": ["Berkeley", "Berkeley,California", "CA"], "amenities": [["Picnic Tables", "Public Restrooms"], ["Public Restrooms", "Picnic Tables"]], "proximity": [10]}}, {"nature_reserve.find_nearby": {"location": ["Tokyo"], "amenities": [["Playgrounds", "Biking Trails"], ["Biking Trails", "Playgrounds"]], "proximity": [5]}}]}
{"id": "parallel_28", "ground_truth": [{"get_current_and_future_temperature": {"location": ["Seattle", "Seattle, Washington", "Seattle, WA"], "hours": [3]}}, {"get_current_and_future_temperature": {"location": ["Los Angeles", "Los Angeles, CA", "LA", "Los Angeles, California", "Los Angeles, CA"], "hours": [3]}}]}
{"id": "parallel_29", "ground_truth": [{"waste_calculation.calculate": {"population": [{"adults": [2], "children": [2], "singles": [0]}], "location": ["Los Angeles", "Los Angeles, CA", "LA"]}}, {"waste_calculation.calculate": {"population": [{"adults": [0], "children": [0], "singles": [1]}], "location": ["New York", "New York, NY", "NY", "New York City", "NYC"]}}]}
{"id": "parallel_30", "ground_truth": [{"book_flight": {"departure_city": ["San Francisco", "SF"], "destination_city": ["Tokyo"], "date": ["2022-05-03", "05/03/2022", "May 3rd, 2022", "May 3, 2022", "May 3rd 2022"]}}, {"book_flight": {"departure_city": ["Tokyo"], "destination_city": ["Sydney"], "date": ["2022-05-18", "05/18/2022", "May 18th, 2022", "May 18, 2022", "May 18th 2022"]}}]}
{"id": "parallel_31", "ground_truth": [{"history_fact.fetch": {"event": ["Treaty of Paris"], "depth": ["", "detailed"], "year": ["", 0]}}, {"history_fact.fetch": {"event": ["Magna Carta"], "depth": ["", "detailed"], "year": ["", 0]}}]}
{"id": "parallel_32", "ground_truth": [{"us_history.events_by_presidency": {"president_name": ["Abraham Lincoln"], "start_year": ["", 0], "end_year": ["", 2000]}}, {"us_history.events_by_presidency": {"president_name": ["George Washington"], "start_year": ["", 0], "end_year": ["", 2000]}}]}
{"id": "parallel_33", "ground_truth": [{"get_president_and_vp": {"year": [1980], "position": ["president"]}}, {"get_president_and_vp": {"year": [2016], "position": ["president"]}}, {"get_president_and_vp": {"year": [1975], "position": ["vice president"]}}, {"get_president_and_vp": {"year": [2011], "position": ["vice president"]}}]}
{"id": "parallel_34", "ground_truth": [{"religion_history.track": {"region": ["Egypt"], "religion": ["Christianity"], "start_year": [100], "end_year": [1500]}}, {"religion_history.track": {"region": ["Turkey"], "religion": ["Christianity"], "start_year": [100], "end_year": [1500]}}]}
{"id": "parallel_35", "ground_truth": [{"ancient_empires.get_religion_info": {"empire_name": ["Mauryan Empire"], "include_influences": [true]}}, {"ancient_empires.get_religion_info": {"empire_name": ["Persian Empire"], "include_influences": [true]}}]}
{"id": "parallel_36", "ground_truth": [{"paint_color_mixture": {"paint_type": ["Watercolor", "watercolor"], "color": ["Magenta", "magenta"]}}, {"paint_color_mixture": {"paint_type": ["Acrylic", "acrylic"], "color": ["Navy", "navy"]}}]}
{"id": "parallel_37", "ground_truth": [{"color_converter.get_color_info": {"color_name": ["navy"], "conversion_type": [["RGB", "HEX"], ["HEX", "RGB"]]}}, {"color_converter.get_color_info": {"color_name": ["purple"], "conversion_type": [["RGB", "HEX"], ["HEX", "RGB"]]}}, {"color_converter.get_color_info": {"color_name": ["maroon"], "conversion_type": [["RGB", "HEX"], ["HEX", "RGB"]]}}]}
{"id": "parallel_38", "ground_truth": [{"calc_distance": {"start_loc": ["New York", "New York, NY", "New York City", "NYC"], "end_loc": ["Washington DC", "Washington D.C."], "shortest_route": [true]}}, {"calc_distance": {"start_loc": ["Los Angeles", "Los Angeles, CA", "LA"], "end_loc": ["San Francisco", "SF"], "shortest_route": [true]}}]}
{"id": "parallel_39", "ground_truth": [{"museum_info.get_info": {"location": ["Washington D.C.", "Washington DC"], "details": [["Opening hours", "Adult tickets", "Child tickets"], ["Opening hours", "Child tickets", "Adult tickets"], ["Child tickets", "Opening hours", "Adult tickets"], ["Child tickets", "Adult tickets", "Opening hours"], ["Adult tickets", "Opening hours", "Child tickets"], ["Adult tickets", "Child tickets", "Opening hours"]]}}, {"museum_info.get_info": {"location": ["Paris"], "details": [["Opening hours", "Adult tickets", "Child tickets"], ["Opening hours", "Child tickets", "Adult tickets"], ["Child tickets", "Opening hours", "Adult tickets"], ["Child tickets", "Adult tickets", "Opening hours"], ["Adult tickets", "Opening hours", "Child tickets"], ["Adult tickets", "Child tickets", "Opening hours"]]}}]}
{"id": "parallel_40", "ground_truth": [{"museum.exhibition_detail": {"exhibition_name": ["Wonder of Nature"], "museum_name": ["Louvre", "Louvre Museum"], "visitor_type": [["child", "adult"], ["adult", "child"]]}}, {"museum.exhibition_detail": {"exhibition_name": ["Age of Reptiles"], "museum_name": ["British Museum"], "visitor_type": [["child", "adult"], ["adult", "child"]]}}]}
{"id": "parallel_41", "ground_truth": [{"find_music_instrument_store": {"location": ["San Francisco, CA", "San Francisco, CA", "San Francisco, California"], "instruments": [["Yamaha Acoustic Guitar", "Kawai Piano"], ["Kawai Piano", "Yamaha Acoustic Guitar"], ["Yamaha acoustic guitar", "Kawai piano"], ["Kawai piano", "Yamaha acoustic guitar"]]}}, {"find_music_instrument_store": {"location": ["Chicago, IL", "Chicago, Illinois", "Chicago, IL."], "instruments": [["Yamaha Acoustic Guitar", "Kawai Piano"], ["Kawai Piano", "Yamaha Acoustic Guitar"], ["Yamaha acoustic guitar", "Kawai piano"], ["Kawai piano", "Yamaha acoustic guitar"]]}}]}
{"id": "parallel_42", "ground_truth": [{"check_instrument_availability": {"instrument": ["Yamaha P125", "Yamaha P125 piano"], "city": ["Berlin"]}}, {"check_instrument_availability": {"instrument": ["Yamaha P125", "Yamaha P125 piano"], "city": ["Madrid"]}}]}
{"id": "parallel_43", "ground_truth": [{"concert_finder": {"location": ["San Francisco, California", "San Francisco, CA", "SF, California", "SF, CA"], "music_genre": ["rock"], "time_period": [30, ""]}}, {"concert_finder": {"location": ["San Francisco, California", "San Francisco, CA", "SF, California", "SF, CA"], "music_genre": ["jazz"], "time_period": [30, ""]}}, {"concert_finder": {"location": ["New York, New York", "New York, NY", "NYC", "NY, NY"], "music_genre": ["rock"], "time_period": [30, ""]}}, {"concert_finder": {"location": ["New York, New York", "New York, NY", "NYC", "NY, NY"], "music_genre": ["jazz"], "time_period": [30, ""]}}]}
{"id": "parallel_44", "ground_truth": [{"concert.find_nearby": {"location": ["Berlin"], "date": ["next Friday"], "genre": ["Classical", "classical"], "amenities": [["Parking"], ""]}}, {"concert.find_nearby": {"location": ["Paris"], "date": ["next Friday"], "genre": ["Classical", "classical"], "amenities": [["Parking"], ""]}}]}
{"id": "parallel_45", "ground_truth": [{"musicCharts.getMostPlayed": {"genre": ["Pop"], "region": ["Australia", "AU"], "duration": ["", 0]}}, {"musicCharts.getMostPlayed": {"genre": ["Rock"], "region": ["Australia", "AU"], "duration": ["", 0]}}]}
{"id": "parallel_46", "ground_truth": [{"calculate_winning_percentage": {"team": ["Lakers"], "season": [2018]}}, {"calculate_winning_percentage": {"team": ["Bulls"], "season": [2018]}}, {"calculate_winning_percentage": {"team": ["Lakers"], "season": [2020]}}, {"calculate_winning_percentage": {"team": ["Bulls"], "season": [2020]}}]}
{"id": "parallel_47", "ground_truth": [{"get_team_ranking": {"team": ["Barcelona", "Barca"], "league": ["UEFA Champions League", "Champions League"]}}, {"get_team_ranking": {"team": ["Manchester United", "Man United", "Man U", "MUFC"], "league": ["La Liga"]}}]}
{"id": "parallel_48", "ground_truth": [{"PokemonGO.get_moves": {"pokemon": ["Pikachu"], "move": ["", "Run"]}}, {"PokemonGO.get_moves": {"pokemon": ["Bulbasaur"], "move": ["Solar Beam"]}}]}
{"id": "parallel_49", "ground_truth": [{"player_status.check": {"team": ["RocketLeague"], "player_id": [3142], "season": [2017]}}, {"player_status.check": {"team": ["RocketLeague"], "player_id": [3142], "season": [2018]}}, {"player_status.check": {"team": ["RocketLeague"], "player_id": [3142], "season": [2019]}}]}
{"id": "parallel_50", "ground_truth": [{"game.save_progress": {"stage": [7], "mode": ["easy"], "level": ["user", ""]}}, {"game.save_progress": {"stage": [3], "mode": ["hard"], "level": ["user", ""]}}]}
{"id": "parallel_51", "ground_truth": [{"recipe_search.find": {"dish": ["Chicken Noodle Soup"], "diet": ["", "Keto"]}}, {"recipe_search.find": {"dish": ["Salad", "salad", "Vegan Salad", "vegan salad"], "diet": ["Vegan"]}}]}
{"id": "parallel_52", "ground_truth": [{"restaurant_finder": {"location": ["New York", "New York, NY", "New York City", "NYC", "NY"], "cuisine": ["Italian"], "preferences": [["Vegetarian"]]}}, {"restaurant_finder": {"location": ["Los Angeles", "Los Angeles, CA", "LA", "L.A."], "cuisine": ["Japanese"], "preferences": [["Delivery"], ""]}}]}
{"id": "parallel_53", "ground_truth": [{"get_cooking_recipe": {"dish_name": ["Lasagne Bolognese"], "serving_size": [4]}}, {"get_cooking_recipe": {"dish_name": ["Caesar Salad"], "serving_size": [2]}}]}
{"id": "parallel_54", "ground_truth": [{"whole_foods.order": {"location": ["downtown", "Downtown"], "items": [["pepperoni pizza", "chicken Caesar salad"], ["chicken Caesar salad", "pepperoni pizza"]], "size": ["large"]}}, {"whole_foods.order": {"location": ["uptown", "Uptown"], "items": [["pepperoni pizza", "chicken Caesar salad"], ["chicken Caesar salad", "pepperoni pizza"]], "size": ["large"]}}]}
{"id": "parallel_55", "ground_truth": [{"grocery_store.find_by_criteria": {"location": ["New York City", "NYC"], "criteria": [["24 hours"]]}}, {"grocery_store.find_by_criteria": {"location": ["SD", "San Diego"], "criteria": [["Home Delivery"]]}}]}
{"id": "parallel_56", "ground_truth": [{"hotel_booking.check_availability": {"hotel_name": ["Queens Hotel"], "location": ["Berlin, Germany"], "check_in_date": ["2022-03-10", "03/10/2022", "Mar.10,2022"], "check_out_date": ["2022-03-20", "03/20/2022", "Mar.20,2022"]}}, {"hotel_booking.check_availability": {"hotel_name": ["Royal Hotel"], "location": ["Paris, France"], "check_in_date": ["2022-04-05", "04/05/2022", "Apr.5,2022"], "check_out_date": ["2022-04-15", "04/15/2022", "Apr.15,2022"]}}]}
{"id": "parallel_57", "ground_truth": [{"hotel_booking.book": {"hotel_name": ["Sheraton Hotel", "Sheraton"], "location": ["New York", "New York, NY", "New York City", "NYC"], "check_in": ["2022-05-01", "05/01/2022", "May 1, 2022"], "check_out": ["2022-05-05", "05/05/2022", "May 5, 2022"], "adults": [2], "children": [1]}}, {"hotel_booking.book": {"hotel_name": ["Marriott"], "location": ["Los Angeles", "Los Angeles, CA", "LA"], "check_in": ["2022-06-01", "06/01/2022", "June 1, 2022"], "check_out": ["2022-06-10", "06/10/2022", "June 10, 2022"], "adults": [1], "children": [2]}}]}
{"id": "parallel_58", "ground_truth": [{"get_exchange_rate": {"base_currency": ["USD"], "target_currency": ["AUD"]}}, {"get_exchange_rate": {"base_currency": ["USD"], "target_currency": ["CAD"]}}]}
{"id": "parallel_59", "ground_truth": [{"get_conversion_cost": {"amount": [15000], "from_currency": ["Euro", "EUR"], "to_currency": ["dollars", "USD", "Dollar"]}}, {"get_conversion_cost": {"amount": [200], "from_currency": ["pounds", "GBP"], "to_currency": ["dollars", "USD"]}}]}
{"id": "parallel_60", "ground_truth": [{"math.factorial": {"number": [5]}}, {"math.factorial": {"number": [7]}}, {"math.factorial": {"number": [9]}}]}
{"id": "parallel_61", "ground_truth": [{"math.hypot": {"x": [3], "y": [4], "z": ["", 0]}}, {"math.hypot": {"x": [6], "y": [8], "z": ["", 0]}}, {"math.hypot": {"x": [9], "y": [12], "z": [15]}}]}
{"id": "parallel_62", "ground_truth": [{"algebra.quadratic_roots": {"a": [3], "b": [4], "c": [2]}}, {"algebra.quadratic_roots": {"a": [5], "b": [-7], "c": [3]}}]}
{"id": "parallel_63", "ground_truth": [{"solve_quadratic_equation": {"a": [5], "b": [6], "c": [1]}}, {"solve_quadratic_equation": {"a": [3], "b": [2], "c": [1]}}]}
{"id": "parallel_64", "ground_truth": [{"solve_quadratic": {"a": [2], "b": [5], "c": [3], "root_type": ["all", ""]}}, {"solve_quadratic": {"a": [1], "b": [-3], "c": [2], "root_type": ["real"]}}, {"solve_quadratic": {"a": [4], "b": [-7], "c": [3], "root_type": ["all", ""]}}, {"solve_quadratic": {"a": [1], "b": [2], "c": [1], "root_type": ["real"]}}]}
{"id": "parallel_65", "ground_truth": [{"calculate_circumference": {"radius": [5], "unit": ["cm", "centimeter"]}}, {"calculate_circumference": {"radius": [10], "unit": ["cm", "centimeter", ""]}}, {"calculate_circumference": {"radius": [15], "unit": ["cm", "centimeter", ""]}}, {"calculate_circumference": {"radius": [20], "unit": ["cm", "centimeter", ""]}}]}
{"id": "parallel_66", "ground_truth": [{"geometry.area_circle": {"radius": [5], "units": ["meters", "m", ""]}}, {"geometry.area_circle": {"radius": [10], "units": ["meters", "m", ""]}}, {"geometry.area_circle": {"radius": [15], "units": ["meters", "m", ""]}}]}
{"id": "parallel_67", "ground_truth": [{"geometry.calculate_area_circle": {"radius": [5], "unit": ["meters", "m"]}}, {"geometry.calculate_area_circle": {"radius": [10], "unit": ["meters", "m"]}}]}
{"id": "parallel_68", "ground_truth": [{"calculate_area": {"base": [12], "height": [15], "unit": ["m", "meters", "meter"]}}, {"calculate_area": {"base": [18], "height": [24], "unit": ["m", "meters", "meter"]}}]}
{"id": "parallel_69", "ground_truth": [{"calculate_triangle_area": {"base": [10], "height": [5]}}, {"calculate_triangle_area": {"base": [8], "height": [6]}}]}
{"id": "parallel_70", "ground_truth": [{"geometry.circumference": {"radius": [5], "units": ["m", "meters"]}}, {"geometry.circumference": {"radius": [10], "units": ["m", "meters", ""]}}, {"geometry.circumference": {"radius": [15], "units": ["m", "meters", ""]}}, {"geometry.circumference": {"radius": [20], "units": ["m", "meters", ""]}}]}
{"id": "parallel_71", "ground_truth": [{"calculate_derivative": {"function": ["3x**3 - 2x**2 + 5x - 7", "lambda x: 3x**3 - 2x**2 + 5x - 7"], "x_value": [4]}}, {"calculate_derivative": {"function": ["9x**2 - 4x + 5", "lambda x: 9x**2 - 4x + 5"], "x_value": [2]}}]}
{"id": "parallel_72", "ground_truth": [{"integrate": {"function": ["x**3", "lambda x: x**3"], "start_x": [2], "end_x": [5], "method": ["trapezoid", ""]}}, {"integrate": {"function": ["x**3", "lambda x: x**3"], "start_x": [2], "end_x": [5], "method": ["simpson"]}}, {"integrate": {"function": ["2x**2 + 3x - 1", "lambda x: 2x**2 + 3x - 1"], "start_x": [-1], "end_x": [3], "method": ["trapezoid", ""]}}, {"integrate": {"function": ["2x**2 + 3x - 1", "lambda x: 2x**2 + 3x - 1"], "start_x": [-1], "end_x": [3], "method": ["simpson"]}}]}
{"id": "parallel_73", "ground_truth": [{"calculus.derivative": {"function": ["3x**2 + 2x - 1", "lambda x: 3x**2 + 2x - 1"], "value": [5], "function_variable": ["x", ""]}}, {"calculus.derivative": {"function": ["4y**3 - 3y**2 + 2y - 1", "lambda y: 4y**3 - 3y**2 + 2y - 1"], "value": [3], "function_variable": ["y"]}}]}
{"id": "parallel_74", "ground_truth": [{"get_prime_factors": {"number": [4567], "formatted": [true]}}, {"get_prime_factors": {"number": [4567], "formatted": [false]}}, {"get_prime_factors": {"number": [7890], "formatted": [true]}}, {"get_prime_factors": {"number": [7890], "formatted": [false]}}]}
{"id": "parallel_75", "ground_truth": [{"number_analysis.prime_factors": {"number": [45]}}, {"number_analysis.prime_factors": {"number": [100]}}, {"number_analysis.prime_factors": {"number": [150]}}]}
{"id": "parallel_76", "ground_truth": [{"math.gcd": {"num1": [45], "num2": [60]}}, {"math.gcd": {"num1": [81], "num2": [27]}}]}
{"id": "parallel_77", "ground_truth": [{"math.hcf": {"number1": [45], "number2": [60]}}, {"math.hcf": {"number1": [90], "number2": [120]}}, {"math.hcf": {"number1": [36], "number2": [48]}}, {"math.hcf": {"number1": [72], "number2": [96]}}]}
{"id": "parallel_78", "ground_truth": [{"number_theory.gcd": {"number1": [45], "number2": [60]}}, {"number_theory.gcd": {"number1": [81], "number2": [63]}}]}
{"id": "parallel_79", "ground_truth": [{"prime_factorize": {"number": [4567], "return_type": ["dictionary"]}}, {"prime_factorize": {"number": [7890], "return_type": ["dictionary"]}}]}
{"id": "parallel_80", "ground_truth": [{"math.gcd": {"num1": [36], "num2": [48]}}, {"math.gcd": {"num1": [60], "num2": [96]}}]}
{"id": "parallel_81", "ground_truth": [{"calculate_final_velocity": {"height": [10], "initial_velocity": [0], "gravity": [9.81, ""]}}, {"calculate_final_velocity": {"height": [20], "initial_velocity": [0], "gravity": [9.81, ""]}}, {"calculate_final_velocity": {"height": [15], "initial_velocity": [0], "gravity": [9.81, ""]}}, {"calculate_final_velocity": {"height": [25], "initial_velocity": [0], "gravity": [9.81, ""]}}]}
{"id": "parallel_82", "ground_truth": [{"calculate_velocity": {"distance": [120], "duration": [5], "unit": ["km/h", ""]}}, {"calculate_velocity": {"distance": [150], "duration": [6], "unit": ["km/h", ""]}}]}
{"id": "parallel_83", "ground_truth": [{"final_velocity": {"initial_velocity": [0], "acceleration": [5], "time": [10]}}, {"final_velocity": {"initial_velocity": [10], "acceleration": [7], "time": [8]}}, {"final_velocity": {"initial_velocity": [20], "acceleration": [4], "time": [12]}}]}
{"id": "parallel_84", "ground_truth": [{"calculate_displacement": {"initial_velocity": [15], "time": [7], "acceleration": [3.5]}}, {"calculate_displacement": {"initial_velocity": [20], "time": [10], "acceleration": [2.0]}}, {"calculate_displacement": {"initial_velocity": [25], "time": [8], "acceleration": [0]}}]}
{"id": "parallel_85", "ground_truth": [{"calculate_final_speed": {"initial_speed": [0], "time": [10], "gravity": [-9.81, ""]}}, {"calculate_final_speed": {"initial_speed": [5], "time": [7], "gravity": [-9.81, ""]}}]}
{"id": "parallel_86", "ground_truth": [{"kinematics.final_velocity_from_distance": {"acceleration": [5], "distance": [100], "initial_velocity": ["", 0]}}, {"kinematics.final_velocity_from_distance": {"acceleration": [10], "distance": [200], "initial_velocity": ["", 0]}}]}
{"id": "parallel_87", "ground_truth": [{"calculate_final_velocity": {"initial_velocity": [0], "acceleration": [6], "time": [10]}}, {"calculate_final_velocity": {"initial_velocity": [20], "acceleration": [4], "time": [15]}}]}
{"id": "parallel_88", "ground_truth": [{"calculate_final_speed": {"initial_velocity": [0, ""], "height": [10], "gravity": [9.8, ""]}}, {"calculate_final_speed": {"initial_velocity": [5], "height": [20], "gravity": [9.8, ""]}}]}
{"id": "parallel_89", "ground_truth": [{"get_directions": {"start_location": ["San Francisco", "SF"], "end_location": ["Palo Alto"], "route_type": ["fastest"]}}, {"get_directions": {"start_location": ["Palo Alto"], "end_location": ["Golden Gate Bridge in San Francisco", "Golden Gate Bridge, San Francisco", "Golden Gate Bridge"], "route_type": ["scenic"]}}, {"get_directions": {"start_location": ["Golden Gate Bridge in San Francisco", "Golden Gate Bridge, San Francisco", "Golden Gate Bridge"], "end_location": ["San Francisco", "SF"], "route_type": ["fastest"]}}]}
{"id": "parallel_90", "ground_truth": [{"travel_itinerary_generator": {"destination": ["Tokyo"], "days": [7], "daily_budget": [200], "exploration_type": ["urban", ""]}}, {"travel_itinerary_generator": {"destination": ["Paris"], "days": [10], "daily_budget": [150], "exploration_type": ["history"]}}, {"travel_itinerary_generator": {"destination": ["Sydney"], "days": [5], "daily_budget": [100], "exploration_type": ["nature"]}}, {"travel_itinerary_generator": {"destination": ["Rome"], "days": [12], "daily_budget": [180], "exploration_type": ["culture"]}}]}
{"id": "parallel_91", "ground_truth": [{"vegan_restaurant.find_nearby": {"location": ["Los Angeles, CA", "Los Angeles", "LA, CA"], "operating_hours": [22]}}, {"vegan_restaurant.find_nearby": {"location": ["San Francisco, CA", "San Francisco", "SF, CA"], "operating_hours": [22]}}, {"vegan_restaurant.find_nearby": {"location": ["Seattle, WA", "Seattle", "WA"], "operating_hours": [22]}}]}
{"id": "parallel_92", "ground_truth": [{"get_shortest_driving_distance": {"origin": ["New York City", "NYC"], "destination": ["Los Angeles", "Los Angeles, CA", "LA"], "unit": ["miles", "mile"]}}, {"get_shortest_driving_distance": {"origin": ["Los Angeles", "Los Angeles, CA", "LA"], "destination": ["Miami"], "unit": ["miles", "mile"]}}, {"get_shortest_driving_distance": {"origin": ["Miami"], "destination": ["New York City", "NYC"], "unit": ["miles", "mile"]}}]}
{"id": "parallel_93", "ground_truth": [{"route.estimate_time": {"start_location": ["New York", "New York, NY", "NYC"], "end_location": ["Miami"], "stops": [["Philadelphia", "Washington D.C.", "Atlanta"], ["Philadelphia", "Washington D.C.", "Atlanta"], ["Philadelphia", "Washington D.C.", "Atlanta"], ["Atlanta", "Philadelphia", "Washington D.C."], ["Atlanta", "Philadelphia", "Washington D.C."], ["Atlanta", "Philadelphia", "Washington D.C."], ["Washington D.C.", "Philadelphia", "Atlanta"], ["Washington D.C.", "Philadelphia", "Atlanta"], ["Washington D.C.", "Philadelphia", "Atlanta"]]}}, {"route.estimate_time": {"start_location": ["New York", "New York, NY", "NYC"], "end_location": ["Miami"], "stops": [["Washington D.C."], ["Philadelphia", "Washington D.C."], ["Philadelphia", "Washington D.C.", "New York"], ["Philadelphia", "Washington D.C.", "NYC"], ["Washington D.C.", "Philadelphia"], ["Washington D.C.", "Philadelphia", "New York"], ["Washington D.C.", "Philadelphia", "NYC"]]}}, {"route.estimate_time": {"start_location": ["Philadelphia"], "end_location": ["Miami"], "stops": [["Washington D.C."], ["Washington D.C.", "Philadelphia"]]}}]}
{"id": "parallel_94", "ground_truth": [{"calculate_electric_field": {"charge": [5], "distance": [2], "permitivity": ["", 0]}}, {"calculate_electric_field": {"charge": [3], "distance": [4], "permitivity": ["", 0]}}]}
{"id": "parallel_95", "ground_truth": [{"calculate_magnetic_field": {"current": [10], "radius": [0.5], "permeability": ["", 0]}}, {"calculate_magnetic_field": {"current": [15], "radius": [1.0], "permeability": ["", 0]}}]}
{"id": "parallel_96", "ground_truth": [{"electromagnetic_force": {"charge1": [5], "charge2": [10], "distance": [2], "medium_permittivity": [8.854e-12, ""]}}, {"electromagnetic_force": {"charge1": [5], "charge2": [10], "distance": [2], "medium_permittivity": [5e-12, ""]}}]}
{"id": "parallel_97", "ground_truth": [{"calculate_resonant_frequency": {"inductance": [0.005], "capacitance": [1e-07], "round_off": [3]}}, {"calculate_resonant_frequency": {"inductance": [0.007], "capacitance": [2e-07], "round_off": [4]}}]}
{"id": "parallel_98", "ground_truth": [{"calculate_electric_field_strength": {"charge": [2], "distance": [0.5], "medium": ["vacuum", ""]}}, {"calculate_electric_field_strength": {"charge": [2], "distance": [1.0], "medium": ["vacuum", ""]}}, {"calculate_electric_field_strength": {"charge": [2], "distance": [2.0], "medium": ["vacuum", ""]}}, {"calculate_electric_field_strength": {"charge": [2], "distance": [1.0], "medium": ["air"]}}]}
{"id": "parallel_99", "ground_truth": [{"thermo.calculate_energy": {"mass": [500], "phase_transition": ["melting"], "substance": ["water", ""]}}, {"thermo.calculate_energy": {"mass": [500], "phase_transition": ["freezing"], "substance": ["water", ""]}}, {"thermo.calculate_energy": {"mass": [500], "phase_transition": ["vaporization"], "substance": ["water", ""]}}, {"thermo.calculate_energy": {"mass": [500], "phase_transition": ["condensation"], "substance": ["water", ""]}}]}
{"id": "parallel_100", "ground_truth": [{"get_boiling_melting_points": {"substance": ["water"], "sea_level": [0]}}, {"get_boiling_melting_points": {"substance": ["iron"], "sea_level": [1000]}}, {"get_boiling_melting_points": {"substance": ["water"], "sea_level": [1000]}}, {"get_boiling_melting_points": {"substance": ["iron"], "sea_level": [0]}}]}
{"id": "parallel_101", "ground_truth": [{"calculate_density": {"mass": [10], "volume": [2], "unit": ["", "kg/m\u00b3"]}}, {"calculate_density": {"mass": [15], "volume": [3], "unit": ["", "kg/m\u00b3"]}}]}
{"id": "parallel_102", "ground_truth": [{"calc_absolute_pressure": {"gauge_pressure": [2.5], "atm_pressure": [1.0, ""]}}, {"calc_absolute_pressure": {"gauge_pressure": [2.5], "atm_pressure": [0.85]}}]}
{"id": "parallel_103", "ground_truth": [{"entropy_change.calculate": {"substance": ["A"], "mass": [2], "initial_temperature": [25], "final_temperature": [75], "pressure": [1, ""]}}, {"entropy_change.calculate": {"substance": ["A"], "mass": [2], "initial_temperature": [10], "final_temperature": [50], "pressure": [1, ""]}}]}
{"id": "parallel_104", "ground_truth": [{"calculate_entropy_change": {"initial_temp": [300], "final_temp": [350], "heat_capacity": [4.18], "isothermal": [true, ""]}}, {"calculate_entropy_change": {"initial_temp": [300], "final_temp": [350], "heat_capacity": [4.18], "isothermal": [false]}}]}
{"id": "parallel_105", "ground_truth": [{"calc_heat_capacity": {"temp": [300], "volume": [2.5], "gas": ["air", ""]}}, {"calc_heat_capacity": {"temp": [350], "volume": [2.5], "gas": ["air", ""]}}, {"calc_heat_capacity": {"temp": [300], "volume": [1.5], "gas": ["air", ""]}}]}
{"id": "parallel_106", "ground_truth": [{"fetch_DNA_sequence": {"DNA_id": ["XYZ123"], "format": ["", "fasta"], "upstream": ["", 0]}}, {"fetch_DNA_sequence": {"DNA_id": ["XYZ123"], "format": ["genbank"], "upstream": [0, ""]}}, {"fetch_DNA_sequence": {"DNA_id": ["XYZ123"], "format": ["", "fasta"], "upstream": [500]}}]}
{"id": "parallel_107", "ground_truth": [{"get_protein_sequence": {"gene": ["BRCA1"], "species": ["Homo sapiens", ""]}}, {"get_protein_sequence": {"gene": ["BRCA2"], "species": ["Homo sapiens", ""]}}, {"get_protein_sequence": {"gene": ["BRCA1"], "species": ["Pan troglodytes"]}}, {"get_protein_sequence": {"gene": ["BRCA2"], "species": ["Pan troglodytes"]}}]}
{"id": "parallel_108", "ground_truth": [{"biology.get_cell_info": {"cell_type": ["neuron"], "detailed": [true]}}, {"biology.get_cell_info": {"cell_type": ["muscle"], "detailed": [false, ""]}}]}
{"id": "parallel_109", "ground_truth": [{"cellbio.get_proteins": {"cell_compartment": ["nucleus"], "include_description": [true]}}, {"cellbio.get_proteins": {"cell_compartment": ["mitochondria"], "include_description": [true]}}, {"cellbio.get_proteins": {"cell_compartment": ["cytoplasm"], "include_description": [true]}}]}
{"id": "parallel_110", "ground_truth": [{"cell_biology.function_lookup": {"molecule": ["ATP"], "organelle": ["mitochondria"], "specific_function": [true]}}, {"cell_biology.function_lookup": {"molecule": ["DNA"], "organelle": ["nucleus"], "specific_function": [true]}}]}
{"id": "parallel_111", "ground_truth": [{"calculate_molecular_weight": {"compound": ["C6H12O6"], "to_unit": ["grams/mole", "g/mol"]}}, {"calculate_molecular_weight": {"compound": ["C12H22O11"], "to_unit": ["grams/mole", "g/mol"]}}]}
{"id": "parallel_112", "ground_truth": [{"mutation_type.find": {"snp_id": ["rs123456"], "species": ["Homo sapiens", "Humans", ""]}}, {"mutation_type.find": {"snp_id": ["rs7891011"], "species": ["Canis lupus familiaris", "Dog"]}}]}
{"id": "parallel_113", "ground_truth": [{"diabetes_prediction": {"weight": [180], "height": [70], "activity_level": ["lightly active"]}}, {"diabetes_prediction": {"weight": [200], "height": [65], "activity_level": ["very active"]}}, {"diabetes_prediction": {"weight": [150], "height": [72], "activity_level": ["moderately active"]}}, {"diabetes_prediction": {"weight": [220], "height": [68], "activity_level": ["extra active"]}}]}
{"id": "parallel_114", "ground_truth": [{"analyze_dna_sequence": {"sequence": ["AGCTTAGCTA"], "reference_sequence": ["AGCTTAGCTA"], "mutation_type": ["insertion", ""]}}, {"analyze_dna_sequence": {"sequence": ["AGCTTAGGCTA"], "reference_sequence": ["AGCTTAGCTA"], "mutation_type": ["insertion", ""]}}, {"analyze_dna_sequence": {"sequence": ["AGCTTAGCTA"], "reference_sequence": ["AGCTTAGCTA"], "mutation_type": ["deletion"]}}, {"analyze_dna_sequence": {"sequence": ["AGCTTAGGCTA"], "reference_sequence": ["AGCTTAGCTA"], "mutation_type": ["deletion"]}}, {"analyze_dna_sequence": {"sequence": ["AGCTTAGCTA"], "reference_sequence": ["AGCTTAGCTA"], "mutation_type": ["substitution"]}}, {"analyze_dna_sequence": {"sequence": ["AGCTTAGGCTA"], "reference_sequence": ["AGCTTAGCTA"], "mutation_type": ["substitution"]}}]}
{"id": "parallel_115", "ground_truth": [{"genetics.calculate_similarity": {"species1": ["human", "Human"], "species2": ["chimpanzee"], "format": ["percentage", ""]}}, {"genetics.calculate_similarity": {"species1": ["human"], "species2": ["chimpanzee"], "format": ["fraction"]}}, {"genetics.calculate_similarity": {"species1": ["human"], "species2": ["gorilla"], "format": ["percentage", ""]}}, {"genetics.calculate_similarity": {"species1": ["human"], "species2": ["gorilla"], "format": ["fraction"]}}]}
{"id": "parallel_116", "ground_truth": [{"calculate_genotype_frequency": {"allele_frequency": [0.7], "genotype": ["AA"]}}, {"calculate_genotype_frequency": {"allele_frequency": [0.7], "genotype": ["Aa"]}}, {"calculate_genotype_frequency": {"allele_frequency": [0.7], "genotype": ["aa"]}}]}
{"id": "parallel_117", "ground_truth": [{"calculate_density": {"country": ["China"], "year": ["2000"], "population": [1267000000.0], "land_area": [9597000.0]}}, {"calculate_density": {"country": ["China"], "year": ["2010"], "population": [1341000000.0], "land_area": [9597000.0]}}]}
{"id": "parallel_118", "ground_truth": [{"ecology_data.precipitation_stats": {"location": ["Amazon rainforest"], "time_frame": ["six_months"]}}, {"ecology_data.precipitation_stats": {"location": ["Amazon rainforest"], "time_frame": ["year"]}}, {"ecology_data.precipitation_stats": {"location": ["Amazon rainforest"], "time_frame": ["five_years"]}}]}
{"id": "parallel_119", "ground_truth": [{"identify_bird": {"color": ["blue"], "habitat": ["forest"], "size": ["small", ""]}}, {"identify_bird": {"color": ["black"], "habitat": ["lake"], "size": ["large"]}}, {"identify_bird": {"color": ["brown"], "habitat": ["desert"], "size": ["medium"]}}, {"identify_bird": {"color": ["green"], "habitat": ["tropical rainforest"], "size": ["large"]}}]}
{"id": "parallel_120", "ground_truth": [{"forest_growth_forecast": {"location": ["Amazon Rainforest"], "years": [10], "include_human_impact": [false, ""]}}, {"forest_growth_forecast": {"location": ["Boreal Forests of Canada"], "years": [20], "include_human_impact": [false, ""]}}]}
{"id": "parallel_121", "ground_truth": [{"ecology.get_turtle_population": {"location": ["Galapagos Islands"], "year": [2015], "species": [true]}}, {"ecology.get_turtle_population": {"location": ["Galapagos Islands"], "year": [2020], "species": [true]}}]}
{"id": "parallel_122", "ground_truth": [{"calculate_vehicle_emission": {"vehicle_type": ["gas"], "miles_driven": [15000], "emission_factor": ["", 1.4]}}, {"calculate_vehicle_emission": {"vehicle_type": ["diesel"], "miles_driven": [15000], "emission_factor": [2.7]}}, {"calculate_vehicle_emission": {"vehicle_type": ["EV"], "miles_driven": [15000], "emission_factor": [0]}}]}
{"id": "parallel_123", "ground_truth": [{"generate_DNA_sequence": {"length": [500], "preferences": [["A"]]}}, {"generate_DNA_sequence": {"length": [500], "preferences": [["T"]]}}, {"generate_DNA_sequence": {"length": [500], "preferences": [["C"]]}}, {"generate_DNA_sequence": {"length": [500], "preferences": [["G"]]}}]}
{"id": "parallel_124", "ground_truth": [{"population_projections": {"country": ["Japan"], "years": [10], "growth_rate": ["", 0.01]}}, {"population_projections": {"country": ["Japan"], "years": [10], "growth_rate": [0.015]}}, {"population_projections": {"country": ["India"], "years": [20], "growth_rate": [0.021]}}, {"population_projections": {"country": ["India"], "years": [20], "growth_rate": ["", 0.01]}}]}
{"id": "parallel_125", "ground_truth": [{"elephant_population_estimate": {"current_population": [500], "growth_rate": [0.02], "years": [10]}}, {"elephant_population_estimate": {"current_population": [500], "growth_rate": [0.015], "years": [10]}}, {"elephant_population_estimate": {"current_population": [500], "growth_rate": [0.025], "years": [10]}}]}
{"id": "parallel_126", "ground_truth": [{"prediction.evolution": {"species": ["African Elephant"], "years": [5000], "model": ["Darwin", ""]}}, {"prediction.evolution": {"species": ["African Elephant"], "years": [5000], "model": ["Lamarck"]}}]}
{"id": "parallel_127", "ground_truth": [{"restaurant.find_nearby": {"location": ["New York, NY", "New York City", "NYC", "NY"], "dietary_preference": [["Vegan", "Gluten-free", "Dairy-free"]]}}, {"restaurant.find_nearby": {"location": ["Los Angeles, CA", "LA", "Los Angeles", "Los Angeles, CA", "CA"], "dietary_preference": [["Vegan", "Gluten-free", "Dairy-free"]]}}, {"restaurant.find_nearby": {"location": ["Chicago, IL", "Chicago", "IL"], "dietary_preference": [["Vegan", "Gluten-free", "Dairy-free"]]}}]}
{"id": "parallel_128", "ground_truth": [{"average_temperature": {"location": ["New York", "New York, NY", "NYC"], "days": [7], "temp_unit": ["Fahrenheit", ""]}}, {"average_temperature": {"location": ["Los Angeles", "Los Angeles, CA", "LA"], "days": [7], "temp_unit": ["Celsius"]}}]}
{"id": "parallel_129", "ground_truth": [{"create_histogram": {"data": [[12, 15, 11, 14, 18, 19, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26]], "bins": [5]}}, {"create_histogram": {"data": [[32, 35, 31, 34, 38, 39, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46]], "bins": [5]}}]}
{"id": "parallel_130", "ground_truth": [{"find_restaurants": {"location": ["New York", "New York, NY", "NYC"], "food_type": ["Italian", "italian"], "number": [4], "dietary_requirements": [["vegan", "gluten-free"]]}}, {"find_restaurants": {"location": ["Los Angeles", "Los Angeles, CA", "LA"], "food_type": ["Italian"], "number": [4], "dietary_requirements": [["vegan", "gluten-free"]]}}]}
{"id": "parallel_131", "ground_truth": [{"map_routing.fastest_route": {"start_location": ["San Francisco", "SF"], "end_location": ["Palo Alto"], "avoid_tolls": [true]}}, {"map_routing.fastest_route": {"start_location": ["Palo Alto"], "end_location": ["San Jose", "SJ"], "avoid_tolls": [true]}}, {"map_routing.fastest_route": {"start_location": ["San Jose", "SJ"], "end_location": ["San Francisco", "SF"], "avoid_tolls": [true]}}]}
{"id": "parallel_132", "ground_truth": [{"calculate_average": {"numbers": [[23, 45, 67, 89]]}}, {"calculate_average": {"numbers": [[12, 34, 56, 78]]}}, {"calculate_average": {"numbers": [[98, 76, 54, 32]]}}, {"calculate_average": {"numbers": [[87, 65, 43, 21]]}}]}
{"id": "parallel_133", "ground_truth": [{"calculate_distance": {"coord1": [[48.8584, 2.2945]], "coord2": [[41.8902, 12.4922]], "unit": ["kilometers", "km"]}}, {"calculate_distance": {"coord1": [[41.8902, 12.4922]], "coord2": [[37.9715, 23.7257]], "unit": ["kilometers", "km"]}}, {"calculate_distance": {"coord1": [[37.9715, 23.7257]], "coord2": [[29.9792, 31.1342]], "unit": ["kilometers", "km"]}}]}
{"id": "parallel_134", "ground_truth": [{"calculate_bmi": {"weight": [85], "height": [175], "unit": ["metric", ""]}}, {"calculate_bmi": {"weight": [60], "height": [160], "unit": ["metric", ""]}}, {"calculate_bmi": {"weight": [75], "height": [180], "unit": ["metric", ""]}}, {"calculate_bmi": {"weight": [90], "height": [185], "unit": ["metric", ""]}}]}
{"id": "parallel_135", "ground_truth": [{"geo_distance.calculate": {"start_location": ["New York", "New York, NY", "New York, NY", "NYC"], "end_location": ["Los Angeles", "Los Angeles, CA", "LA"], "units": ["kilometers", ""]}}, {"geo_distance.calculate": {"start_location": ["Los Angeles", "Los Angeles, CA", "LA"], "end_location": ["Miami"], "units": ["kilometers", ""]}}, {"geo_distance.calculate": {"start_location": ["Miami"], "end_location": ["New York", "New York, NY", "NYC"], "units": ["kilometers", ""]}}]}
{"id": "parallel_136", "ground_truth": [{"city_distance.find_shortest": {"start_city": ["New York", "New York, NY", "NYC"], "end_city": ["Los Angeles", "Los Angeles, CA", "LA"], "transportation": ["bus", ""], "allow_transfer": ["", false]}}, {"city_distance.find_shortest": {"start_city": ["New York", "New York, NY", "NYC"], "end_city": ["Los Angeles", "Los Angeles, CA", "LA"], "transportation": ["bus", ""], "allow_transfer": [true]}}]}
{"id": "parallel_137", "ground_truth": [{"array_sort": {"list": [[45, 12, 67, 21, 89]], "order": ["ascending"]}}, {"array_sort": {"list": [[45, 12, 67, 21, 89]], "order": ["descending"]}}, {"array_sort": {"list": [[34, 78, 12, 56, 90]], "order": ["ascending"]}}, {"array_sort": {"list": [[34, 78, 12, 56, 90]], "order": ["descending"]}}, {"array_sort": {"list": [[23, 45, 67, 89, 12]], "order": ["ascending"]}}, {"array_sort": {"list": [[23, 45, 67, 89, 12]], "order": ["descending"]}}, {"array_sort": {"list": [[56, 78, 90, 12, 34]], "order": ["ascending"]}}, {"array_sort": {"list": [[56, 78, 90, 12, 34]], "order": ["descending"]}}]}
{"id": "parallel_138", "ground_truth": [{"calculate_BMI": {"weight_kg": [85], "height_m": [1.8]}}, {"calculate_BMI": {"weight_kg": [60], "height_m": [1.65]}}, {"calculate_BMI": {"weight_kg": [75], "height_m": [1.7]}}]}
{"id": "parallel_139", "ground_truth": [{"employee.fetch_data": {"company_name": ["Tech Solutions"], "employee_id": [12345], "data_field": [["Personal Info", "Job History", "Payroll", "Attendance"]]}}, {"employee.fetch_data": {"company_name": ["Tech Solutions"], "employee_id": [67890], "data_field": [["Personal Info", "Job History", "Payroll", "Attendance"]]}}]}
{"id": "parallel_140", "ground_truth": [{"imdb.find_movies_by_actor": {"actor_name": ["Leonardo DiCaprio"], "year": [2010], "category": ["Drama", ""]}}, {"imdb.find_movies_by_actor": {"actor_name": ["Leonardo DiCaprio"], "year": [2012], "category": ["Comedy"]}}]}
{"id": "parallel_141", "ground_truth": [{"get_theater_movie_releases": {"location": ["New York", "New York, NY", "NYC"], "timeframe": [7], "format": ["IMAX", ""]}}, {"get_theater_movie_releases": {"location": ["Los Angeles", "Los Angeles, CA", "LA"], "timeframe": [14], "format": ["2D"]}}]}
{"id": "parallel_142", "ground_truth": [{"update_user_info": {"user_id": [12345], "update_info": [{"name": ["John"], "email": ["example@.com"]}], "database": ["CustomerInfo", ""]}}, {"update_user_info": {"user_id": [67890], "update_info": [{"name": ["John"], "email": ["example@.com"]}], "database": ["CustomerInfo", ""]}}]}
{"id": "parallel_143", "ground_truth": [{"calc_area_triangle": {"base": [10], "height": [5]}}, {"calc_area_triangle": {"base": [15], "height": [7]}}, {"calc_area_triangle": {"base": [20], "height": [10]}}]}
{"id": "parallel_144", "ground_truth": [{"math.factorial": {"number": [5]}}, {"math.factorial": {"number": [3]}}, {"math.factorial": {"number": [4]}}, {"math.factorial": {"number": [2]}}]}
{"id": "parallel_145", "ground_truth": [{"calculate_clock_angle": {"hours": [3], "minutes": [15], "round_to": [2, ""]}}, {"calculate_clock_angle": {"hours": [8], "minutes": [20], "round_to": [2, ""]}}, {"calculate_clock_angle": {"hours": [11], "minutes": [50], "round_to": [2, ""]}}]}
{"id": "parallel_146", "ground_truth": [{"plot_sine_wave": {"start_range": [0], "end_range": [10], "frequency": [5], "amplitude": [2], "phase_shift": [1]}}, {"plot_sine_wave": {"start_range": [0], "end_range": [20], "frequency": [10], "amplitude": [3], "phase_shift": [2]}}]}
{"id": "parallel_147", "ground_truth": [{"light_travel_time": {"distance_in_light_years": [4.22], "speed_of_light": [*********, ""]}}, {"light_travel_time": {"distance_in_light_years": [6.1], "speed_of_light": [*********, ""]}}, {"light_travel_time": {"distance_in_light_years": [5.88], "speed_of_light": [*********, ""]}}]}
{"id": "parallel_148", "ground_truth": [{"calculate_speed": {"distance": [500], "time": [25], "to_unit": ["km/h"]}}, {"calculate_speed": {"distance": [1000], "time": [200], "to_unit": ["m/s", ""]}}, {"calculate_speed": {"distance": [10000], "time": [600], "to_unit": ["km/h"]}}]}
{"id": "parallel_149", "ground_truth": [{"calculate_distance": {"body1": ["Mars"], "body2": ["Venus"], "unit": ["miles"]}}, {"calculate_distance": {"body1": ["Mars"], "body2": ["Jupiter"], "unit": ["miles"]}}]}
{"id": "parallel_150", "ground_truth": [{"mathematics.calculate_area_under_curve": {"polynomial": [[3, -2, 1]], "limits": [[-1, 2]]}}, {"mathematics.calculate_area_under_curve": {"polynomial": [[1, 0, -1]], "limits": [[0, 3]]}}]}
{"id": "parallel_151", "ground_truth": [{"geometry.area_triangle": {"base": [15], "height": [20], "unit": ["square meters", "m^2", ""]}}, {"geometry.area_triangle": {"base": [25], "height": [30], "unit": ["square feet", "ft^2"]}}, {"geometry.area_triangle": {"base": [35], "height": [40], "unit": ["square inches", "in^2"]}}]}
{"id": "parallel_152", "ground_truth": [{"math.power": {"base": [2], "exponent": [3], "mod": ["", null]}}, {"math.power": {"base": [3], "exponent": [5], "mod": ["", null]}}]}
{"id": "parallel_153", "ground_truth": [{"train_random_forest_classifier": {"dataset": ["dataset1"], "max_depth": [10], "n_estimators": [100]}}, {"train_random_forest_classifier": {"dataset": ["dataset2"], "max_depth": [20], "n_estimators": [200]}}]}
{"id": "parallel_154", "ground_truth": [{"calculate_bmi": {"weight": [75], "height": [180], "system": ["metric", ""]}}, {"calculate_bmi": {"weight": [60], "height": [165], "system": ["metric", ""]}}, {"calculate_bmi": {"weight": [80], "height": [175], "system": ["metric", ""]}}, {"calculate_bmi": {"weight": [90], "height": [185], "system": ["metric", ""]}}]}
{"id": "parallel_155", "ground_truth": [{"run_linear_regression": {"predictors": [["Age", "Income", "Education"]], "target": ["Spending Score"], "standardize": [false]}}, {"run_linear_regression": {"predictors": [["Age", "Income", "Education"]], "target": ["Spending Score"], "standardize": [true, false]}}]}
{"id": "parallel_156", "ground_truth": [{"random_forest.train": {"n_estimators": [100], "max_depth": [10], "data": ["data_random_forest"]}}, {"random_forest.train": {"n_estimators": [200], "max_depth": [20], "data": ["data_random_forest"]}}, {"random_forest.train": {"n_estimators": [300], "max_depth": [30], "data": ["data_random_forest"]}}, {"random_forest.train": {"n_estimators": [400], "max_depth": [40], "data": ["data_random_forest"]}}]}
{"id": "parallel_157", "ground_truth": [{"predict_house_price": {"bedrooms": [3], "bathrooms": [2], "area": [1500], "location": ["New York", "New York, NY", "New York City", "NYC"]}}, {"predict_house_price": {"bedrooms": [4], "bathrooms": [3], "area": [2000], "location": ["Los Angeles", "Los Angeles, CA", "LA"]}}, {"predict_house_price": {"bedrooms": [2], "bathrooms": [1], "area": [1200], "location": ["Chicago"]}}, {"predict_house_price": {"bedrooms": [3], "bathrooms": [2], "area": [1800], "location": ["Miami"]}}]}
{"id": "parallel_158", "ground_truth": [{"random.normalvariate": {"mu": [5], "sigma": [2]}}, {"random.normalvariate": {"mu": [10], "sigma": [3]}}]}
{"id": "parallel_159", "ground_truth": [{"probability.dice_roll": {"desired_number": [4], "number_of_rolls": [3], "die_sides": [6, ""]}}, {"probability.dice_roll": {"desired_number": [2], "number_of_rolls": [2], "die_sides": [6, ""]}}, {"probability.dice_roll": {"desired_number": [7], "number_of_rolls": [2], "die_sides": [8]}}]}
{"id": "parallel_160", "ground_truth": [{"prob_dist.binomial": {"trials": [20], "successes": [5], "p": [0.3]}}, {"prob_dist.binomial": {"trials": [50], "successes": [15], "p": [0.3]}}, {"prob_dist.binomial": {"trials": [100], "successes": [30], "p": [0.3]}}]}
{"id": "parallel_161", "ground_truth": [{"calculate_binomial_probability": {"number_of_trials": [10], "number_of_successes": [7], "probability_of_success": [0.6]}}, {"calculate_binomial_probability": {"number_of_trials": [15], "number_of_successes": [10], "probability_of_success": [0.6]}}, {"calculate_binomial_probability": {"number_of_trials": [20], "number_of_successes": [15], "probability_of_success": [0.6]}}]}
{"id": "parallel_162", "ground_truth": [{"probability_of_event": {"success_outcomes": [4], "total_outcomes": [52], "format_as_ratio": [false, ""]}}, {"probability_of_event": {"success_outcomes": [13], "total_outcomes": [52], "format_as_ratio": [false, ""]}}, {"probability_of_event": {"success_outcomes": [26], "total_outcomes": [52], "format_as_ratio": [true]}}]}
{"id": "parallel_163", "ground_truth": [{"calc_binomial_prob": {"num_trials": [10], "num_success": [6], "prob_success": [0.6]}}, {"calc_binomial_prob": {"num_trials": [10], "num_success": [6], "prob_success": [0.5]}}, {"calc_binomial_prob": {"num_trials": [15], "num_success": [6], "prob_success": [0.5]}}]}
{"id": "parallel_164", "ground_truth": [{"chi_squared_test": {"table": [[45, 55, 35, 65]], "alpha": [0.05]}}, {"chi_squared_test": {"table": [[30, 70, 50, 50]], "alpha": [0.05]}}]}
{"id": "parallel_165", "ground_truth": [{"t_test": {"dataset_A": [[12, 15, 18, 20, 22, 25, 28, 30, 32, 35]], "dataset_B": [[14, 17, 19, 21, 23, 26, 29, 31, 33, 36]], "alpha": [0.05]}}, {"t_test": {"dataset_A": [[12, 15, 18, 20, 22, 25, 28, 30, 32, 35]], "dataset_B": [[14, 17, 19, 21, 23, 26, 29, 31, 33, 36]], "alpha": [0.01]}}]}
{"id": "parallel_166", "ground_truth": [{"predict_house_price": {"area": [2500], "rooms": [3], "year": [2000], "location": ["New York", "New York, NY", "New York City", "NYC", "NY"]}}, {"predict_house_price": {"area": [3000], "rooms": [3], "year": [2005], "location": ["Los Angeles", "Los Angeles, CA", "LA", "Los Angeles, CA", "CA"]}}, {"predict_house_price": {"area": [2000], "rooms": [2], "year": [1995], "location": ["Chicago"]}}]}
{"id": "parallel_167", "ground_truth": [{"linear_regression.get_r_squared": {"dataset_path": ["/user/home/<USER>/finance.csv"], "independent_variables": [["income", "age", "education"]], "dependent_variable": ["credit_score"]}}, {"linear_regression.get_r_squared": {"dataset_path": ["/user/home/<USER>/finance.csv"], "independent_variables": [["income", "age", "credit_score"]], "dependent_variable": ["education"]}}]}
{"id": "parallel_168", "ground_truth": [{"finance.calculate_quarterly_dividend_per_share": {"total_payout": [5000000], "outstanding_shares": [2000000]}}, {"finance.calculate_quarterly_dividend_per_share": {"total_payout": [6000000], "outstanding_shares": [2500000]}}, {"finance.calculate_quarterly_dividend_per_share": {"total_payout": [6000000], "outstanding_shares": [2000000]}}]}
{"id": "parallel_169", "ground_truth": [{"calculate_discounted_cash_flow": {"coupon_payment": [50], "period": [5], "discount_rate": [0.05], "face_value": [1000, ""]}}, {"calculate_discounted_cash_flow": {"coupon_payment": [60], "period": [7], "discount_rate": [0.04], "face_value": [1000, ""]}}]}
{"id": "parallel_170", "ground_truth": [{"calculate_compound_interest": {"principal": [5000], "rate": [0.025], "time": [2], "n": [4]}}, {"calculate_compound_interest": {"principal": [5000], "rate": [0.025], "time": [3], "n": [4]}}, {"calculate_compound_interest": {"principal": [5000], "rate": [0.025], "time": [5], "n": [4]}}]}
{"id": "parallel_171", "ground_truth": [{"calculate_return_on_equity": {"net_income": [1000000], "shareholder_equity": [5000000], "dividends_paid": [200000]}}, {"calculate_return_on_equity": {"net_income": [2000000], "shareholder_equity": [10000000], "dividends_paid": [0, ""]}}]}
{"id": "parallel_172", "ground_truth": [{"finance.predict_future_value": {"present_value": [5000], "annual_interest_rate": [0.05], "compounding_periods_per_year": [1, ""], "time_years": [10]}}, {"finance.predict_future_value": {"present_value": [7000], "annual_interest_rate": [0.04], "compounding_periods_per_year": [1, ""], "time_years": [15]}}]}
{"id": "parallel_173", "ground_truth": [{"investment.predictProfit": {"investment_amount": [5000], "annual_return": [0.07], "years": [5]}}, {"investment.predictProfit": {"investment_amount": [8000], "annual_return": [0.05], "years": [7]}}]}
{"id": "parallel_174", "ground_truth": [{"calculate_return_on_investment": {"purchase_price": [150], "sale_price": [180], "dividend": [20]}}, {"calculate_return_on_investment": {"purchase_price": [200], "sale_price": [210], "dividend": [30]}}, {"calculate_return_on_investment": {"purchase_price": [250], "sale_price": [300], "dividend": [40]}}]}
{"id": "parallel_175", "ground_truth": [{"portfolio_future_value": {"stock": ["AAPL"], "invested_amount": [5000], "expected_annual_return": [0.07], "years": [5]}}, {"portfolio_future_value": {"stock": ["MSFT"], "invested_amount": [8000], "expected_annual_return": [0.06], "years": [7]}}, {"portfolio_future_value": {"stock": ["AMZN"], "invested_amount": [10000], "expected_annual_return": [0.08], "years": [10]}}]}
{"id": "parallel_176", "ground_truth": [{"calculate_cagr": {"initial_value": [5000], "final_value": [7000], "period_in_years": [5]}}, {"calculate_cagr": {"initial_value": [8000], "final_value": [12000], "period_in_years": [3]}}]}
{"id": "parallel_177", "ground_truth": [{"get_metal_price": {"metal": ["gold"], "measure": ["ounce"]}}, {"get_metal_price": {"metal": ["silver"], "measure": ["ounce"]}}, {"get_metal_price": {"metal": ["platinum"], "measure": ["ounce"]}}, {"get_metal_price": {"metal": ["palladium"], "measure": ["ounce"]}}]}
{"id": "parallel_178", "ground_truth": [{"get_stock_price": {"company_name": ["Microsoft", "Apple"], "date": ["2022-01-01", "01/01/2022", "Jan.1,2022"], "exchange": ["NASDAQ", ""]}}, {"get_stock_price": {"company_name": ["Microsoft"], "date": ["2022-02-01", "02/01/2022", "Feb.1,2022"], "exchange": ["NASDAQ", ""]}}, {"get_stock_price": {"company_name": ["Apple"], "date": ["2022-01-01", "01/01/2022", "Jan.1,2022"], "exchange": ["NASDAQ", ""]}}, {"get_stock_price": {"company_name": ["Apple"], "date": ["2022-02-01", "02/01/2022", "Feb.1,2022"], "exchange": ["NASDAQ", ""]}}]}
{"id": "parallel_179", "ground_truth": [{"get_stock_price": {"company": ["AAPL"], "days": [10], "exchange": ["NASDAQ"]}}, {"get_stock_price": {"company": ["MSFT"], "days": [15], "exchange": ["NYSE", ""]}}]}
{"id": "parallel_180", "ground_truth": [{"stock_price": {"company": ["Microsoft"], "days": [30], "data_type": ["Open", ""]}}, {"stock_price": {"company": ["Microsoft"], "days": [30], "data_type": ["Close", ""]}}, {"stock_price": {"company": ["Microsoft"], "days": [30], "data_type": ["High", ""]}}, {"stock_price": {"company": ["Microsoft"], "days": [30], "data_type": ["Low", ""]}}, {"stock_price": {"company": ["Apple"], "days": [30], "data_type": ["Open", ""]}}, {"stock_price": {"company": ["Apple"], "days": [30], "data_type": ["Close", ""]}}, {"stock_price": {"company": ["Apple"], "days": [30], "data_type": ["High", ""]}}, {"stock_price": {"company": ["Apple"], "days": [30], "data_type": ["Low", ""]}}]}
{"id": "parallel_181", "ground_truth": [{"get_stock_prices": {"companies": [["Apple"]], "duration": ["1 week"]}}, {"get_stock_prices": {"companies": [["Microsoft"]], "duration": ["2 weeks"]}}, {"get_stock_prices": {"companies": [["Amazon"]], "duration": ["3 weeks"]}}, {"get_stock_prices": {"companies": [["Tesla"]], "duration": ["1 month"]}}]}
{"id": "parallel_182", "ground_truth": [{"finance.calculate_future_value": {"initial_investment": [5000], "rate_of_return": [0.07], "years": [10], "contribution": ["", 0]}}, {"finance.calculate_future_value": {"initial_investment": [3000], "rate_of_return": [0.06], "years": [10], "contribution": [200]}}]}
{"id": "parallel_183", "ground_truth": [{"math.hypot": {"x": [5], "y": [7], "z": ["", 0]}}, {"math.hypot": {"x": [10], "y": [15], "z": ["", 0]}}, {"math.hypot": {"x": [20], "y": [25], "z": ["", 0]}}]}
{"id": "parallel_184", "ground_truth": [{"algebra.quadratic_roots": {"a": [3], "b": [7], "c": [2]}}, {"algebra.quadratic_roots": {"a": [5], "b": [-4], "c": [1]}}]}
{"id": "parallel_185", "ground_truth": [{"estimate_population": {"species": ["Bengal Tigers", "Bengal Tiger"], "country": ["India"], "year": [2021]}}, {"estimate_population": {"species": ["African Elephants"], "country": ["Kenya"], "year": [2021]}}, {"estimate_population": {"species": ["Bengal Tigers", "Bengal Tiger"], "country": ["India"], "year": [""]}}, {"estimate_population": {"species": ["African Elephants"], "country": ["Kenya"], "year": [2023]}}]}
{"id": "parallel_186", "ground_truth": [{"calculate_emission_savings": {"energy_type": ["solar"], "usage_duration": [12], "region": ["Midwest", "Midwest region"]}}, {"calculate_emission_savings": {"energy_type": ["wind"], "usage_duration": [8], "region": ["Midwest", "Midwest region"]}}]}
{"id": "parallel_187", "ground_truth": [{"get_air_quality": {"location": ["New York City", "NYC"], "detail": [true], "historical": ["2023-05-05"]}}, {"get_air_quality": {"location": ["New York City", "NYC"], "detail": [true], "historical": ["2023-05-04"]}}, {"get_air_quality": {"location": ["New York City", "NYC"], "detail": [true], "historical": ["2023-05-03"]}}]}
{"id": "parallel_188", "ground_truth": [{"get_traffic_info": {"start_location": ["New York", "New York, NY", "NYC"], "end_location": ["Los Angeles", "Los Angeles, CA", "LA"], "mode": ["driving", ""]}}, {"get_traffic_info": {"start_location": ["Los Angeles", "Los Angeles, CA", "LA"], "end_location": ["San Francisco", "SF"], "mode": ["bicycling"]}}, {"get_traffic_info": {"start_location": ["San Francisco", "SF"], "end_location": ["New York", "New York, NY", "NYC"], "mode": ["transit"]}}]}
{"id": "parallel_189", "ground_truth": [{"parks.find_nearby": {"location": ["New York, USA", "NY, USA", "New York City, USA", "NYC, USA"], "amenities": [["Tennis Court", "Picnic Area"]]}}, {"parks.find_nearby": {"location": ["Los Angeles, USA", "LA, USA"], "amenities": [["Playground", "Running Track"]]}}, {"parks.find_nearby": {"location": ["Chicago, USA"], "amenities": [["Tennis Court", "Playground"]]}}]}
{"id": "parallel_190", "ground_truth": [{"calculate_shortest_distance": {"start_location": ["New York City", "NYC"], "end_location": ["Los Angeles", "Los Angeles, CA", "LA"], "route_preference": ["Shortest"]}}, {"calculate_shortest_distance": {"start_location": ["Los Angeles", "Los Angeles, CA", "LA"], "end_location": ["Miami"], "route_preference": ["Shortest"]}}, {"calculate_shortest_distance": {"start_location": ["New York City", "NYC"], "end_location": ["Los Angeles", "Los Angeles, CA", "LA"], "route_preference": ["Scenic"]}}, {"calculate_shortest_distance": {"start_location": ["Los Angeles", "Los Angeles, CA", "LA"], "end_location": ["Miami"], "route_preference": ["Scenic"]}}]}
{"id": "parallel_191", "ground_truth": [{"public_library.find_nearby": {"location": ["New York, NY", "NY"], "facilities": [["Reading Room", "Fiction"]]}}, {"public_library.find_nearby": {"location": ["Los Angeles, CA", "LA"], "facilities": [["Wi-Fi", "Children Section"]]}}, {"public_library.find_nearby": {"location": ["Chicago, IL", "Chi"], "facilities": [["Cafe", "Reading Room"]]}}]}
{"id": "parallel_192", "ground_truth": [{"get_news": {"topic": ["Climate Change"], "quantity": [5], "region": ["Europe", "EU"]}}, {"get_news": {"topic": ["Artificial Intelligence"], "quantity": [5], "region": ["Europe", "EU"]}}]}
{"id": "parallel_193", "ground_truth": [{"send_email": {"to": ["<EMAIL>"], "subject": ["Project Update"], "body": ["Dear John, The project is progressing as planned and we are on track to meet our deadlines. Best, Alex"], "cc": ["<EMAIL>"], "bcc": ["<EMAIL>"]}}, {"send_email": {"to": ["<EMAIL>"], "subject": ["Meeting Reminder"], "body": ["Dear Jane, This is a reminder for our meeting scheduled for tomorrow at 10 AM. Best, Alex"], "cc": ["<EMAIL>"], "bcc": ["<EMAIL>"]}}]}
{"id": "parallel_194", "ground_truth": [{"event_finder.find_upcoming": {"location": ["Los Angeles, CA", "LA"], "genre": ["jazz"], "days_ahead": [14]}}, {"event_finder.find_upcoming": {"location": ["Chicago, IL"], "genre": ["rock"], "days_ahead": [10]}}, {"event_finder.find_upcoming": {"location": ["Boston, MA"], "genre": ["classical music", "classical"], "days_ahead": [7, ""]}}]}
{"id": "parallel_195", "ground_truth": [{"movie_details.brief": {"title": ["Inception"], "extra_info": [true]}}, {"movie_details.brief": {"title": ["The Dark Knight"], "extra_info": [true]}}]}
{"id": "parallel_196", "ground_truth": [{"get_lawsuit_details": {"case_number": ["12345"], "court_location": ["New York Supreme Court", "NY Supreme Court"], "with_verdict": [true]}}, {"get_lawsuit_details": {"case_number": ["67890"], "court_location": ["Los Angeles Superior Court", "LA Superior Court"], "with_verdict": [false, ""]}}]}
{"id": "parallel_197", "ground_truth": [{"lawsuit_info": {"case_number": ["12345ABC"], "year": [2018], "location": ["New York", "New York, NY", "NY", ""]}}, {"lawsuit_info": {"case_number": ["67890XYZ"], "year": [2019], "location": ["California", "CA"]}}]}
{"id": "parallel_198", "ground_truth": [{"lawsuit_search": {"entity": ["Google"], "county": ["Santa Clara"], "state": ["California", "CA", ""]}}, {"lawsuit_search": {"entity": ["Facebook"], "county": ["San Mateo"], "state": ["California", "CA", ""]}}]}
{"id": "parallel_199", "ground_truth": [{"get_current_weather": {"location": ["New York", "New York, NY", "New York City", "NYC"], "include_temperature": [true, ""], "include_humidity": [true, ""]}}, {"get_current_weather": {"location": ["Los Angeles", "Los Angeles, CA", "LA"], "include_temperature": [true, ""], "include_humidity": [true, ""]}}, {"get_current_weather": {"location": ["London"], "include_temperature": [true, ""], "include_humidity": [true, ""]}}, {"get_current_weather": {"location": ["Tokyo"], "include_temperature": [true, ""], "include_humidity": [true, ""]}}]}