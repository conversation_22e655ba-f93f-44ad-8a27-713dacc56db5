{"id": "live_parallel_multiple_0-0-0", "ground_truth": [{"ChaFod": {"foodItem": ["Caesar salad"], "newIngredients": [""], "removeIngredients": ["anchovies", "anchovy"], "specialInstructions": [""]}}, {"ChaDri.change_drink": {"drink_id": ["123"], "new_preferences": [{"size": ["large"], "temperature": ["hot"], "sweetness_level": ["", "regular"], "milk_type": ["almond"], "special_instructions": [""]}]}}]}
{"id": "live_parallel_multiple_1-1-0", "ground_truth": [{"get_current_weather": {"location": ["Guangzhou, China"], "unit": ["metric"]}}, {"get_current_weather": {"location": ["Beijing, China"], "unit": ["metric"]}}]}
{"id": "live_parallel_multiple_2-2-0", "ground_truth": [{"ControlAppliance.execute": {"command": ["\uac70\uc2e4, \uc5d0\uc5b4\ucee8, \uc2e4\ud589"]}}, {"ControlAppliance.execute": {"command": ["\uce68\uc2e4, \uacf5\uae30\uccad\uc815\uae30, \uc911\uc9c0"]}}]}
{"id": "live_parallel_multiple_3-2-1", "ground_truth": [{"OpenWeatherMap.get_current_weather": {"location": ["Seoul, South Korea"], "units": ["", "metric"], "api_key": ["", "YOUR_API_KEY_HERE"]}}, {"ControlAppliance.execute": {"command": ["\uac70\uc2e4, \uc5d0\uc5b4\ucee8, \uc2e4\ud589"]}}, {"HNA_WQA.search": {"keyword": ["Imjin War", "the Imjin War"], "result_format": ["", "text"], "language": ["", "EN"], "max_results": ["", 10]}}]}
{"id": "live_parallel_multiple_4-3-0", "ground_truth": [{"get_news_report": {"location": ["Paris, France"], "category": ["Technology"], "language": ["en", ""]}}, {"get_news_report": {"location": ["Letterkenny, Ireland"], "category": ["Technology"], "language": ["en", ""]}}]}
{"id": "live_parallel_multiple_5-4-0", "ground_truth": [{"get_news_report": {"location": ["Paris, France"]}}, {"get_current_weather": {"location": ["Letterkenny, Ireland"], "unit": ["celsius"]}}]}
{"id": "live_parallel_multiple_6-5-0", "ground_truth": [{"get_interviewer_list": {"skill": ["Python"], "experience_level": ["", "Mid-Level"], "availability": ["", true]}}, {"get_interviewer_list": {"skill": ["Java"], "experience_level": ["", "Mid-Level"], "availability": ["", true]}}]}
{"id": "live_parallel_multiple_7-6-0", "ground_truth": [{"get_aws_pricing": {"memory": [4], "cpu": [2]}}, {"get_gcp_pricing": {"memory_gb": [4], "cpu_count": [2], "region": ["", "us-central1"], "usage_type": ["", "OnDemand"]}}]}
{"id": "live_parallel_multiple_8-7-0", "ground_truth": [{"clone_repo": {"repo_url": ["**************:zelarhq/nodejs-welcome.git", "https://github.com/zelarhq/nodejs-welcome.git"], "destination": ["", "."], "recursive": ["", false], "depth": ["", null]}}, {"analyse_repo_contents": {"directory_name": ["nodejs-welcome"]}}, {"create_a_docker_file": {"directory_name": ["nodejs-welcome"]}}, {"create_kubernetes_yaml_file": {"directory_name": ["nodejs-welcome"], "deployment_name": ["", null], "image_name": ["", "latest"], "replicas": ["", 1], "port": ["", 80], "service_type": ["", "ClusterIP"]}}, {"push_git_changes_to_github": {"directory_name": ["nodejs-welcome"], "commit_message": ["", "Update changes"], "branch_name": ["", "main"], "force_push": ["", false]}}]}
{"id": "live_parallel_multiple_9-8-0", "ground_truth": [{"search_engine.query": {"prompt": ["significant events, news articles, and developments that since October 2021"], "include_after_year": [true], "source": ["", "Google"]}}, {"generate_image": {"prompt": ["a digital painting of a masked woman with bright peacock feathers, complex and elegant, with green highlighted lines and complex patterns, reminiscent of cyberpunk Alphonse Mucha's style."], "resolution": ["", "1280x720"], "color_mode": ["", "RGB"], "image_quality": ["", 80]}}]}
{"id": "live_parallel_multiple_10-9-0", "ground_truth": [{"recall_memory_search": {"query": ["Shishir Birthday", "Birthday Shishir", "Shishir's Birthday", "when is shishir's birthday", "when shishir birthday is", "birthday"], "page": ["", 0], "request_heartbeat": ["", false]}}, {"send_message": {"message": ["Happy Birthday!"], "recipient": ["Shishir"], "urgent": ["", false]}}]}
{"id": "live_parallel_multiple_11-10-0", "ground_truth": [{"get_relevant_classes": {"search_string": ["CellResult"], "case_sensitive": ["", false], "include_subdirectories": [true]}}, {"get_signature": {"class_name": ["AbstractCellHandler"], "method_name": ["setCellValue"], "include_private": ["", false]}}, {"get_signature": {"class_name": ["AbstractCellHandler"], "method_name": ["getCellValue"], "include_private": ["", false]}}]}
{"id": "live_parallel_multiple_12-10-1", "ground_truth": [{"get_class_info": {"class_name": ["Weather"], "include_private": ["", false], "module_name": ["", null]}}, {"get_signature": {"class_name": ["Weather"], "method_name": ["get_weather"], "include_private": ["", false]}}]}
{"id": "live_parallel_multiple_13-11-0", "ground_truth": [{"math_gcd": {"a": [48], "b": [36]}}, {"estimate_derivative": {"function": ["lambda x: x**2"], "x": [5.0], "dx": ["", 0.0001], "method": ["", "central"]}}]}
{"id": "live_parallel_multiple_14-12-0", "ground_truth": [{"math_gcd": {"a": [48], "b": [36]}}, {"estimate_derivative": {"function": ["lambda x: x**2"], "x": [5.0], "dx": ["", 0.001], "method": ["", "central"]}}]}
{"id": "live_parallel_multiple_15-13-0", "ground_truth": [{"sum": {"a": [1], "b": [2]}}, {"getCurrentTime": {"timezone": ["Europe/Berlin"], "include_date": [true]}}]}
{"id": "live_parallel_multiple_16-14-0", "ground_truth": [{"get_area_of_square": {"size": [5.0]}}, {"get_current_weather": {"location": ["Queenstown, Singapore"], "unit": ["", "fahrenheit"]}}]}
{"id": "live_parallel_multiple_17-15-0", "ground_truth": [{"rotateImageAction": {"degrees": [20], "image_format": ["", "JPEG"], "keep_original": ["", true]}}, {"flipImageAction": {"flip_direction": ["horizontal"]}}]}
{"id": "live_parallel_multiple_18-16-0", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["San Diego, CA"], "date": ["2023-03-01"]}}, {"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["San Diego, CA"], "date": ["2023-03-01"]}}]}
{"id": "live_parallel_multiple_19-16-1", "ground_truth": [{"Events_3_FindEvents": {"event_type": ["Music"], "city": ["Los Angeles, CA"], "date": ["2023-03-10"]}}, {"Events_3_FindEvents": {"event_type": ["Theater"], "city": ["Los Angeles, CA"], "date": ["2023-03-10"]}}]}
{"id": "live_parallel_multiple_20-17-0", "ground_truth": [{"Buses_3_FindBus": {"from_city": ["Boston, MA"], "to_city": ["New York, NY"], "departure_date": ["2023-05-21"], "num_passengers": [2], "category": ["", "direct"]}}, {"Events_3_FindEvents": {"event_type": ["Music"], "city": ["New York, NY"], "date": ["2023-05-21"]}}]}
{"id": "live_parallel_multiple_21-18-0", "ground_truth": [{"Services_1_FindProvider": {"city": ["Sunnyvale, CA"], "is_unisex": ["", "dontcare"]}}, {"Services_1_BookAppointment": {"stylist_name": ["Elegant Styles"], "appointment_time": ["15:00"], "appointment_date": ["2023-05-10"]}}]}
{"id": "live_parallel_multiple_22-19-0", "ground_truth": [{"get_current_weather": {"location": ["Boston, MA"], "unit": ["", "fahrenheit"]}}, {"get_current_weather": {"location": ["San Francisco, CA"], "unit": ["", "fahrenheit"]}}]}
{"id": "live_parallel_multiple_23-20-0", "ground_truth": [{"user.mandates": {"name": ["parath"], "status": ["", "active"]}}, {"user.mandates": {"name": ["parath"], "status": ["pending"]}}, {"user.mandates": {"name": ["bhanu"], "status": ["", "active"]}}, {"user.mandates": {"name": ["bhanu"], "status": ["pending"]}}]}