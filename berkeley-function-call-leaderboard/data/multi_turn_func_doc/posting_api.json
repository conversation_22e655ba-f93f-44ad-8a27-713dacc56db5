{"name": "authenticate_twitter", "description": "This tool belongs to the TwitterAPI, which provides core functionality for posting tweets, retweeting, commenting, and following users on Twitter. Tool description: Authenticate a user with username and password.", "parameters": {"type": "dict", "properties": {"username": {"type": "string", "description": "Username of the user."}, "password": {"type": "string", "description": "Password of the user."}}, "required": ["username", "password"]}, "response": {"type": "dict", "properties": {"authentication_status": {"type": "boolean", "description": "True if authenticated, False otherwise."}}}}
{"name": "comment", "description": "This tool belongs to the TwitterAPI, which provides core functionality for posting tweets, retweeting, commenting, and following users on Twitter. Tool description: Comment on a tweet for the authenticated user.", "parameters": {"type": "dict", "properties": {"tweet_id": {"type": "integer", "description": "ID of the tweet to comment on."}, "comment_content": {"type": "string", "description": "Content of the comment."}}, "required": ["tweet_id", "comment_content"]}, "response": {"type": "dict", "properties": {"comment_status": {"type": "string", "description": "Status of the comment action."}}}}
{"name": "follow_user", "description": "This tool belongs to the TwitterAPI, which provides core functionality for posting tweets, retweeting, commenting, and following users on Twitter. Tool description: Follow a user for the authenticated user.", "parameters": {"type": "dict", "properties": {"username_to_follow": {"type": "string", "description": "Username of the user to follow."}}, "required": ["username_to_follow"]}, "response": {"type": "dict", "properties": {"follow_status": {"type": "boolean", "description": "True if followed, False if already following."}}}}
{"name": "get_tweet", "description": "This tool belongs to the TwitterAPI, which provides core functionality for posting tweets, retweeting, commenting, and following users on Twitter. Tool description: Retrieve a specific tweet.", "parameters": {"type": "dict", "properties": {"tweet_id": {"type": "integer", "description": "ID of the tweet to retrieve."}}, "required": ["tweet_id"]}, "response": {"type": "dict", "properties": {"id": {"type": "integer", "description": "ID of the retrieved tweet."}, "username": {"type": "string", "description": "Username of the tweet's author."}, "content": {"type": "string", "description": "Content of the tweet."}, "tags": {"type": "array", "description": "List of tags associated with the tweet.", "items": {"type": "string"}}, "mentions": {"type": "array", "description": "List of users mentioned in the tweet.", "items": {"type": "string"}}}}}
{"name": "get_tweet_comments", "description": "This tool belongs to the TwitterAPI, which provides core functionality for posting tweets, retweeting, commenting, and following users on Twitter. Tool description: Retrieve all comments for a specific tweet.", "parameters": {"type": "dict", "properties": {"tweet_id": {"type": "integer", "description": "ID of the tweet to retrieve comments for."}}, "required": ["tweet_id"]}, "response": {"type": "dict", "properties": {"comments": {"type": "array", "description": "List of dictionaries, each containing comment information.", "items": {"type": "dict", "properties": {"username": {"type": "string", "description": "Username of the commenter."}, "content": {"type": "string", "description": "Content of the comment."}}}}}}}
{"name": "get_user_stats", "description": "This tool belongs to the TwitterAPI, which provides core functionality for posting tweets, retweeting, commenting, and following users on Twitter. Tool description: Get statistics for a specific user.", "parameters": {"type": "dict", "properties": {"username": {"type": "string", "description": "Username of the user to get statistics for."}}, "required": ["username"]}, "response": {"type": "dict", "properties": {"tweet_count": {"type": "integer", "description": "Number of tweets posted by the user."}, "following_count": {"type": "integer", "description": "Number of users the specified user is following."}, "retweet_count": {"type": "integer", "description": "Number of retweets made by the user."}}}}
{"name": "get_user_tweets", "description": "This tool belongs to the TwitterAPI, which provides core functionality for posting tweets, retweeting, commenting, and following users on Twitter. Tool description: Retrieve all tweets from a specific user.", "parameters": {"type": "dict", "properties": {"username": {"type": "string", "description": "Username of the user whose tweets to retrieve."}}, "required": ["username"]}, "response": {"type": "dict", "properties": {"user_tweets": {"type": "array", "description": "List of dictionaries, each containing tweet information.", "items": {"type": "dict", "properties": {"id": {"type": "integer", "description": "ID of the retrieved tweet."}, "username": {"type": "string", "description": "Username of the tweet's author."}, "content": {"type": "string", "description": "Content of the tweet."}, "tags": {"type": "array", "description": "List of tags associated with the tweet.", "items": {"type": "string"}}, "mentions": {"type": "array", "description": "List of users mentioned in the tweet.", "items": {"type": "string"}}}}}}}}
{"name": "list_all_following", "description": "This tool belongs to the TwitterAPI, which provides core functionality for posting tweets, retweeting, commenting, and following users on Twitter. Tool description: List all users that the authenticated user is following.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"following_list": {"type": "array", "description": "List of all users that the authenticated user is following.", "items": {"type": "string"}}}}}
{"name": "mention", "description": "This tool belongs to the TwitterAPI, which provides core functionality for posting tweets, retweeting, commenting, and following users on Twitter. Tool description: Mention specified users in a tweet.", "parameters": {"type": "dict", "properties": {"tweet_id": {"type": "integer", "description": "ID of the tweet where users are mentioned."}, "mentioned_usernames": {"type": "array", "items": {"type": "string"}, "description": "List of usernames to be mentioned."}}, "required": ["tweet_id", "mentioned_usernames"]}, "response": {"type": "dict", "properties": {"mention_status": {"type": "string", "description": "Status of the mention action."}}}}
{"name": "post_tweet", "description": "This tool belongs to the TwitterAPI, which provides core functionality for posting tweets, retweeting, commenting, and following users on Twitter. Tool description: Post a tweet for the authenticated user.", "parameters": {"type": "dict", "properties": {"content": {"type": "string", "description": "Content of the tweet."}, "tags": {"type": "array", "items": {"type": "string"}, "description": "List of tags for the tweet. Tag name should start with #. This is only relevant if the user wants to add tags to the tweet.", "default": []}, "mentions": {"type": "array", "items": {"type": "string"}, "description": "List of users mentioned in the tweet. Mention name should start with @. This is only relevant if the user wants to add mentions to the tweet.", "default": []}}, "required": ["content"]}, "response": {"type": "dict", "properties": {"id": {"type": "integer", "description": "ID of the posted tweet."}, "username": {"type": "string", "description": "Username of the poster."}, "content": {"type": "string", "description": "Content of the tweet."}, "tags": {"type": "array", "description": "List of tags associated with the tweet.", "items": {"type": "string"}}, "mentions": {"type": "array", "description": "List of users mentioned in the tweet.", "items": {"type": "string"}}}}}
{"name": "posting_get_login_status", "description": "This tool belongs to the TwitterAPI, which provides core functionality for posting tweets, retweeting, commenting, and following users on Twitter. Tool description: Get the login status of the current user.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"login_status": {"type": "boolean", "description": "True if the current user is logged in, False otherwise."}}}}
{"name": "retweet", "description": "This tool belongs to the TwitterAPI, which provides core functionality for posting tweets, retweeting, commenting, and following users on Twitter. Tool description: Retweet a tweet for the authenticated user.", "parameters": {"type": "dict", "properties": {"tweet_id": {"type": "integer", "description": "ID of the tweet to retweet."}}, "required": ["tweet_id"]}, "response": {"type": "dict", "properties": {"retweet_status": {"type": "string", "description": "Status of the retweet action."}}}}
{"name": "search_tweets", "description": "This tool belongs to the TwitterAPI, which provides core functionality for posting tweets, retweeting, commenting, and following users on Twitter. Tool description: Search for tweets containing a specific keyword.", "parameters": {"type": "dict", "properties": {"keyword": {"type": "string", "description": "Keyword to search for in the content of the tweets."}}, "required": ["keyword"]}, "response": {"type": "dict", "properties": {"matching_tweets": {"type": "array", "description": "List of dictionaries, each containing tweet information.", "items": {"type": "dict", "properties": {"id": {"type": "integer", "description": "ID of the retrieved tweet."}, "username": {"type": "string", "description": "Username of the tweet's author."}, "content": {"type": "string", "description": "Content of the tweet."}, "tags": {"type": "array", "description": "List of tags associated with the tweet.", "items": {"type": "string"}}, "mentions": {"type": "array", "description": "List of users mentioned in the tweet.", "items": {"type": "string"}}}}}}}}
{"name": "unfollow_user", "description": "This tool belongs to the TwitterAPI, which provides core functionality for posting tweets, retweeting, commenting, and following users on Twitter. Tool description: Unfollow a user for the authenticated user.", "parameters": {"type": "dict", "properties": {"username_to_unfollow": {"type": "string", "description": "Username of the user to unfollow."}}, "required": ["username_to_unfollow"]}, "response": {"type": "dict", "properties": {"unfollow_status": {"type": "boolean", "description": "True if unfollowed, False if not following."}}}}
