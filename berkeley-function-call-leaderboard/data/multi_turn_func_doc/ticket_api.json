{"name": "close_ticket", "description": "This tool belongs to the ticketing system that is part of a company, which allows users to create, view, and manage support business tickets. Tool description: Close a ticket.", "parameters": {"type": "dict", "properties": {"ticket_id": {"type": "integer", "description": "ID of the ticket to be closed. "}}, "required": ["ticket_id"]}, "response": {"type": "dict", "properties": {"status": {"type": "string", "description": "Status of the close operation."}}}}
{"name": "create_ticket", "description": "This tool belongs to the ticketing system that is part of a company, which allows users to create, view, and manage support business tickets. Tool description: Create a ticket in the system and queue it.", "parameters": {"type": "dict", "properties": {"title": {"type": "string", "description": "Title of the ticket."}, "description": {"type": "string", "description": "Description of the ticket. Defaults to an empty string.", "default": ""}, "priority": {"type": "integer", "description": "Priority of the ticket, from 1 to 5. Defaults to 1. 5 is the highest priority. ", "default": 1}}, "required": ["title"]}, "response": {"type": "dict", "properties": {"id": {"type": "integer", "description": "Unique identifier of the ticket."}, "title": {"type": "string", "description": "Title of the ticket."}, "description": {"type": "string", "description": "Description of the ticket."}, "status": {"type": "string", "description": "Current status of the ticket."}, "priority": {"type": "integer", "description": "Priority level of the ticket."}}}}
{"name": "edit_ticket", "description": "This tool belongs to the ticketing system that is part of a company, which allows users to create, view, and manage support business tickets. Tool description: Modify the details of an existing ticket.", "parameters": {"type": "dict", "properties": {"ticket_id": {"type": "integer", "description": "ID of the ticket to be changed."}, "updates": {"type": "dict", "description": "Dictionary containing the fields to be updated. - title (str) : [Optional] New title for the ticket. ", "properties": {"description": {"type": "string", "description": "New description for the ticket."}, "status": {"type": "string", "description": "New status for the ticket."}, "priority": {"type": "integer", "description": "New priority for the ticket."}}}}, "required": ["ticket_id", "updates"]}, "response": {"type": "dict", "properties": {"status": {"type": "string", "description": "Status of the update operation."}}}}
{"name": "get_ticket", "description": "This tool belongs to the ticketing system that is part of a company, which allows users to create, view, and manage support business tickets. Tool description: Get a specific ticket by its ID.", "parameters": {"type": "dict", "properties": {"ticket_id": {"type": "integer", "description": "ID of the ticket to retrieve. "}}, "required": ["ticket_id"]}, "response": {"type": "dict", "properties": {"id": {"type": "integer", "description": "Unique identifier of the ticket."}, "title": {"type": "string", "description": "Title of the ticket."}, "description": {"type": "string", "description": "Description of the ticket."}, "status": {"type": "string", "description": "Current status of the ticket."}, "priority": {"type": "integer", "description": "Priority level of the ticket."}, "created_by": {"type": "string", "description": "Username of the ticket creator."}}}}
{"name": "get_user_tickets", "description": "This tool belongs to the ticketing system that is part of a company, which allows users to create, view, and manage support business tickets. Tool description: Get all tickets created by the current user, optionally filtered by status.", "parameters": {"type": "dict", "properties": {"status": {"type": "string", "description": "Status to filter tickets by. If None, return all tickets. ", "default": "None"}}, "required": []}, "response": {"type": "dict", "properties": {"id": {"type": "integer", "description": "Unique identifier of the ticket."}, "title": {"type": "string", "description": "Title of the ticket."}, "description": {"type": "string", "description": "Description of the ticket."}, "status": {"type": "string", "description": "Current status of the ticket."}, "priority": {"type": "integer", "description": "Priority level of the ticket."}, "created_by": {"type": "string", "description": "Username of the ticket"}}}}
{"name": "logout", "description": "This tool belongs to the ticketing system that is part of a company, which allows users to create, view, and manage support business tickets. Tool description: Log out the current user.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"success": {"type": "boolean", "description": "True if logout was successful, False otherwise."}}}}
{"name": "resolve_ticket", "description": "This tool belongs to the ticketing system that is part of a company, which allows users to create, view, and manage support business tickets. Tool description: Resolve a ticket with a resolution.", "parameters": {"type": "dict", "properties": {"ticket_id": {"type": "integer", "description": "ID of the ticket to be resolved."}, "resolution": {"type": "string", "description": "Resolution details for the ticket. "}}, "required": ["ticket_id", "resolution"]}, "response": {"type": "dict", "properties": {"status": {"type": "string", "description": "Status of the resolve operation."}}}}
{"name": "ticket_get_login_status", "description": "This tool belongs to the ticketing system that is part of a company, which allows users to create, view, and manage support business tickets. Tool description: Get the username of the currently authenticated user.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"username": {"type": "boolean", "description": "True if a user is logged in, False otherwise."}}}}
{"name": "ticket_login", "description": "This tool belongs to the ticketing system that is part of a company, which allows users to create, view, and manage support business tickets. Tool description: Authenticate a user for ticket system.", "parameters": {"type": "dict", "properties": {"username": {"type": "string", "description": "Username of the user."}, "password": {"type": "string", "description": "Password of the user. "}}, "required": ["username", "password"]}, "response": {"type": "dict", "properties": {"success": {"type": "boolean", "description": "True if login was successful, False otherwise."}}}}
