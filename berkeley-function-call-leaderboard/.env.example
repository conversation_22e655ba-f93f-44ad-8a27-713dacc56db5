# Provide the API key for the model(s) you intend to use
OPENAI_API_KEY=sk-XXXXXX
MISTRAL_API_KEY=
FIREWORKS_API_KEY=
ANTHROPIC_API_KEY=
NVIDIA_API_KEY=nvapi-XXXXXX
DEEPSEEK_API_KEY=sk-XXXXXX
YI_API_KEY=
COHERE_API_KEY=
GROK_API_KEY=xai-XXXXXX
GOGOAGENT_API_KEY=
WRITER_API_KEY=
MINING_API_KEY="sk-XXXXXX"

# We use Vertex AI to inference Google Gemini models
VERTEX_AI_PROJECT_ID=
VERTEX_AI_LOCATION=

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=

DATABRICKS_API_KEY=
DATABRICKS_AZURE_ENDPOINT_URL=

# [OPTIONAL] For inference via Novita AI endpoint
NOVITA_API_KEY=sk-XXXXXX

# [OPTIONAL] For local vllm/sglang server configuration
# Defaults to localhost port 1053 if not provided
VLLM_ENDPOINT=localhost
VLLM_PORT=1053

# [OPTIONAL] Required for WandB to log the generated .csv in the format 'entity:project
WANDB_BFCL_PROJECT=ENTITY:PROJECT